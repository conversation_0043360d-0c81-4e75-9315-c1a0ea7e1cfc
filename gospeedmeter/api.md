<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# gospeedmeter

```go
import "github.com/real-rm/gospeedmeter"
```

## Index

- [Constants](<#constants>)
- [type SpeedMeter](<#SpeedMeter>)
  - [func NewSpeedMeter\(options SpeedMeterOptions\) \*SpeedMeter](<#NewSpeedMeter>)
  - [func \(sm \*SpeedMeter\) Check\(name string, value float64\)](<#SpeedMeter.Check>)
  - [func \(sm \*SpeedMeter\) Estimate\(name string, targetValue float64, unit Unit\) float64](<#SpeedMeter.Estimate>)
  - [func \(sm \*SpeedMeter\) GetCounters\(\) map\[string\]float64](<#SpeedMeter.GetCounters>)
  - [func \(sm \*SpeedMeter\) GetSpeed\(unit Unit\) map\[string\]float64](<#SpeedMeter.GetSpeed>)
  - [func \(sm \*SpeedMeter\) Reset\(values ...map\[string\]float64\)](<#SpeedMeter.Reset>)
  - [func \(sm \*SpeedMeter\) ToString\(unit Unit, toBeEstimated map\[string\]float64\) string](<#SpeedMeter.ToString>)
- [type SpeedMeterOptions](<#SpeedMeterOptions>)
- [type Unit](<#Unit>)


## Constants

<a name="AMOUNT_UNIT"></a>

```go
const AMOUNT_UNIT = "KMBT"
```

<a name="SpeedMeter"></a>
## type [SpeedMeter](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L34-L41>)

SpeedMeter handles speed calculations for multiple meters

```go
type SpeedMeter struct {
    // contains filtered or unexported fields
}
```

<a name="NewSpeedMeter"></a>
### func [NewSpeedMeter](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L51>)

```go
func NewSpeedMeter(options SpeedMeterOptions) *SpeedMeter
```

NewSpeedMeter creates a new SpeedMeter instance

<a name="SpeedMeter.Check"></a>
### func \(\*SpeedMeter\) [Check](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L87>)

```go
func (sm *SpeedMeter) Check(name string, value float64)
```

Check updates a single meter value and triggers callback if needed

<a name="SpeedMeter.Estimate"></a>
### func \(\*SpeedMeter\) [Estimate](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L138>)

```go
func (sm *SpeedMeter) Estimate(name string, targetValue float64, unit Unit) float64
```

Estimate calculates estimated time to reach target value for a single meter

<a name="SpeedMeter.GetCounters"></a>
### func \(\*SpeedMeter\) [GetCounters](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L148>)

```go
func (sm *SpeedMeter) GetCounters() map[string]float64
```

GetCounters returns current counter values

<a name="SpeedMeter.GetSpeed"></a>
### func \(\*SpeedMeter\) [GetSpeed](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L100>)

```go
func (sm *SpeedMeter) GetSpeed(unit Unit) map[string]float64
```

GetSpeed returns the current speed for all meters in the specified unit

<a name="SpeedMeter.Reset"></a>
### func \(\*SpeedMeter\) [Reset](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L77>)

```go
func (sm *SpeedMeter) Reset(values ...map[string]float64)
```

Reset resets the speed meter with optional initial values

<a name="SpeedMeter.ToString"></a>
### func \(\*SpeedMeter\) [ToString](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L119>)

```go
func (sm *SpeedMeter) ToString(unit Unit, toBeEstimated map[string]float64) string
```

ToString returns a formatted string representation of the speed meter

<a name="SpeedMeterOptions"></a>
## type [SpeedMeterOptions](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L44-L48>)

SpeedMeterOptions defines the configuration options for SpeedMeter

```go
type SpeedMeterOptions struct {
    Values               map[string]float64
    IntervalCallback     func(*SpeedMeter)
    IntervalTriggerCount int
}
```

<a name="Unit"></a>
## type [Unit](<https://github.com/real-rm/gospeedmeter/blob/main/speed_meter.go#L13>)

Unit represents time units for speed calculation

```go
type Unit string
```

<a name="UnitMS"></a>

```go
const (
    UnitMS Unit = "ms"
    UnitS  Unit = "s"
    UnitM  Unit = "m"
    UnitH  Unit = "h"
    UnitD  Unit = "d"
)
```

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)
