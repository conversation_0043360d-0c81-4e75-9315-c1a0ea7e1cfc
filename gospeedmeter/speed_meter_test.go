package gospeedmeter

import (
	"strings"
	"testing"
	"time"
)

func TestNewSpeedMeter(t *testing.T) {
	options := SpeedMeterOptions{
		Values: map[string]float64{"meter1": 10},
	}
	sm := NewSpeedMeter(options)
	if sm == nil {
		t.<PERSON>rror("Expected non-nil SpeedMeter")
	}
	if len(sm.GetCounters()) != 1 {
		t.<PERSON><PERSON>("Expected 1 counter")
	}
}

func TestSpeedMeterCheck(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// Test initial check
	sm.Check("meter1", 13)
	sm.Check("meter2", 200)
	counters := sm.GetCounters()
	if counters["meter1"] != 13 || counters["meter2"] != 200 {
		t.Error("Check values not properly updated")
	}

	// Test second check
	sm.Check("meter1", 10)
	sm.Check("meter3", 10)
	counters = sm.GetCounters()
	if counters["meter1"] != 23 || counters["meter2"] != 200 || counters["meter3"] != 10 {
		t.Error("Check values not properly accumulated")
	}
}

func TestSpeedMeterGetSpeed(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// Add some values
	sm.Check("meter1", 100)

	// Wait a bit to ensure time difference
	time.Sleep(100 * time.Millisecond)

	// Get speed in different units
	speed := sm.GetSpeed(UnitS)
	if speed["meter1"] < 900 || speed["meter1"] > 1100 {
		t.Errorf("Speed should be approximately 1000/s, got %f", speed["meter1"])
	}
}

func TestSpeedMeterReset(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// Add initial values
	sm.Check("meter1", 100)

	// Reset with values
	sm.Reset(map[string]float64{"meter1": 50})
	counters := sm.GetCounters()
	if counters["meter1"] != 50 {
		t.Error("Reset not working properly")
	}

	// Reset without values
	sm.Reset()
	counters = sm.GetCounters()
	if len(counters) != 0 {
		t.Error("Reset should clear counters")
	}
}

func TestSpeedMeterEstimate(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// Add some values
	sm.Check("meter1", 100)

	// Wait a bit
	time.Sleep(100 * time.Millisecond)

	// Test estimation
	estimate := sm.Estimate("meter1", 1000, UnitS)
	expectedEstimate := 0.9 // estimate should be 9.0
	if estimate < expectedEstimate*0.8 || estimate > expectedEstimate*1.2 {
		t.Errorf("Estimate should be approximately %f, got %f", expectedEstimate, estimate)
	}
}

func TestSpeedMeterToString(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// Add some values
	sm.Check("meter1", 100)

	// Test string representation
	str := sm.ToString(UnitS, nil)
	if str == "" {
		t.Error("String representation should not be empty")
	}

	// Test string with estimates
	str = sm.ToString(UnitS, map[string]float64{"meter1": 1000})
	if str == "" {
		t.Error("String representation with estimates should not be empty")
	}
}

func TestSpeedMeterIntervalCallback(t *testing.T) {
	callbackCalled := false
	callback := func(sm *SpeedMeter) {
		callbackCalled = true
	}

	options := SpeedMeterOptions{
		IntervalTriggerCount: 2,
		IntervalCallback:     callback,
	}
	sm := NewSpeedMeter(options)

	// Add values to trigger callback
	sm.Check("meter1", 1)
	sm.Check("meter1", 1)

	if !callbackCalled {
		t.Error("Interval callback should have been called")
	}
}

func TestSpeedMeterConcurrency(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// Test concurrent access
	done := make(chan bool)
	for i := 0; i < 10; i++ {
		go func() {
			sm.Check("meter1", 1)
			done <- true
		}()
	}

	// Wait for all goroutines to complete
	for i := 0; i < 10; i++ {
		<-done
	}

	counters := sm.GetCounters()
	if counters["meter1"] != 10 {
		t.Error("Concurrent access not handled properly")
	}
}

func TestSpeedMeterToStringSorting(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// 添加不同数量的计数器值来测试按名称排序
	sm.Check("meter_c", 100) // 名称中间
	sm.Check("meter_a", 500) // 名称最前
	sm.Check("meter_b", 50)  // 名称中间

	// 获取字符串输出
	result := sm.ToString(UnitS, nil)

	// 验证输出不为空
	if result == "" {
		t.Error("ToString result should not be empty")
	}

	// 验证输出包含所有 meter 名称
	// 由于排序是按名称升序，meter_a 应该在最前面，不管计数器值大小
	t.Logf("Name sorting output: %s", result)

	// 验证排序：meter_a 应该在 meter_b 之前，meter_b 应该在 meter_c 之前
	// 通过检查字符串中名称出现的位置来验证排序
	posA := strings.Index(result, "meter_a")
	posB := strings.Index(result, "meter_b")
	posC := strings.Index(result, "meter_c")

	if posA == -1 || posB == -1 || posC == -1 {
		t.Error("All meter names should be present in the output")
	}

	if posA > posB || posB > posC {
		t.Error("Meters should be sorted by name: meter_a < meter_b < meter_c")
	}
}

func TestSpeedMeterToStringSortingWithDifferentCounters(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// 添加不同计数器值的 meter 来测试纯名称排序（忽略计数器值）
	sm.Check("zebra", 100) // 名称最后，中等数量
	sm.Check("alpha", 50)  // 名称最前，最小数量
	sm.Check("beta", 200)  // 名称中间，最大数量
	sm.Check("gamma", 75)  // 名称中间，中等数量

	// 获取字符串输出
	result := sm.ToString(UnitS, nil)

	// 验证输出不为空
	if result == "" {
		t.Error("ToString result should not be empty")
	}

	t.Logf("Name-only sorting output: %s", result)

	// 验证排序逻辑：无论计数器值如何，都应该按名称升序排列
	// alpha < beta < gamma < zebra
	posAlpha := strings.Index(result, "alpha")
	posBeta := strings.Index(result, "beta")
	posGamma := strings.Index(result, "gamma")
	posZebra := strings.Index(result, "zebra")

	if posAlpha == -1 || posBeta == -1 || posGamma == -1 || posZebra == -1 {
		t.Error("All meter names should be present in the output")
	}

	if posAlpha > posBeta || posBeta > posGamma || posGamma > posZebra {
		t.Error("Meters should be sorted by name only: alpha < beta < gamma < zebra")
	}
}

// TestNewSpeedMeterDefaultCallback 测试默认回调函数的覆盖率
func TestNewSpeedMeterDefaultCallback(t *testing.T) {
	// 测试使用默认选项创建 SpeedMeter
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// 验证默认值设置正确
	if sm == nil {
		t.Error("Expected non-nil SpeedMeter")
	}

	// 测试默认的 intervalTriggerCount
	// 通过多次调用 Check 来触发默认回调
	for i := 0; i < 1000; i++ {
		sm.Check("test", 1)
	}

	// 验证计数器正确累加
	counters := sm.GetCounters()
	if counters["test"] != 1000 {
		t.Errorf("Expected counter to be 1000, got %f", counters["test"])
	}
}

// TestGetSpeedInvalidUnit 测试无效单位的处理
func TestGetSpeedInvalidUnit(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})
	sm.Check("meter1", 100)

	// 等待一段时间
	time.Sleep(10 * time.Millisecond)

	// 测试无效单位，应该使用默认的秒单位
	speed := sm.GetSpeed("invalid_unit")

	if len(speed) == 0 {
		t.Error("Speed map should not be empty")
	}

	if speed["meter1"] <= 0 {
		t.Error("Speed should be positive for invalid unit (defaults to seconds)")
	}
}

// TestEstimateEdgeCases 测试 Estimate 函数的边界情况
func TestEstimateEdgeCases(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// 测试速度为0的情况
	sm.Check("meter1", 0)
	time.Sleep(10 * time.Millisecond)

	estimate := sm.Estimate("meter1", 100, UnitS)
	if estimate != 0 {
		t.Errorf("Estimate should be 0 when speed is 0, got %f", estimate)
	}

	// 测试目标值小于当前值的情况
	sm.Check("meter2", 200)
	time.Sleep(10 * time.Millisecond)

	estimate = sm.Estimate("meter2", 100, UnitS) // 目标值小于当前值200
	if estimate != 0 {
		t.Errorf("Estimate should be 0 when target is less than current, got %f", estimate)
	}

	// 测试不存在的 meter
	estimate = sm.Estimate("nonexistent", 100, UnitS)
	if estimate != 0 {
		t.Errorf("Estimate should be 0 for nonexistent meter, got %f", estimate)
	}
}

// TestToStringLargeCountersNoScientificNotation 测试大数值计数器不显示科学计数法
func TestToStringLargeCountersNoScientificNotation(t *testing.T) {
	sm := NewSpeedMeter(SpeedMeterOptions{})

	// 添加大数值的计数器，这些值在修复前会显示为科学计数法
	sm.Check("writeSpeedMBps_TRB", 27603798.913692053) // 约 2.76e+07
	sm.Check("downloadMedia", 1862635.0)               // 约 1.86e+06
	sm.Check("smallValue", 123.45)                     // 小数值作为对比

	// 等待一段时间以确保有速度计算
	time.Sleep(10 * time.Millisecond)

	// 获取字符串输出
	result := sm.ToString(UnitM, nil)

	// 验证输出不为空
	if result == "" {
		t.Error("ToString result should not be empty")
	}

	t.Logf("Large counters output: %s", result)

	// 验证输出不包含科学计数法格式（不应该包含 'e+' 或 'E+'）
	if strings.Contains(result, "e+") || strings.Contains(result, "E+") {
		t.Errorf("Output should not contain scientific notation, got: %s", result)
	}

	// 验证大数值被正确格式化为 K/M/B/T 单位
	// writeSpeedMBps_TRB 的计数器应该显示为类似 "27.60M" 的格式
	if !strings.Contains(result, "writeSpeedMBps_TRB(") {
		t.Error("Output should contain writeSpeedMBps_TRB meter")
	}

	// downloadMedia 的计数器应该显示为类似 "1.862M" 的格式
	if !strings.Contains(result, "downloadMedia(") {
		t.Error("Output should contain downloadMedia meter")
	}

	// 验证格式化后的计数器包含单位后缀（K, M, B, T）
	// 对于大数值，应该有 M 单位
	if !strings.Contains(result, "M)") {
		t.Error("Large counters should be formatted with M unit suffix")
	}

	// 验证小数值仍然正常显示
	if !strings.Contains(result, "smallValue(") {
		t.Error("Output should contain smallValue meter")
	}
}
