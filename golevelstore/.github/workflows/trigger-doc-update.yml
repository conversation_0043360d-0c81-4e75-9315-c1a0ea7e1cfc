name: Trigger API Docs Update

on:
  push:
    branches:
      - main

jobs:
  notify-docs-repo:
    runs-on: ubuntu-latest
    steps:
      - name: Trigger docs repo update
        uses: peter-evans/repository-dispatch@v2
        with:
          token: ${{ secrets.WORKFLOW_TOKEN_GITHUB }}
          repository: real-rm/goapidocs
          event-type: update-docs
          client-payload: '{"source_repo": "${{ github.repository }}"}'
