package levelStore

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"

	gohelper "github.com/real-rm/gohelper"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	DEFAULT_UPDATE_INTERVAL = 10 * time.Minute   // default update interval
	DEFAULT_CLEAN_INTERVAL  = 7 * 24 * time.Hour // default clean interval
	DEFAULT_L1_DIFF_LIMIT   = 4                  // default L1 diff limit
	DEFAULT_SOURCE_ROOT     = "/data/files"      // default source root directory
	DEFAULT_L1_START_NUM    = 100                // default starting L1 number for stats-based generation
	DEFAULT_L1_FILE_LIMIT   = 1000000            // default file limit per L1 (1 million)
)

// DirStats tracks directory statistics
type DirStats struct {
	EntityAmount int `bson:"l" json:"entity_amount"` // Number of entities in directory
	FileAmount   int `bson:"f" json:"file_amount"`   // Number of files in directory
}

// DirMetaInfo represents the metadata information for a directory
type DirMetaInfo struct {
	TotalEntities   int                 `json:"total_entities"`
	TotalFiles      int                 `json:"total_files"`
	L2Stats         map[string]DirStats `json:"l2_stats"`
	ActualSize      int64               `json:"actual_size"`       // Actual size in bytes
	DiskSize        int64               `json:"disk_size"`         // Disk size in bytes (including block size)
	ActualSizeHuman string              `json:"actual_size_human"` // Human-readable actual size
	DiskSizeHuman   string              `json:"disk_size_human"`   // Human-readable disk size
	LastUpdated     time.Time           `json:"last_updated"`
}

// l1Stats holds statistics and error information for an L1 directory
type l1Stats struct {
	metaInfo  DirMetaInfo
	lastError error
}

// DirKeyStore manages directory key allocation and storage
type DirKeyStore struct {
	mu           sync.RWMutex
	prefix       string                   // Original board prefix (e.g., "TRB", "DDF", "NEWBOARD")
	entryName    string                   // Entry name (e.g., "video", "file", empty for compatibility)
	boardKey     string                   // MongoDB and registry key (e.g., "TRB", "NEWBOARD_video")
	collection   *gomongo.MongoCollection // MongoDB collection
	tmpDirMap    sync.Map                 // Temporary directory mapping {L1: []string}
	dirMap       map[string]DirStats      // Directory statistics {L1-L2: stats}
	lastUpdateTs time.Time                // Last update timestamp
	lastSavedTs  time.Time                // Last save timestamp
	saveTicker   *gohelper.Interval       // Ticker for saving changes
	cleanTicker  *gohelper.Interval       // Ticker for cleaning tmp dir map
	sourceRoot   string                   // Root directory for file storage
	curL1        string                   // Current active L1 directory (e.g., "100", "101")
	customDirs   []string                 // Custom directories for statistics files
}

// NewDirKeyStore creates a new DirKeyStore instance
func NewDirKeyStore(prefix string, coll interface{}, entryName string, updateInterval ...time.Duration) (*DirKeyStore, error) {
	if coll == nil {
		return nil, fmt.Errorf("collection is nil")
	}

	mongoCollection, ok := coll.(*gomongo.MongoCollection)
	if !ok {
		return nil, fmt.Errorf("collection must be *gomongo.MongoCollection, got %T", coll)
	}

	// Parse update interval
	interval := DEFAULT_UPDATE_INTERVAL
	if len(updateInterval) > 0 {
		if updateInterval[0] <= 0 {
			golog.Warn("Invalid update interval, using default", "interval", updateInterval[0], "default", DEFAULT_UPDATE_INTERVAL)
		} else {
			interval = updateInterval[0]
		}
	}

	// Build boardKey for MongoDB and registry operations
	var boardKey string
	if entryName != "" {
		boardKey = fmt.Sprintf("%s_%s", prefix, entryName)
	} else {
		boardKey = prefix
	}

	// Validate based on original prefix (for L2 size lookup)
	if _, exists := SOURCE_L2_SIZE[prefix]; !exists {
		return nil, fmt.Errorf("unsupported board type: %s", prefix)
	}

	// 检查是否为已知的图片板块
	_, isImageBoard := BoardImageDirName[prefix]

	// 仅对图片板块严格验证目录配置
	if isImageBoard {
		dirs, err := GetImageDir(prefix)
		if err != nil {
			golog.Error("Failed to get image directories", "board", prefix, "error", err)
			return nil, fmt.Errorf("failed to get image directories for board %s: %w", prefix, err)
		}
		if len(dirs) == 0 {
			golog.Error("No image directories configured", "board", prefix)
			return nil, fmt.Errorf("no image directories configured for board: %s", prefix)
		}
	} else {
		// 对于非图片板块，只记录日志，不阻止创建
		golog.Info("Creating DirKeyStore for non-image board", "board", prefix, "entryName", entryName)
	}

	store := &DirKeyStore{
		prefix:       prefix,
		entryName:    entryName,
		boardKey:     boardKey,
		collection:   mongoCollection,
		dirMap:       make(map[string]DirStats),
		lastUpdateTs: time.Now(),
		lastSavedTs:  time.Now(),
		sourceRoot:   DEFAULT_SOURCE_ROOT,
		curL1:        "", // Will be loaded from MongoDB
	}

	// Load current L1 from MongoDB
	if err := store.loadCurrentL1FromMongoDB(); err != nil {
		golog.Error("Failed to load current L1", "board", boardKey, "error", err)
		return nil, fmt.Errorf("failed to load current L1 for board %s: %w", boardKey, err)
	}

	// Start background save ticker
	store.saveTicker = gohelper.StartInterval(float64(interval.Seconds()), store.SaveChangedCurrent)

	// Start background clean ticker
	store.cleanTicker = gohelper.StartInterval(float64(DEFAULT_CLEAN_INTERVAL.Seconds()), store.CleanTmpDirMap)

	// Register store for stats-based L1 generation using boardKey
	RegisterDirKeyStore(boardKey, store)

	return store, nil
}

// SetSourceRoot sets the source root directory
func (store *DirKeyStore) SetSourceRoot(root string) {
	root = strings.TrimSpace(root)
	if root == "" {
		store.sourceRoot = DEFAULT_SOURCE_ROOT
	} else {
		store.sourceRoot = root
	}
}

// Close stops all background goroutines and waits for them to finish
func (store *DirKeyStore) Close() {
	if store == nil {
		return
	}
	if store.collection == nil {
		return
	}
	// Save any remaining changes
	store.SaveChangedCurrent()

	// Stop tickers
	if store.saveTicker != nil {
		store.saveTicker.Stop()
		store.saveTicker = nil // Set to nil after stopping
	}
	if store.cleanTicker != nil {
		store.cleanTicker.Stop()
		store.cleanTicker = nil // Set to nil after stopping
	}

	// Unregister store from global registry
	UnregisterDirKeyStore(store.boardKey)

	// Clear dirMap
	store.mu.Lock()
	store.dirMap = make(map[string]DirStats)
	store.mu.Unlock()
}

// SetCustomDirectories sets custom directories for storing statistics files
// This is primarily used by goupload and other systems that manage their own storage paths
func (store *DirKeyStore) SetCustomDirectories(dirs []string) {
	if store == nil {
		return
	}

	// Filter out empty strings and validate paths
	validDirs := make([]string, 0, len(dirs))
	for _, dir := range dirs {
		if dir = strings.TrimSpace(dir); dir != "" {
			validDirs = append(validDirs, dir)
		}
	}

	store.mu.Lock()
	defer store.mu.Unlock()
	store.customDirs = validDirs

	if len(validDirs) > 0 {
		golog.Info("Set custom directories for statistics files",
			"board", store.prefix,
			"count", len(validDirs),
			"dirs", validDirs)
	} else {
		golog.Info("Cleared custom directories, will use default directories",
			"board", store.prefix)
	}
}

// groupStatsByL1 groups directory statistics by L1 directory
func groupStatsByL1(dirMap map[string]DirStats) map[string]map[string]DirStats {
	l1Map := make(map[string]map[string]DirStats) // l1 -> {l2 -> stats}
	for key, stats := range dirMap {
		parts := strings.SplitN(key, "-", 2) // only split once
		if len(parts) != 2 {
			golog.Warn("invalid dirMap key", "key", key)
			continue
		}
		l1, l2 := parts[0], parts[1]
		if _, ok := l1Map[l1]; !ok {
			l1Map[l1] = make(map[string]DirStats)
		}
		l1Map[l1][l2] = stats
	}
	return l1Map
}

// SaveChangedCurrent saves current state if changes exist
func (store *DirKeyStore) SaveChangedCurrent() {
	if store == nil {
		golog.Error("SaveChangedCurrent called on nil store")
		return
	}
	if store.collection == nil {
		golog.Error("SaveChangedCurrent called with nil collection")
		return
	}

	store.mu.Lock()
	hasChanges := store.lastUpdateTs.After(store.lastSavedTs)
	if !hasChanges {
		// Clear dirMap even when no changes
		store.dirMap = make(map[string]DirStats)
		store.mu.Unlock()
		return
	}

	// Take snapshot and clear map in O(1) time
	snapshot := store.dirMap
	store.dirMap = make(map[string]DirStats)
	pendingTs := time.Now()

	// Check if we should save to file system
	_, isImageBoard := BoardImageDirName[store.prefix]
	hasCustomDirs := len(store.customDirs) > 0
	shouldSaveToFile := isImageBoard || hasCustomDirs

	store.mu.Unlock()

	// Process snapshot and perform IO operations outside the lock
	l1Map := groupStatsByL1(snapshot)

	// Only attempt to save to file if board is an image board or has custom directories
	if shouldSaveToFile {
		fileErr := store.saveStatsToFile(l1Map)
		if fileErr != nil {
			golog.Error("Failed to save current state to file", "error", fileErr)
			// Continue to try saving to database even if file save fails
		}
	}

	// Try to save to database (this will also check for L1 upgrades)
	dbErr := store.saveStatsToDb(l1Map)
	if dbErr != nil {
		golog.Error("Failed to save current state to database", "error", dbErr)
		store.mu.Lock()
		for k, v := range snapshot {
			merged := store.dirMap[k]
			merged.EntityAmount += v.EntityAmount
			merged.FileAmount += v.FileAmount
			store.dirMap[k] = merged
		}
		store.mu.Unlock()
		return
	}

	// Only update lastSavedTs if database save succeeded
	store.mu.Lock()
	store.lastSavedTs = pendingTs
	store.mu.Unlock()
}

// saveStatsToFile saves the provided data to file
func (store *DirKeyStore) saveStatsToFile(l1Map map[string]map[string]DirStats) error {
	// 优先使用自定义目录（来自 goupload 的 storage 配置）
	store.mu.RLock()
	customDirs := store.customDirs
	store.mu.RUnlock()

	var dirs []string

	if len(customDirs) > 0 {
		dirs = append([]string{}, customDirs...)
		golog.Debug("Using custom directories for stats", "board", store.prefix, "dirs", dirs)
	} else {
		// 如果没有自定义目录，才使用 GetImageDir
		var err error
		dirs, err = GetImageDir(store.prefix)

		if err != nil {
			// 对于图片板块，保持原有严格验证
			if _, isImageBoard := BoardImageDirName[store.prefix]; isImageBoard {
				golog.Error("Failed to get image directories", "board", store.prefix, "error", err)
				return fmt.Errorf("failed to get image directories for board %s: %w", store.prefix, err)
			} else {
				// 非图片板块，创建默认目录
				defaultDir := filepath.Join(DEFAULT_SOURCE_ROOT, "uploads", store.prefix)
				if err := os.MkdirAll(defaultDir, 0755); err != nil {
					golog.Error("Failed to create default directory", "dir", defaultDir, "error", err)
					return fmt.Errorf("failed to create default directory: %w", err)
				}
				dirs = []string{defaultDir}
				golog.Info("Using default directory for non-image board", "board", store.prefix, "dir", defaultDir)
			}
		}

		if len(dirs) == 0 {
			// 对于图片板块，保持原有行为
			if _, isImageBoard := BoardImageDirName[store.prefix]; isImageBoard {
				golog.Error("No image directories configured", "board", store.prefix)
				return fmt.Errorf("no image directories configured for board: %s", store.prefix)
			} else {
				// 非图片板块，创建默认目录
				defaultDir := filepath.Join(DEFAULT_SOURCE_ROOT, "uploads", store.prefix)
				if err := os.MkdirAll(defaultDir, 0755); err != nil {
					golog.Error("Failed to create default directory", "dir", defaultDir, "error", err)
					return fmt.Errorf("failed to create default directory: %w", err)
				}
				dirs = []string{defaultDir}
				golog.Info("Using default directory for non-image board", "board", store.prefix, "dir", defaultDir)
			}
		}
	}

	// Calculate stats for each L1
	l1StatsMap := store.calculateL1Stats(l1Map, dirs[0])

	golog.Debug("Saving stats to directories", "dirs", dirs, "l1_count", len(l1StatsMap))

	// Save to all directories
	return store.saveStatsToAllDirs(l1StatsMap, dirs)
}

// calculateL1Stats calculates statistics for each L1 directory
func (store *DirKeyStore) calculateL1Stats(l1Map map[string]map[string]DirStats, primaryDir string) map[string]l1Stats {
	l1StatsMap := make(map[string]l1Stats)

	for l1, l2Stats := range l1Map {
		stats := store.calculateSingleL1Stats(l1, l2Stats, primaryDir)
		l1StatsMap[l1] = stats
	}

	return l1StatsMap
}

// calculateSingleL1Stats calculates stats for a single L1 directory
func (store *DirKeyStore) calculateSingleL1Stats(l1 string, l2Stats map[string]DirStats, primaryDir string) l1Stats {
	metaPath := filepath.Join(primaryDir, l1, "dir_stats.json")
	existingMeta, err := store.loadExistingMeta(metaPath, l2Stats)
	if err != nil {
		return l1Stats{lastError: err}
	}

	// Merge stats and calculate totals
	existingMeta = store.mergeL2Stats(existingMeta, l2Stats)

	// Calculate directory sizes
	actualSize, diskSize, err := calculateDirSize(filepath.Join(primaryDir, l1))
	if err != nil {
		golog.Error("Failed to calculate directory size", "l1", l1, "error", err)
		return l1Stats{lastError: err}
	}

	// Create meta info
	metaInfo := store.createMetaInfo(existingMeta, actualSize, diskSize)
	return l1Stats{metaInfo: metaInfo}
}

// loadExistingMeta loads existing metadata from file or creates new one
func (store *DirKeyStore) loadExistingMeta(metaPath string, l2Stats map[string]DirStats) (DirMetaInfo, error) {
	var existingMeta DirMetaInfo

	if data, err := os.ReadFile(metaPath); err == nil {
		if err := json.Unmarshal(data, &existingMeta); err == nil {
			// Initialize L2Stats if nil
			if existingMeta.L2Stats == nil {
				existingMeta.L2Stats = make(map[string]DirStats)
			}
			return existingMeta, nil
		} else {
			// Handle corrupted file
			return existingMeta, store.handleCorruptedFile(metaPath, err)
		}
	} else {
		// If file doesn't exist, initialize with new stats
		existingMeta = DirMetaInfo{
			L2Stats:         make(map[string]DirStats),
			ActualSizeHuman: "0 B",
			DiskSizeHuman:   "0 B",
			LastUpdated:     time.Now(),
		}
		return existingMeta, nil
	}
}

// handleCorruptedFile handles corrupted metadata files by backing them up
func (store *DirKeyStore) handleCorruptedFile(metaPath string, parseErr error) error {
	golog.Error("Failed to parse dir_stats.json",
		"path", metaPath,
		"error", parseErr)

	// Backup the corrupted file
	backupPath := metaPath + ".corrupted." + time.Now().Format("20060102150405")
	if backupErr := os.Rename(metaPath, backupPath); backupErr != nil {
		golog.Error("Failed to backup corrupted file", "error", backupErr)
	} else {
		golog.Info("Backed up corrupted file", "backup", backupPath)
	}

	return fmt.Errorf("failed to parse dir_stats.json: %w", parseErr)
}

// mergeL2Stats merges L2 statistics into existing metadata
func (store *DirKeyStore) mergeL2Stats(existingMeta DirMetaInfo, l2Stats map[string]DirStats) DirMetaInfo {
	for l2, stats := range l2Stats {
		existingStats := existingMeta.L2Stats[l2]
		existingStats.EntityAmount += stats.EntityAmount
		existingStats.FileAmount += stats.FileAmount
		existingMeta.L2Stats[l2] = existingStats
	}
	return existingMeta
}

// createMetaInfo creates a DirMetaInfo with calculated totals and sizes
func (store *DirKeyStore) createMetaInfo(existingMeta DirMetaInfo, actualSize, diskSize int64) DirMetaInfo {
	// Calculate totals from merged L2Stats
	var totalFiles, totalEntities int
	for _, stats := range existingMeta.L2Stats {
		totalFiles += stats.FileAmount
		totalEntities += stats.EntityAmount
	}

	return DirMetaInfo{
		TotalEntities:   totalEntities,
		TotalFiles:      totalFiles,
		L2Stats:         existingMeta.L2Stats,
		ActualSize:      actualSize,
		DiskSize:        diskSize,
		ActualSizeHuman: formatBytes(actualSize),
		DiskSizeHuman:   formatBytes(diskSize),
		LastUpdated:     time.Now(),
	}
}

// saveStatsToAllDirs saves statistics to all configured directories
func (store *DirKeyStore) saveStatsToAllDirs(l1StatsMap map[string]l1Stats, dirs []string) error {
	var errors []error

	// Save to each directory
	for _, dir := range dirs {
		for l1, stats := range l1StatsMap {
			if stats.lastError != nil {
				errors = append(errors, stats.lastError)
				continue
			}

			// Create directory if it doesn't exist
			l1Path := filepath.Join(dir, l1)
			if err := os.MkdirAll(l1Path, 0755); err != nil {
				errors = append(errors, fmt.Errorf("failed to create directory %s: %w", l1Path, err))
				continue
			}

			// Save to this directory
			if err := store.saveStatsToSingleDir(dir, l1, stats.metaInfo); err != nil {
				errors = append(errors, err)
			}
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("failed to save stats to some directories: %v", errors)
	}
	return nil
}

// saveStatsToSingleDir saves statistics to a single directory
func (store *DirKeyStore) saveStatsToSingleDir(baseDir, l1 string, metaInfo DirMetaInfo) error {
	l1Path := filepath.Join(baseDir, l1)

	// Create and validate directory
	if err := store.createAndValidateDir(l1Path, l1); err != nil {
		return err
	}

	// Convert to JSON and save
	return store.saveMetaInfoToFile(l1Path, l1, metaInfo)
}

// createAndValidateDir creates directory and validates write permissions
func (store *DirKeyStore) createAndValidateDir(l1Path, l1 string) error {
	// Create directory if not exists
	if err := os.MkdirAll(l1Path, 0755); err != nil {
		golog.Error("Failed to create directory", "l1", l1, "path", l1Path, "error", err)
		return fmt.Errorf("failed to create directory %s: %w", l1Path, err)
	}

	// Check if directory is writable
	if info, err := os.Stat(l1Path); err != nil {
		golog.Error("Failed to stat directory", "l1", l1, "path", l1Path, "error", err)
		return fmt.Errorf("failed to stat directory %s: %w", l1Path, err)
	} else if !info.IsDir() {
		return fmt.Errorf("path exists but is not a directory: %s", l1Path)
	}

	// Test write permissions
	return store.testWritePermissions(l1Path, l1)
}

// testWritePermissions tests if directory is writable
func (store *DirKeyStore) testWritePermissions(l1Path, l1 string) error {
	testFile := filepath.Join(l1Path, ".test_write")
	if err := os.WriteFile(testFile, []byte("test"), 0644); err != nil {
		golog.Error("Directory is not writable", "l1", l1, "path", l1Path, "error", err)
		return fmt.Errorf("directory is not writable: %s", l1Path)
	}
	if err := os.Remove(testFile); err != nil {
		golog.Error("Failed to remove test file", "l1", l1, "path", l1Path, "error", err)
		return fmt.Errorf("failed to remove test file: %w", err)
	}
	return nil
}

// saveMetaInfoToFile converts metadata to JSON and saves to file
func (store *DirKeyStore) saveMetaInfoToFile(l1Path, l1 string, metaInfo DirMetaInfo) error {
	// Convert to JSON
	jsonData, err := json.MarshalIndent(metaInfo, "", "  ")
	if err != nil {
		golog.Error("Failed to marshal meta info", "l1", l1, "error", err)
		return err
	}

	// Save to file
	metaPath := filepath.Join(l1Path, "dir_stats.json")
	if err := os.WriteFile(metaPath, jsonData, 0644); err != nil {
		golog.Error("Failed to write meta file", "l1", l1, "path", metaPath, "error", err)
		return err
	}

	return nil
}

// saveStatsToDb saves the provided data to database
func (store *DirKeyStore) saveStatsToDb(l1Map map[string]map[string]DirStats) error {
	ctx := context.Background()

	// Update each l1 document
	for l1, l2Stats := range l1Map {
		// Calculate totals for this L1
		var totalFiles, totalEntities int
		for _, stats := range l2Stats {
			totalFiles += stats.FileAmount
			totalEntities += stats.EntityAmount
		}

		// Get existing document
		var existingDoc struct {
			L2Stats       map[string]DirStats `bson:"l2Stats"`
			TotalFiles    int                 `bson:"totalFiles"`
			TotalEntities int                 `bson:"totalEntities"`
		}

		filter := bson.D{
			{Key: "_id", Value: bson.D{
				{Key: "board", Value: store.boardKey},
				{Key: "l1", Value: l1},
			}},
		}

		// Try to get existing document
		err := store.collection.FindOne(ctx, filter).Decode(&existingDoc)
		if err == nil {
			if existingDoc.L2Stats == nil {
				existingDoc.L2Stats = make(map[string]DirStats)
			}
			// Add new stats to existing ones
			totalFiles += existingDoc.TotalFiles
			totalEntities += existingDoc.TotalEntities
			// Merge L2 stats
			for l2, stats := range l2Stats {
				existingStats := existingDoc.L2Stats[l2]
				existingStats.EntityAmount += stats.EntityAmount
				existingStats.FileAmount += stats.FileAmount
				existingDoc.L2Stats[l2] = existingStats
			}
			l2Stats = existingDoc.L2Stats
		}

		// Check if L1 upgrade is needed for current L1 (before saving)
		if l1 == store.getCurrentL1() && totalFiles >= DEFAULT_L1_FILE_LIMIT {
			golog.Info("L1 upgrade needed",
				"board", store.prefix,
				"l1", l1,
				"totalFiles", totalFiles,
				"limit", DEFAULT_L1_FILE_LIMIT)

			// Upgrade L1 before saving the data
			if upgradeErr := store.upgradeToNextL1(); upgradeErr != nil {
				golog.Error("Failed to upgrade L1", "error", upgradeErr)
				// Continue with saving even if upgrade fails
			}
		}

		// Update the database
		update := bson.M{
			"$set": bson.M{
				"l2Stats":       l2Stats,
				"totalFiles":    totalFiles,
				"totalEntities": totalEntities,
			},
		}

		_, err = store.collection.UpdateOne(ctx, filter, update, options.Update().SetUpsert(true))
		if err != nil {
			golog.Error("Failed to update document",
				"l1", l1,
				"error", err)
			return err
		}
	}

	return nil
}

// calculateDirSize calculates the actual size and disk size of a directory
func calculateDirSize(dirPath string) (int64, int64, error) {
	var actualSize, diskSize int64

	// Check if directory exists
	if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		// Directory doesn't exist, return zero sizes
		return 0, 0, nil
	}

	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() {
			actualSize += info.Size()
			// Calculate disk size (including block size)
			stat, ok := info.Sys().(*syscall.Stat_t)
			if ok {
				diskSize += stat.Blocks * 512 // 512 is the standard block size
			} else {
				diskSize += info.Size() // Fallback to actual size if stat_t is not available
			}
		}
		return nil
	})

	return actualSize, diskSize, err
}

// AddDirStats adds to the directory statistics for a specific L1-L2 combination
func (store *DirKeyStore) AddDirStats(l1 string, l2 string, entityAmount int, fileAmount int) {
	// Ensure L2 array exists and is cached (important for data consistency)
	dirArray, err := store.FetchDirArrayByL1(context.Background(), store.prefix, l1)
	if err != nil {
		golog.Error("failed to fetch dir array", "l1", l1, "error", err)
		return
	} else {
		golog.Debug("fetched dir array", "l1", l1, "dirArray", len(dirArray))
	}

	store.mu.Lock()
	defer store.mu.Unlock()

	if fileAmount == 0 && entityAmount == 0 {
		golog.Warn("AddDirStats called with fileAmount 0", "l1", l1, "l2", l2)
		return
	}
	key := fmt.Sprintf("%s-%s", l1, l2)
	stats := store.dirMap[key]
	stats.EntityAmount += entityAmount
	stats.FileAmount += fileAmount
	store.dirMap[key] = stats
	store.lastUpdateTs = time.Now()
}

// getDirArray retrieves dirArray from database
// Returns nil if not found or empty
func (store *DirKeyStore) getDirArrayFromDB(ctx context.Context, board string, l1 string) ([]string, error) {
	var result struct {
		DirArray []string `bson:"dirArray"`
	}

	err := store.collection.FindOne(ctx, bson.D{
		{Key: "_id", Value: bson.D{
			{Key: "board", Value: store.boardKey},
			{Key: "l1", Value: l1},
		}},
	}).Decode(&result)

	if err != nil {
		return nil, err
	}

	if len(result.DirArray) == 0 {
		return nil, fmt.Errorf("empty dirArray")
	}

	return result.DirArray, nil
}

// AddTmpDirMap adds a new L2 directory array to the temporary directory map
func (store *DirKeyStore) AddTmpDirMap(l1 string, dirArray []string) {
	store.tmpDirMap.Store(l1, dirArray)
	golog.Debug("Added new L2 directory array to tmpDirMap",
		"l1", l1,
		"array_size", len(dirArray))
}

// CleanTmpDirMap removes entries from tmpDirMap that are older than the specified duration
// or have L1 values that are too old compared to the current L1
func (store *DirKeyStore) CleanTmpDirMap() {
	cleaned := 0
	now := time.Now()
	currentOnD := gohelper.TimeToDateInt(now)
	currentL1 := CalculateL1(currentOnD, store.prefix)

	store.tmpDirMap.Range(func(key, value interface{}) bool {
		if l1Str, ok := key.(string); ok {
			// For stats-based boards, use a different cleanup strategy
			if useStatsBasedL1(store.prefix) {
				// For stats-based L1, clean entries that are significantly older than current L1
				l1Val, err1 := strconv.Atoi(l1Str)
				currentVal, err2 := strconv.Atoi(currentL1)
				if err1 != nil || err2 != nil {
					golog.Warn("invalid L1 value when cleaning tmpDirMap (stats-based)",
						"l1Str", l1Str, "currentL1", currentL1, "err1", err1, "err2", err2)
					return true // skip
				}
				// Clean if L1 is more than DEFAULT_L1_DIFF_LIMIT behind current L1
				if l1Val < currentVal-DEFAULT_L1_DIFF_LIMIT {
					store.tmpDirMap.Delete(key)
					cleaned++
				}
			} else {
				// For time-based boards, use the original date-based logic
				oldDate := now.AddDate(0, 0, -DEFAULT_L1_DIFF_LIMIT*7)
				oldOnD := gohelper.TimeToDateInt(oldDate)
				oldL1 := CalculateL1(oldOnD, store.prefix)

				// Clean if L1 is older than the limit
				l1Val, err1 := strconv.Atoi(l1Str)
				oldVal, err2 := strconv.Atoi(oldL1)
				if err1 != nil || err2 != nil {
					golog.Warn("invalid L1 value when cleaning tmpDirMap (time-based)",
						"l1Str", l1Str, "oldL1", oldL1, "err1", err1, "err2", err2)
					return true // skip
				}
				if l1Val < oldVal {
					store.tmpDirMap.Delete(key)
					cleaned++
				}
			}
		}
		return true
	})

	if cleaned > 0 {
		golog.Info("Cleaned old entries from tmpDirMap",
			"cleaned_count", cleaned,
			"current_l1", currentL1)
	}
}

// FetchDirArray retrieves l2 Array using board and propTsD
// If the array doesn't exist or is empty, creates a new one
func (store *DirKeyStore) FetchDirArray(ctx context.Context, board string, propTsD int) ([]string, error) {
	if board == "" || !IsValidBoard(board) {
		return nil, fmt.Errorf("invalid board: %s", board)
	}

	// Calculate L1 from propTsD
	l1 := CalculateL1(propTsD, board)

	// Use the new internal method
	return store.FetchDirArrayByL1(ctx, board, l1)
}

// FetchDirArrayByL1 retrieves l2 Array using board and l1 (extracted from FetchDirArray)
// If the array doesn't exist or is empty, creates a new one
func (store *DirKeyStore) FetchDirArrayByL1(ctx context.Context, board string, l1 string) ([]string, error) {
	if board == "" || !IsValidBoard(board) {
		return nil, fmt.Errorf("invalid board: %s", board)
	}
	if l1 == "" {
		return nil, fmt.Errorf("l1 cannot be empty")
	}
	if store.collection == nil {
		return nil, fmt.Errorf("store collection is nil")
	}

	// First check tmpDirMap
	if value, ok := store.tmpDirMap.Load(l1); ok {
		if dirArray, ok := value.([]string); ok {
			return dirArray, nil
		}
	}

	// Then check database
	dirArray, err := store.getDirArrayFromDB(ctx, board, l1)
	if err == nil {
		// Cache the result
		store.AddTmpDirMap(l1, dirArray)

		golog.Debug("Got L2 directory list",
			"board", board,
			"l1", l1,
			"array_size", len(dirArray))
		return dirArray, nil
	}

	// If not found or empty array, create a new one
	return store.SaveDirArrayByL1(ctx, board, l1, true)
}

// SaveDirArrayInDB creates or updates l2Array in database
// If force is false (default), returns existing array or creates new one if not exists
// If force is true, always creates new array
func (store *DirKeyStore) SaveDirArrayInDB(ctx context.Context, board string, propTsD int, force ...bool) ([]string, error) {
	if board == "" || !IsValidBoard(board) {
		return nil, fmt.Errorf("invalid board: %s", board)
	}

	// Check if force update is requested
	forceUpdate := false
	if len(force) > 0 {
		forceUpdate = force[0]
	}

	// Calculate L1 from propTsD
	l1 := CalculateL1(propTsD, board)

	// Use the new internal method
	return store.SaveDirArrayByL1(ctx, board, l1, forceUpdate)
}

// SaveDirArrayByL1 creates or updates l2Array in database using board and l1 (extracted from SaveDirArrayInDB)
// If force is false, returns existing array or creates new one if not exists
// If force is true, always creates new array
func (store *DirKeyStore) SaveDirArrayByL1(ctx context.Context, board string, l1 string, forceUpdate bool) ([]string, error) {
	if board == "" || !IsValidBoard(board) {
		return nil, fmt.Errorf("invalid board: %s", board)
	}
	if l1 == "" {
		return nil, fmt.Errorf("l1 cannot be empty")
	}
	if store.collection == nil {
		return nil, fmt.Errorf("store collection is nil")
	}

	golog.Info("saving L2 list", "board", board, "l1", l1, "force", forceUpdate)

	// Check existing array first
	dirArray, err := store.getDirArrayFromDB(ctx, board, l1)
	if err == nil && !forceUpdate {
		golog.Debug("Using existing L2 directory list",
			"board", board,
			"l1", l1,
			"array_size", len(dirArray))
		return dirArray, nil
	}

	// Create new array if forced or not exists
	dirArray, err = GetL2ListForL1(l1, SOURCE_L2_SIZE[board])
	if err != nil {
		golog.Error("failed to get L2 list", "error", err)
		return nil, fmt.Errorf("failed to get L2 list: %v", err)
	}

	filter := bson.D{
		{Key: "_id", Value: bson.D{
			{Key: "board", Value: store.boardKey},
			{Key: "l1", Value: l1},
		}},
	}
	update := bson.D{
		{Key: "$set", Value: bson.D{
			{Key: "dirArray", Value: dirArray},
		}},
	}

	_, err = store.collection.UpdateOne(ctx, filter, update, options.Update().SetUpsert(true))
	if err != nil {
		golog.Error("Failed to update L2 list",
			"board", board,
			"l1", l1,
			"error", err)
		return nil, fmt.Errorf("failed to update L2 list: %w", err)
	}

	// Update cache
	store.AddTmpDirMap(l1, dirArray)

	golog.Info("Successfully updated L2 list",
		"board", board,
		"l1", l1,
		"array_size", len(dirArray),
		"force", forceUpdate)

	return dirArray, nil
}

// IsValidBoard checks if the board is valid
func IsValidBoard(board string) bool {
	if board == "" {
		return false
	}

	if _, ok := SOURCE_L2_SIZE[board]; !ok {
		return false
	}

	return true
}

// loadCurrentL1FromMongoDB loads current L1 from MongoDB
func (store *DirKeyStore) loadCurrentL1FromMongoDB() error {
	var result struct {
		L1 string `bson:"l1"`
	}

	err := store.collection.FindOne(context.Background(), bson.D{
		{Key: "_id", Value: bson.D{
			{Key: "board", Value: store.boardKey},
			{Key: "type", Value: "current_l1"},
		}},
	}).Decode(&result)

	if err != nil {
		if err.Error() == "mongo: no documents in result" {
			// Initialize with default starting L1
			store.curL1 = fmt.Sprintf("%d", DEFAULT_L1_START_NUM)
			return store.saveCurrentL1ToMongoDB()
		}
		return fmt.Errorf("failed to load current L1: %w", err)
	}

	store.curL1 = result.L1
	golog.Info("Loaded current L1", "board", store.prefix, "l1", store.curL1)
	return nil
}

// saveCurrentL1ToMongoDB saves current L1 to MongoDB
func (store *DirKeyStore) saveCurrentL1ToMongoDB() error {
	filter := bson.D{
		{Key: "_id", Value: bson.D{
			{Key: "board", Value: store.boardKey},
			{Key: "type", Value: "current_l1"},
		}},
	}
	update := bson.D{
		{Key: "$set", Value: bson.D{
			{Key: "l1", Value: store.curL1},
		}},
	}

	opts := options.Update().SetUpsert(true)
	_, err := store.collection.UpdateOne(context.Background(), filter, update, opts)
	if err != nil {
		return fmt.Errorf("failed to save current L1: %w", err)
	}

	golog.Debug("Saved current L1", "board", store.prefix, "l1", store.curL1)
	return nil
}

// getCurrentL1 returns current L1 in a thread-safe manner
func (store *DirKeyStore) getCurrentL1() string {
	store.mu.RLock()
	defer store.mu.RUnlock()
	return store.curL1
}

// upgradeToNextL1 upgrades to the next L1 number
func (store *DirKeyStore) upgradeToNextL1() error {
	store.mu.Lock()
	defer store.mu.Unlock()

	// Parse current L1 number and increment
	currentNum, err := strconv.Atoi(store.curL1)
	if err != nil {
		golog.Error("Failed to parse current L1", "l1", store.curL1, "error", err)
		return fmt.Errorf("failed to parse current L1 %s: %w", store.curL1, err)
	}

	oldL1 := store.curL1
	newL1 := fmt.Sprintf("%d", currentNum+1)
	store.curL1 = newL1

	// Save to MongoDB
	if err := store.saveCurrentL1ToMongoDB(); err != nil {
		// Rollback on failure
		store.curL1 = oldL1
		return fmt.Errorf("failed to save new L1 to MongoDB: %w", err)
	}

	golog.Info("Upgraded L1", "board", store.prefix, "from", oldL1, "to", newL1)
	return nil
}

// formatBytes formats bytes into human readable format (KB, MB, GB, etc.)
func formatBytes(bytes int64) string {
	if bytes < 0 {
		return "0 B"
	}

	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}

	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}
