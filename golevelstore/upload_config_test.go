package levelStore

import (
	"reflect"
	"strings"
	"testing"
)

func TestGetConnectionSources(t *testing.T) {
	tests := []struct {
		name        string
		cfg         interface{}
		expectError bool
		expected    *ConnectionSources
	}{
		{
			name: "Valid configuration",
			cfg: map[string]interface{}{
				"connection_sources": map[string]interface{}{
					"s3_providers": []interface{}{
						map[string]interface{}{
							"name":     "aws-s3",
							"endpoint": "s3.amazonaws.com",
							"key":      "test-key",
							"pass":     "test-pass",
							"region":   "us-east-1",
						},
						map[string]interface{}{
							"name":     "minio",
							"endpoint": "localhost:9000",
							"key":      "minioadmin",
							"pass":     "minioadmin",
							"region":   "us-east-1",
						},
					},
				},
			},
			expectError: false,
			expected: &ConnectionSources{
				S3Providers: []S3ProviderConfig{
					{
						Name:     "aws-s3",
						Endpoint: "s3.amazonaws.com",
						Key:      "test-key",
						Pass:     "test-pass",
						Region:   "us-east-1",
					},
					{
						Name:     "minio",
						Endpoint: "localhost:9000",
						Key:      "minioadmin",
						Pass:     "minioadmin",
						Region:   "us-east-1",
					},
				},
			},
		},
		{
			name: "Empty s3_providers",
			cfg: map[string]interface{}{
				"connection_sources": map[string]interface{}{
					"s3_providers": []interface{}{},
				},
			},
			expectError: false,
			expected: &ConnectionSources{
				S3Providers: []S3ProviderConfig{},
			},
		},
		{
			name: "Missing s3_providers",
			cfg: map[string]interface{}{
				"connection_sources": map[string]interface{}{},
			},
			expectError: false,
			expected: &ConnectionSources{
				S3Providers: []S3ProviderConfig{},
			},
		},
		{
			name:        "Invalid cfg type",
			cfg:         "invalid",
			expectError: true,
		},
		{
			name: "Missing connection_sources",
			cfg: map[string]interface{}{
				"other": "value",
			},
			expectError: true,
		},
		{
			name: "Invalid connection_sources type",
			cfg: map[string]interface{}{
				"connection_sources": "invalid",
			},
			expectError: true,
		},
		{
			name: "Invalid s3_providers type",
			cfg: map[string]interface{}{
				"connection_sources": map[string]interface{}{
					"s3_providers": "invalid",
				},
			},
			expectError: true,
		},
		{
			name:        "Nil config",
			cfg:         nil,
			expectError: true, // Will fail because goconfig is not available in test
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := GetConnectionSources(tt.cfg)

			if tt.expectError {
				if err == nil {
					t.Errorf("GetConnectionSources() expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("GetConnectionSources() unexpected error: %v", err)
				return
			}

			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("GetConnectionSources() = %+v, want %+v", result, tt.expected)
			}
		})
	}
}

func TestGetUserUploadConfig(t *testing.T) {
	tests := []struct {
		name        string
		site        string
		entryName   string
		cfg         interface{}
		expectError bool
		errorMsg    string
		expected    *UserUploadConfig
	}{
		{
			name:      "Valid configuration",
			site:      "rm",
			entryName: "file",
			cfg: map[string]interface{}{
				"userupload": map[string]interface{}{
					"site": "rm",
					"types": []interface{}{
						map[string]interface{}{
							"entryName": "file",
							"prefix":    "/uploads/files",
							"tmpPath":   "/tmp/uploads",
							"maxSize":   "100MB",
							"draftPath": "/drafts",
							"storage": []interface{}{
								map[string]interface{}{
									"type": "local",
									"path": "/data/storage",
								},
								map[string]interface{}{
									"type":   "s3",
									"target": "aws-s3",
									"bucket": "my-bucket",
								},
							},
						},
					},
				},
			},
			expectError: false,
			expected: &UserUploadConfig{
				Prefix:    "/uploads/files",
				TmpPath:   "/tmp/uploads",
				MaxSize:   "100MB",
				DraftPath: "/drafts",
				Storage: []StorageConfig{
					{
						Type: "local",
						Path: "/data/storage",
					},
					{
						Type:   "s3",
						Target: "aws-s3",
						Bucket: "my-bucket",
					},
				},
			},
		},
		{
			name:      "Configuration without storage",
			site:      "rm",
			entryName: "file",
			cfg: map[string]interface{}{
				"userupload": map[string]interface{}{
					"site": "rm",
					"types": []interface{}{
						map[string]interface{}{
							"entryName": "file",
							"prefix":    "/uploads/files",
							"tmpPath":   "/tmp/uploads",
						},
					},
				},
			},
			expectError: false,
			expected: &UserUploadConfig{
				Prefix:  "/uploads/files",
				TmpPath: "/tmp/uploads",
				Storage: []StorageConfig{},
			},
		},
		{
			name:        "Invalid cfg type",
			site:        "rm",
			entryName:   "file",
			cfg:         "invalid",
			expectError: true,
			errorMsg:    "invalid cfg type",
		},
		{
			name:        "Missing userupload section",
			site:        "rm",
			entryName:   "file",
			cfg:         map[string]interface{}{},
			expectError: true,
			errorMsg:    "userupload configuration not found",
		},
		{
			name:      "Site mismatch",
			site:      "different",
			entryName: "file",
			cfg: map[string]interface{}{
				"userupload": map[string]interface{}{
					"site": "rm",
					"types": []interface{}{
						map[string]interface{}{
							"entryName": "file",
						},
					},
				},
			},
			expectError: true,
			errorMsg:    "site 'different' not found",
		},
		{
			name:      "EntryName not found",
			site:      "rm",
			entryName: "nonexistent",
			cfg: map[string]interface{}{
				"userupload": map[string]interface{}{
					"site": "rm",
					"types": []interface{}{
						map[string]interface{}{
							"entryName": "file",
						},
					},
				},
			},
			expectError: true,
			errorMsg:    "entryName 'nonexistent' not found",
		},
		{
			name:        "Nil config",
			site:        "rm",
			entryName:   "file",
			cfg:         nil,
			expectError: true, // Will fail because goconfig is not available in test
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := GetUserUploadConfig(tt.site, tt.entryName, tt.cfg)

			if tt.expectError {
				if err == nil {
					t.Errorf("GetUserUploadConfig() expected error but got none")
					return
				}
				if tt.errorMsg != "" && !strings.Contains(err.Error(), tt.errorMsg) {
					t.Errorf("GetUserUploadConfig() error = %v, want error containing %v", err, tt.errorMsg)
				}
				return
			}

			if err != nil {
				t.Errorf("GetUserUploadConfig() unexpected error = %v", err)
				return
			}

			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("GetUserUploadConfig() = %+v, want %+v", result, tt.expected)
			}
		})
	}
}

func TestParseBasicFields(t *testing.T) {
	tests := []struct {
		name     string
		input    map[string]interface{}
		expected UserUploadConfig
	}{
		{
			name: "All fields present",
			input: map[string]interface{}{
				"prefix":    "/test",
				"tmpPath":   "/tmp/test",
				"maxSize":   "100MB",
				"draftPath": "/draft",
			},
			expected: UserUploadConfig{
				Prefix:    "/test",
				TmpPath:   "/tmp/test",
				MaxSize:   "100MB",
				DraftPath: "/draft",
			},
		},
		{
			name: "Some fields missing",
			input: map[string]interface{}{
				"prefix":  "/test",
				"tmpPath": "/tmp/test",
			},
			expected: UserUploadConfig{
				Prefix:  "/test",
				TmpPath: "/tmp/test",
			},
		},
		{
			name: "Invalid field types",
			input: map[string]interface{}{
				"prefix":    123,     // should be string
				"tmpPath":   "/tmp",
				"maxSize":   true,    // should be string
				"draftPath": "/draft",
			},
			expected: UserUploadConfig{
				TmpPath:   "/tmp",
				DraftPath: "/draft",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := &UserUploadConfig{}
			parseBasicFields(tt.input, config)

			if config.Prefix != tt.expected.Prefix {
				t.Errorf("parseBasicFields() prefix = %v, want %v", config.Prefix, tt.expected.Prefix)
			}
			if config.TmpPath != tt.expected.TmpPath {
				t.Errorf("parseBasicFields() tmpPath = %v, want %v", config.TmpPath, tt.expected.TmpPath)
			}
			if config.MaxSize != tt.expected.MaxSize {
				t.Errorf("parseBasicFields() maxSize = %v, want %v", config.MaxSize, tt.expected.MaxSize)
			}
			if config.DraftPath != tt.expected.DraftPath {
				t.Errorf("parseBasicFields() draftPath = %v, want %v", config.DraftPath, tt.expected.DraftPath)
			}
		})
	}
}
