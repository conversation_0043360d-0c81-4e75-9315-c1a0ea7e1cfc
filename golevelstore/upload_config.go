package levelStore

import (
	"fmt"

	goconfig "github.com/real-rm/goconfig"
)

// ConnectionSources represents the connection sources configuration
type ConnectionSources struct {
	S3Providers []S3ProviderConfig `json:"s3_providers"`
}

// S3ProviderConfig represents an S3 provider configuration
type S3ProviderConfig struct {
	Name     string `json:"name"`
	Endpoint string `json:"endpoint"`
	Key      string `json:"key"`
	Pass     string `json:"pass"`
	Region   string `json:"region"`
}

// UserUploadConfig represents the user upload configuration
type UserUploadConfig struct {
	Prefix    string          `json:"prefix"`
	TmpPath   string          `json:"tmpPath"`
	MaxSize   string          `json:"maxSize"`
	DraftPath string          `json:"draftPath"`
	Storage   []StorageConfig `json:"storage"`
}

// StorageConfig represents a storage configuration
type StorageConfig struct {
	Type   string `json:"type"`
	Path   string `json:"path"`
	Target string `json:"target"`
	Bucket string `json:"bucket"`
}

// GetConnectionSources retrieves connection sources configuration.
// cfg is optional - if nil, uses goconfig.Config("connection_sources")
func GetConnectionSources(cfg interface{}) (*ConnectionSources, error) {
	var connectionSourcesConfig map[string]interface{}

	if cfg != nil {
		// Use provided configuration to get connection_sources section
		if providedConfig, ok := cfg.(map[string]interface{}); ok {
			if connSources, exists := providedConfig["connection_sources"]; exists {
				if connSourcesMap, ok := connSources.(map[string]interface{}); ok {
					connectionSourcesConfig = connSourcesMap
				} else {
					return nil, fmt.Errorf("invalid connection_sources configuration in provided cfg")
				}
			} else {
				return nil, fmt.Errorf("connection_sources configuration not found in provided cfg")
			}
		} else {
			return nil, fmt.Errorf("invalid cfg type, expected map[string]interface{}")
		}
	} else {
		// Use default goconfig.Config
		if config, ok := goconfig.Config("connection_sources").(map[string]interface{}); ok {
			connectionSourcesConfig = config
		} else {
			return nil, fmt.Errorf("invalid or missing connection_sources configuration")
		}
	}

	// Parse S3 providers
	s3ProvidersInterface, exists := connectionSourcesConfig["s3_providers"]
	if !exists {
		return &ConnectionSources{S3Providers: []S3ProviderConfig{}}, nil
	}

	s3ProvidersArray, ok := s3ProvidersInterface.([]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid s3_providers configuration, expected array")
	}

	s3Providers := make([]S3ProviderConfig, 0, len(s3ProvidersArray))
	for _, providerInterface := range s3ProvidersArray {
		providerMap, ok := providerInterface.(map[string]interface{})
		if !ok {
			continue // Skip invalid entries
		}

		provider := S3ProviderConfig{}
		if name, ok := providerMap["name"].(string); ok {
			provider.Name = name
		}
		if endpoint, ok := providerMap["endpoint"].(string); ok {
			provider.Endpoint = endpoint
		}
		if key, ok := providerMap["key"].(string); ok {
			provider.Key = key
		}
		if pass, ok := providerMap["pass"].(string); ok {
			provider.Pass = pass
		}
		if region, ok := providerMap["region"].(string); ok {
			provider.Region = region
		}

		s3Providers = append(s3Providers, provider)
	}

	return &ConnectionSources{S3Providers: s3Providers}, nil
}

// GetUserUploadConfig retrieves user upload configuration for a given site and entryName.
// It validates that the site exists in the userupload configuration and returns
// the configuration for the specified entryName.
// cfg is optional - if nil, uses goconfig.Config("userupload")
func GetUserUploadConfig(site, entryName string, cfg interface{}) (*UserUploadConfig, error) {
	// Get the userupload configuration
	userUploadConfig, err := getUserUploadConfigMap(cfg)
	if err != nil {
		return nil, err
	}

	// Validate site configuration
	if err := validateSiteConfig(userUploadConfig, site); err != nil {
		return nil, err
	}

	// Get and validate types configuration
	typesArray, err := getTypesConfig(userUploadConfig)
	if err != nil {
		return nil, err
	}

	// Find and parse the configuration for the requested entryName
	return findAndParseEntryConfig(typesArray, entryName)
}

// getUserUploadConfigMap extracts userupload configuration from cfg or default config
func getUserUploadConfigMap(cfg interface{}) (map[string]interface{}, error) {
	if cfg != nil {
		// Use provided configuration to get userupload section
		if providedConfig, ok := cfg.(map[string]interface{}); ok {
			if uploadConfig, exists := providedConfig["userupload"]; exists {
				if uploadConfigMap, ok := uploadConfig.(map[string]interface{}); ok {
					return uploadConfigMap, nil
				} else {
					return nil, fmt.Errorf("invalid userupload configuration in provided cfg")
				}
			} else {
				return nil, fmt.Errorf("userupload configuration not found in provided cfg")
			}
		} else {
			return nil, fmt.Errorf("invalid cfg type, expected map[string]interface{}")
		}
	} else {
		// Use default goconfig.Config
		if config, ok := goconfig.Config("userupload").(map[string]interface{}); ok {
			return config, nil
		} else {
			return nil, fmt.Errorf("invalid or missing userupload configuration")
		}
	}
}

// validateSiteConfig validates that the requested site exists in configuration
func validateSiteConfig(userUploadConfig map[string]interface{}, site string) error {
	siteConfig, exists := userUploadConfig["site"]
	if !exists {
		return fmt.Errorf("site configuration not found in userupload")
	}

	if siteStr, ok := siteConfig.(string); !ok || siteStr != site {
		return fmt.Errorf("site '%s' not found in userupload configuration", site)
	}

	return nil
}

// getTypesConfig extracts and validates types configuration
func getTypesConfig(userUploadConfig map[string]interface{}) ([]interface{}, error) {
	typesConfig, exists := userUploadConfig["types"]
	if !exists {
		return nil, fmt.Errorf("types configuration not found in userupload")
	}

	typesArray, ok := typesConfig.([]interface{})
	if !ok {
		return nil, fmt.Errorf("invalid types configuration, expected array")
	}

	return typesArray, nil
}

// findAndParseEntryConfig finds the requested entryName and parses its configuration
func findAndParseEntryConfig(typesArray []interface{}, entryName string) (*UserUploadConfig, error) {
	for _, typeConfigInterface := range typesArray {
		typeConfigMap, ok := typeConfigInterface.(map[string]interface{})
		if !ok {
			continue // Skip invalid entries
		}

		// Check if this is the entry we're looking for
		if entryNameValue, exists := typeConfigMap["entryName"]; exists {
			if entryNameStr, ok := entryNameValue.(string); ok && entryNameStr == entryName {
				return parseEntryConfig(typeConfigMap)
			}
		}
	}

	return nil, fmt.Errorf("entryName '%s' not found in configuration", entryName)
}

// parseEntryConfig parses a single entry configuration
func parseEntryConfig(entryConfig map[string]interface{}) (*UserUploadConfig, error) {
	config := &UserUploadConfig{}

	// Parse basic fields
	parseBasicFields(entryConfig, config)

	// Parse storage configuration
	if err := parseStorage(entryConfig, config); err != nil {
		return nil, err
	}

	return config, nil
}

// parseBasicFields parses basic configuration fields
func parseBasicFields(entryConfig map[string]interface{}, config *UserUploadConfig) {
	if prefix, ok := entryConfig["prefix"].(string); ok {
		config.Prefix = prefix
	}
	if tmpPath, ok := entryConfig["tmpPath"].(string); ok {
		config.TmpPath = tmpPath
	}
	if maxSize, ok := entryConfig["maxSize"].(string); ok {
		config.MaxSize = maxSize
	}
	if draftPath, ok := entryConfig["draftPath"].(string); ok {
		config.DraftPath = draftPath
	}
}

// parseStorage parses storage configuration
func parseStorage(entryConfig map[string]interface{}, config *UserUploadConfig) error {
	storageInterface, exists := entryConfig["storage"]
	if !exists {
		config.Storage = []StorageConfig{} // Initialize empty slice
		return nil                         // Storage is optional
	}

	storageArray, ok := storageInterface.([]interface{})
	if !ok {
		return fmt.Errorf("invalid storage configuration, expected array")
	}

	storage := make([]StorageConfig, 0, len(storageArray))
	for _, storageItemInterface := range storageArray {
		storageItemMap, ok := storageItemInterface.(map[string]interface{})
		if !ok {
			continue // Skip invalid entries
		}

		storageItem := StorageConfig{}
		if storageType, ok := storageItemMap["type"].(string); ok {
			storageItem.Type = storageType
		}
		if path, ok := storageItemMap["path"].(string); ok {
			storageItem.Path = path
		}
		if target, ok := storageItemMap["target"].(string); ok {
			storageItem.Target = target
		}
		if bucket, ok := storageItemMap["bucket"].(string); ok {
			storageItem.Bucket = bucket
		}

		storage = append(storage, storageItem)
	}

	config.Storage = storage
	return nil
}
