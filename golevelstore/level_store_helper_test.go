package levelStore

import (
	"fmt"
	"math"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"testing"
	"time"

	golog "github.com/real-rm/golog"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMain(m *testing.M) {
	// Initialize logging
	if err := golog.InitLog(); err != nil {
		fmt.Printf("Failed to initialize logging: %v\n", err)
		os.Exit(1)
	}

	// Initialize constants for tests
	initTestConstants()

	// Run tests
	exitCode := m.Run()

	// Exit with test exit code
	os.Exit(exitCode)
}

// initTestConstants initializes constants for tests
func initTestConstants() {
	// Set up L2 folder sizes
	SOURCE_L2_SIZE = map[string]int{
		"TRB":      512,
		"CAR":      256,
		"DDF":      1024,
		"BRE":      64,
		"CLG":      64,
		"OTW":      64,
		"EDM":      64,
		"USER":     64,
		"TEST":     128, // TEST板块 - 用于测试非图片板块功能
		"NEWBOARD": 256, // NEWBOARD板块 - 支持多entryName的新板块
	}

	// Generate some test L2 folders
	testL2Folders, _ := GenerateUUID5Array(TOTAL_UUID5_COUNT)
	L2_FOLDER_LIST = testL2Folders
}

func TestCalculateL1(t *testing.T) {
	SetupTestProcess(t)

	tests := []struct {
		name     string
		dateNum  int
		board    string
		expected string
	}{
		{
			name:     "year 2015 week 1",
			dateNum:  20150101,
			board:    "TRB",
			expected: "750",
		},
		{
			name:     "year 2024 week 25",
			dateNum:  20240620,
			board:    "TRB",
			expected: "1224",
		},
		{
			name:     "year 2024 USER board",
			dateNum:  20240620,
			board:    "USER",
			expected: "1200",
		},
		{
			name:     "year before 2015",
			dateNum:  20140101,
			board:    "TRB",
			expected: "750",
		},
		{
			name:     "week > 50",
			dateNum:  20241225,
			board:    "TRB",
			expected: "1200",
		},
		{
			name:     "invalid date",
			dateNum:  0,
			board:    "TRB",
			expected: "750",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateL1(tt.dateNum, tt.board)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestCalculateL1_EdgeCases(t *testing.T) {
	SetupTestProcess(t)

	tests := []struct {
		name     string
		dateNum  int
		board    string
		expected string
	}{
		{
			name:     "max int date",
			dateNum:  99991231,
			board:    "TRB",
			expected: "399950",
		},
		{
			name:     "min int date",
			dateNum:  1,
			board:    "TRB",
			expected: "750",
		},
		{
			name:     "invalid board with valid date",
			dateNum:  20240101,
			board:    "INVALID",
			expected: "100", // Invalid boards now use stats-based L1 (fallback to "100")
		},
		{
			name:     "empty board with valid date",
			dateNum:  20240101,
			board:    "",
			expected: "1200", // Empty boards use time-based L1 (backward compatibility)
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CalculateL1(tt.dateNum, tt.board)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGenerateUUID5Array(t *testing.T) {
	SetupTestProcess(t)

	tests := []struct {
		name    string
		size    int
		wantErr bool
	}{
		{
			name:    "Size 64 (BRE/CLG/OTW/EDM)",
			size:    64,
			wantErr: false,
		},
		{
			name:    "Size 256 (CAR)",
			size:    256,
			wantErr: false,
		},
		{
			name:    "Size 512 (TRB)",
			size:    512,
			wantErr: false,
		},
		{
			name:    "Size 1024 (DDF)",
			size:    1024,
			wantErr: false,
		},
		{
			name:    "zero size",
			size:    0,
			wantErr: true,
		},
		{
			name:    "negative size",
			size:    -1,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GenerateUUID5Array(tt.size)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, got)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, got)
				assert.Equal(t, tt.size, len(got))

				// Check each UUID is 5 characters
				for _, uuid := range got {
					assert.Len(t, uuid, 5)
				}

				// Check uniqueness
				seen := make(map[string]bool)
				for _, uuid := range got {
					assert.False(t, seen[uuid])
					seen[uuid] = true
				}
			}
		})
	}
}

func TestGenerateUUID5Array_EdgeCases(t *testing.T) {
	SetupTestProcess(t)

	tests := []struct {
		name    string
		size    int
		wantErr bool
	}{
		{
			name:    "very large size",
			size:    10000,
			wantErr: false,
		},
		{
			name:    "zero size",
			size:    0,
			wantErr: true,
		},
		{
			name:    "negative size",
			size:    -1,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := GenerateUUID5Array(tt.size)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.size, len(result))
				// Check uniqueness
				seen := make(map[string]bool)
				for _, uuid := range result {
					assert.False(t, seen[uuid])
					seen[uuid] = true
				}
			}
		})
	}
}

func TestCalculateL2Index(t *testing.T) {
	SetupTestProcess(t)

	tests := []struct {
		name     string
		sid      string
		size     int
		expected int
	}{
		{
			name:     "normal sid",
			sid:      "W4054894",
			size:     512,
			expected: 0, // This will be deterministic due to FIXED_SEED
		},
		{
			name:     "empty sid",
			sid:      "",
			size:     512,
			expected: 0,
		},
		{
			name:     "size 1",
			sid:      "W4054894",
			size:     1,
			expected: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := CalculateL2Index(tt.sid, tt.size)
			if err != nil {
				t.Errorf("CalculateL2Index() returned error: %v", err)
			} else {
				assert.True(t, result >= 0 && result < tt.size)
			}
		})
	}
}

func TestCalculateL2Index_EdgeCases(t *testing.T) {
	SetupTestProcess(t)

	tests := []struct {
		name     string
		sid      string
		size     int
		expected int
	}{
		{
			name:     "empty sid with size 1",
			sid:      "",
			size:     1,
			expected: 0,
		},
		{
			name:     "empty sid with size 512",
			sid:      "",
			size:     512,
			expected: 0,
		},
		{
			name:     "very long sid",
			sid:      "W4054894" + strings.Repeat("0", 1000),
			size:     512,
			expected: 0,
		},
		{
			name:     "special characters in sid",
			sid:      "W4054894!@#$%^&*()",
			size:     512,
			expected: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := CalculateL2Index(tt.sid, tt.size)
			if err != nil {
				t.Errorf("CalculateL2Index() returned error: %v", err)
			} else {
				assert.True(t, result >= 0 && result < tt.size)
			}
		})
	}
}

func TestCalculateL2(t *testing.T) {
	SetupTestProcess(t)

	l2Array := []string{"abc12", "def34", "ghi56", "jkl78"}
	tests := []struct {
		name     string
		sid      string
		expected string
	}{
		{
			name:     "normal sid",
			sid:      "W4054894",
			expected: "abc12", // This will be deterministic due to FIXED_SEED
		},
		{
			name:     "empty sid",
			sid:      "",
			expected: "abc12",
		},
		{
			name:     "special characters",
			sid:      "W4054894-123",
			expected: "abc12",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := CalculateL2(tt.sid, l2Array)
			if err != nil {
				t.Errorf("CalculateL2() returned error: %v", err)
			} else {
				assert.NotEmpty(t, result)
				assert.Contains(t, l2Array, result)
			}
		})
	}
}

func TestCalculateL2_EdgeCases(t *testing.T) {
	SetupTestProcess(t)

	l2Array := []string{"abc12", "def34", "ghi56", "jkl78"}
	tests := []struct {
		name     string
		sid      string
		l2Array  []string
		expected string
	}{
		{
			name:     "empty l2Array",
			sid:      "W4054894",
			l2Array:  []string{},
			expected: "",
		},
		{
			name:     "nil l2Array",
			sid:      "W4054894",
			l2Array:  nil,
			expected: "",
		},
		{
			name:     "empty sid with valid l2Array",
			sid:      "",
			l2Array:  l2Array,
			expected: "abc12",
		},
		{
			name:     "very long sid",
			sid:      "W4054894" + strings.Repeat("0", 1000),
			l2Array:  l2Array,
			expected: "abc12",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if len(tt.l2Array) == 0 {
				result, err := CalculateL2(tt.sid, tt.l2Array)
				assert.Error(t, err)
				assert.Empty(t, result)
			} else {
				result, err := CalculateL2(tt.sid, tt.l2Array)
				if err != nil {
					t.Errorf("CalculateL2() returned error: %v", err)
				} else {
					assert.NotEmpty(t, result)
					assert.Contains(t, tt.l2Array, result)
				}
			}
		})
	}
}

func TestGetFullFilePath(t *testing.T) {
	SetupTestProcess(t)

	tests := []struct {
		name        string
		propTs      time.Time
		board       string
		sid         string
		expectError bool
	}{
		{
			name:        "valid input",
			propTs:      time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			board:       "TRB",
			sid:         "W4054894",
			expectError: false,
		},
		{
			name:        "invalid date",
			propTs:      time.Date(0, 0, 0, 0, 0, 0, 0, time.UTC),
			board:       "TRB",
			sid:         "W4054894",
			expectError: true,
		},
		{
			name:        "invalid board",
			propTs:      time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			board:       "",
			sid:         "W4054894",
			expectError: true,
		},
		{
			name:        "invalid sid",
			propTs:      time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			board:       "TRB",
			sid:         "",
			expectError: true,
		},
		{
			name:        "non-existent board",
			propTs:      time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			board:       "NONEXISTENT",
			sid:         "W4054894",
			expectError: true,
		},
		{
			name:        "date before 1970",
			propTs:      time.Date(1969, 12, 31, 23, 59, 59, 0, time.UTC),
			board:       "TRB",
			sid:         "W4054894",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			path, err := GetFullFilePathForProp(tt.propTs, tt.board, tt.sid)
			if tt.expectError {
				assert.Error(t, err)
				assert.Empty(t, path)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, path)
				assert.Contains(t, path, "/")
			}
		})
	}
}

func TestGetFullFilePath_EdgeCases(t *testing.T) {
	SetupTestProcess(t)

	tests := []struct {
		name        string
		propTs      time.Time
		board       string
		sid         string
		expectError bool
	}{
		{
			name:        "future date",
			propTs:      time.Now().AddDate(1, 0, 0),
			board:       "TRB",
			sid:         "W4054894",
			expectError: false,
		},
		{
			name:        "very old date",
			propTs:      time.Date(1900, 1, 1, 0, 0, 0, 0, time.UTC),
			board:       "TRB",
			sid:         "W4054894",
			expectError: true,
		},
		{
			name:        "very long sid",
			propTs:      time.Now(),
			board:       "TRB",
			sid:         strings.Repeat("W4054894", 100),
			expectError: false,
		},
		{
			name:        "special characters in sid",
			propTs:      time.Now(),
			board:       "TRB",
			sid:         "W4054894!@#$%^&*()",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			path, err := GetFullFilePathForProp(tt.propTs, tt.board, tt.sid)
			if tt.expectError {
				assert.Error(t, err)
				assert.Empty(t, path)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, path)
				assert.Contains(t, path, "/")
			}
		})
	}
}

func TestGetL2ListForL1(t *testing.T) {
	SetupTestProcess(t)

	tests := []struct {
		name     string
		l1Folder string
		size     int
		wantErr  bool
	}{
		{
			name:     "valid L1",
			l1Folder: "750",
			size:     512,
			wantErr:  false,
		},
		{
			name:     "invalid L1",
			l1Folder: "invalid",
			size:     512,
			wantErr:  true,
		},
		{
			name:     "zero size",
			l1Folder: "750",
			size:     0,
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := GetL2ListForL1(tt.l1Folder, tt.size)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.size, len(result))
				// Check uniqueness
				seen := make(map[string]bool)
				for _, l2 := range result {
					assert.False(t, seen[l2])
					seen[l2] = true
				}
			}
		})
	}
}

func TestGenerateAndSaveUUID5List(t *testing.T) {
	SetupTestProcess(t)

	// Create temporary file
	tmpFile, err := os.CreateTemp("", "test-uuid5-list-*.go")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	defer func() {
		if err := os.Remove(tmpFile.Name()); err != nil {
			t.Fatalf("Failed to remove temp file: %v", err)
		}
	}()

	tests := []struct {
		name     string
		filePath string
		size     int
		wantErr  bool
	}{
		{
			name:     "valid size",
			filePath: tmpFile.Name(),
			size:     512,
			wantErr:  false,
		},
		{
			name:     "invalid file path",
			filePath: "/nonexistent/path/file.go",
			size:     512,
			wantErr:  true,
		},
		{
			name:     "zero size",
			filePath: tmpFile.Name(),
			size:     0,
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := GenerateAndSaveUUID5List(tt.filePath, tt.size)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.size, len(result))
				// Check uniqueness
				seen := make(map[string]bool)
				for _, uuid := range result {
					assert.Len(t, uuid, 5)
					assert.False(t, seen[uuid])
					seen[uuid] = true
				}
			}
		})
	}
}

func TestMurmurToInt32(t *testing.T) {
	SetupTestProcess(t)

	tests := []struct {
		name     string
		key      string
		expected int32
	}{
		{
			name:     "normal string",
			key:      "test123",
			expected: -1, // This will be deterministic due to murmur3
		},
		{
			name:     "empty string",
			key:      "",
			expected: 0, // Empty string hash is 0
		},
		{
			name:     "special characters",
			key:      "!@#$%^&*()",
			expected: -1,
		},
		{
			name:     "very long string",
			key:      strings.Repeat("a", 1000),
			expected: -1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := MurmurToInt32(tt.key)
			if tt.key == "" {
				assert.Zero(t, result)
			} else {
				assert.NotZero(t, result)
			}
		})
	}
}

func TestInt32ToBase62(t *testing.T) {
	SetupTestProcess(t)

	tests := []struct {
		name     string
		input    int32
		expected string
		wantErr  bool
	}{
		{
			name:     "zero",
			input:    0,
			expected: "A",
			wantErr:  false,
		},
		{
			name:     "positive number",
			input:    12345,
			expected: "DNH",
			wantErr:  false,
		},
		{
			name:     "negative number",
			input:    -12345,
			expected: "EqpL87",
			wantErr:  false,
		},
		{
			name:     "max int32",
			input:    math.MaxInt32,
			expected: "CVUmlB",
			wantErr:  false,
		},
		{
			name:     "min int32",
			input:    math.MinInt32,
			expected: "CVUmlC",
			wantErr:  false,
		},
		{
			name:     "Single digit",
			input:    1,
			expected: "B",
			wantErr:  false,
		},
		{
			name:     "Two digits",
			input:    62,
			expected: "BA",
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := Int32ToBase62(tt.input)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestBase62ToInt32(t *testing.T) {
	SetupTestProcess(t)

	tests := []struct {
		name     string
		input    string
		expected int32
		wantErr  bool
	}{
		{
			name:     "zero",
			input:    "A",
			expected: 0,
			wantErr:  false,
		},
		{
			name:     "positive number",
			input:    "DNH",
			expected: 12345,
			wantErr:  false,
		},
		{
			name:     "negative number as large positive",
			input:    "EqpL87",
			expected: -12345,
			wantErr:  false,
		},
		{
			name:     "max int32",
			input:    "CVUmlB",
			expected: math.MaxInt32,
			wantErr:  false,
		},
		{
			name:     "min int32 as large positive",
			input:    "CVUmlC",
			expected: math.MinInt32,
			wantErr:  false,
		},
		{
			name:     "invalid character",
			input:    "3D7!",
			expected: 0,
			wantErr:  true,
		},
		{
			name:     "empty string",
			input:    "",
			expected: 0,
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := Base62ToInt32(tt.input)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

func TestInt32ToBase62AndBack(t *testing.T) {
	SetupTestProcess(t)

	tests := []struct {
		name  string
		input int32
	}{
		{
			name:  "zero",
			input: 0,
		},
		{
			name:  "positive number",
			input: 12345,
		},
		{
			name:  "negative number",
			input: -12345,
		},
		{
			name:  "max int32",
			input: math.MaxInt32,
		},
		{
			name:  "min int32",
			input: math.MinInt32,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Convert to base62
			base62, err := Int32ToBase62(tt.input)
			assert.NoError(t, err)

			// Convert back to int32
			result, err := Base62ToInt32(base62)
			assert.NoError(t, err)

			// Check if we get the same number back
			assert.Equal(t, tt.input, result)
		})
	}
}

func TestSetL2FolderFile(t *testing.T) {
	// 只需验证全局变量被设置
	path := "/tmp/test_uuid5.go"
	SetL2FolderFile(path)
	assert.Equal(t, path, defaultL2FolderFile)
}

func TestGetL2ListForL1_InvalidL1(t *testing.T) {
	SetupTestProcess(t)
	// l1Folder 不是数字
	l2, err := GetL2ListForL1("notanumber", 10)
	assert.Error(t, err)
	assert.Nil(t, l2)
}

func TestGetL2ListForL1_ZeroSize(t *testing.T) {
	SetupTestProcess(t)
	// size=0 应该返回空 slice
	l2, err := GetL2ListForL1("750", 0)
	assert.NoError(t, err)
	assert.Equal(t, 0, len(l2))
}

func TestCalculateL2Index_InvalidSize(t *testing.T) {
	SetupTestProcess(t)
	_, err := CalculateL2Index("sid", 0)
	assert.Error(t, err)
	_, err = CalculateL2Index("sid", -1)
	assert.Error(t, err)
}

func TestGetL2ListFromUUIDs(t *testing.T) {
	SetupTestProcess(t)

	tests := []struct {
		name     string
		l1Folder string
		size     int
		uuids    []string
		wantErr  bool
	}{
		{
			name:     "valid input",
			l1Folder: "750",
			size:     3,
			uuids:    []string{"abc12", "def34", "ghi56", "jkl78"},
			wantErr:  false,
		},
		{
			name:     "invalid l1Folder",
			l1Folder: "invalid",
			size:     3,
			uuids:    []string{"abc12", "def34", "ghi56", "jkl78"},
			wantErr:  true,
		},
		{
			name:     "empty uuids",
			l1Folder: "750",
			size:     3,
			uuids:    []string{},
			wantErr:  true,
		},
		{
			name:     "zero size",
			l1Folder: "750",
			size:     0,
			uuids:    []string{"abc12", "def34", "ghi56", "jkl78"},
			wantErr:  false,
		},
		{
			name:     "nil uuids",
			l1Folder: "750",
			size:     3,
			uuids:    nil,
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := getL2ListFromUUIDs(tt.l1Folder, tt.size, tt.uuids)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.size, len(result))
				// Check that all returned values are from the input uuids
				for _, l2 := range result {
					assert.Contains(t, tt.uuids, l2)
				}
			}
		})
	}
}

func TestListFiles(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "gofile_test_*")
	require.NoError(t, err)
	defer func() {
		if err := os.RemoveAll(tempDir); err != nil {
			t.Errorf("Failed to remove temporary directory: %v", err)
		}
	}()

	// Create test files and directories
	testFiles := []struct {
		path    string
		isDir   bool
		content string
	}{
		{"dir1", true, ""},
		{"dir1/test_sid_file1.txt", false, "content1"},
		{"dir1/test_sid_file2.txt", false, "content2"},
		{"dir1/other_file.txt", false, "content3"},
		{"dir2", true, ""},
		{"dir2/test_sid_file3.txt", false, "content4"},
		{"test_sid_file4.txt", false, "content5"},
		{"other_file.txt", false, "content6"},
	}

	for _, f := range testFiles {
		fullPath := filepath.Join(tempDir, f.path)
		if f.isDir {
			err := os.MkdirAll(fullPath, 0755)
			require.NoError(t, err)
		} else {
			err := os.MkdirAll(filepath.Dir(fullPath), 0755)
			require.NoError(t, err)
			err = os.WriteFile(fullPath, []byte(f.content), 0644)
			require.NoError(t, err)
		}
	}

	tests := []struct {
		name          string
		baseDir       string
		filePath      string
		sid           string
		expectedFiles []string
		expectedError bool
	}{
		{
			name:          "list root directory with sid prefix",
			baseDir:       tempDir,
			filePath:      "",
			sid:           "test_sid",
			expectedFiles: []string{"test_sid_file1.txt", "test_sid_file2.txt", "test_sid_file3.txt", "test_sid_file4.txt"},
			expectedError: false,
		},
		{
			name:          "list subdirectory with sid prefix",
			baseDir:       tempDir,
			filePath:      "dir1",
			sid:           "test_sid",
			expectedFiles: []string{"test_sid_file1.txt", "test_sid_file2.txt"},
			expectedError: false,
		},
		{
			name:          "list non-existent directory",
			baseDir:       tempDir,
			filePath:      "nonexistent",
			sid:           "test_sid",
			expectedFiles: nil,
			expectedError: true,
		},
		{
			name:          "list with non-matching sid",
			baseDir:       tempDir,
			filePath:      "",
			sid:           "nonexistent_sid",
			expectedFiles: nil,
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			files, err := ListFiles(tt.baseDir, tt.filePath, tt.sid)
			if tt.expectedError {
				assert.Error(t, err)
				assert.Nil(t, files)
			} else {
				assert.NoError(t, err)
				if tt.expectedFiles == nil {
					assert.Nil(t, files)
				} else {
					assert.NotNil(t, files)
					// Sort both slices for comparison
					sort.Strings(files)
					sort.Strings(tt.expectedFiles)
					assert.Equal(t, tt.expectedFiles, files)
				}
			}
		})
	}
}

// Test the new path format functions
func TestGetFullFilePathForProp_BackwardCompatibility(t *testing.T) {
	SetupTestProcess(t)

	propTs := time.Date(2024, 6, 15, 10, 30, 0, 0, time.UTC)
	board := "TRB"
	sid := "W4054894"

	// Test backward compatibility - should return path with leading slash
	path, err := GetFullFilePathForProp(propTs, board, sid)
	assert.NoError(t, err)
	assert.True(t, strings.HasPrefix(path, "/1223/"))
	assert.Contains(t, path, "/")
	assert.Len(t, strings.Split(path[1:], "/"), 2) // Should have exactly 2 parts after removing leading slash
}

func TestGetPathComponents(t *testing.T) {
	SetupTestProcess(t)

	propTs := time.Date(2024, 6, 15, 10, 30, 0, 0, time.UTC)
	board := "TRB"
	sid := "W4054894"

	result, err := GetPathComponents(propTs, board, sid)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, "1223", result.L1)
	assert.NotEmpty(t, result.L2)
	assert.Len(t, result.L2, 5)
	// Combined should not have leading slash
	assert.Equal(t, fmt.Sprintf("%s/%s", result.L1, result.L2), result.Combined)
	assert.False(t, strings.HasPrefix(result.Combined, "/"))
}

func TestGetL1L2Separate(t *testing.T) {
	SetupTestProcess(t)

	propTs := time.Date(2024, 6, 15, 10, 30, 0, 0, time.UTC)
	board := "TRB"
	sid := "W4054894"

	l1, l2, err := GetL1L2Separate(propTs, board, sid)
	assert.NoError(t, err)
	assert.Equal(t, "1223", l1)
	assert.NotEmpty(t, l2)
	assert.Len(t, l2, 5)
}

func TestGetPathComponents_ErrorCases(t *testing.T) {
	SetupTestProcess(t)

	propTs := time.Date(2024, 6, 15, 10, 30, 0, 0, time.UTC)
	sid := "W4054894"

	tests := []struct {
		name    string
		board   string
		wantErr bool
	}{
		{
			name:    "empty board",
			board:   "",
			wantErr: true,
		},
		{
			name:    "invalid board",
			board:   "INVALID",
			wantErr: true,
		},
		{
			name:    "valid board",
			board:   "TRB",
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := GetPathComponents(propTs, tt.board, sid)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
			}
		})
	}
}

func TestGetImageDir(t *testing.T) {
	SetupTestProcess(t)

	tests := []struct {
		name        string
		board       string
		wantDirs    []string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "Valid board with directories",
			board:       "TRB",
			wantDirs:    []string{"/tmp/trb_images", "/tmp/trb_images2"},
			expectError: false,
		},
		{
			name:        "Valid board with directories configured",
			board:       "CAR", // CAR actually has directories in test config
			wantDirs:    []string{"/tmp/car_images"},
			expectError: false,
		},
		{
			name:        "Invalid/unsupported board",
			board:       "INVALID",
			wantDirs:    nil,
			expectError: true,
			errorMsg:    "unsupported board type",
		},
		{
			name:        "Empty board name",
			board:       "",
			wantDirs:    nil,
			expectError: true,
			errorMsg:    "unsupported board type",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dirs, err := GetImageDir(tt.board)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
				assert.Nil(t, dirs)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.wantDirs, dirs)
			}
		})
	}
}

func TestGetImageDir_ErrorCases(t *testing.T) {
	SetupTestProcess(t)

	tests := []struct {
		name        string
		board       string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "Unsupported board type",
			board:       "UNSUPPORTED",
			expectError: true,
			errorMsg:    "unsupported board type: UNSUPPORTED",
		},
		{
			name:        "Empty board name",
			board:       "",
			expectError: true,
			errorMsg:    "unsupported board type:",
		},
		{
			name:        "Valid board with config",
			board:       "CAR", // CAR has directories in test config
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			dirs, err := GetImageDir(tt.board)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorMsg != "" {
					assert.Contains(t, err.Error(), tt.errorMsg)
				}
				assert.Nil(t, dirs)
			} else {
				assert.NoError(t, err)
				// For valid boards, dirs can be nil (no config) or have values
				// This is not an error condition
			}
		})
	}
}

func TestGetBoardNames(t *testing.T) {
	boardNames := getBoardNames()

	// Should contain all boards from BoardImageDirName
	expectedBoards := []string{"CAR", "DDF", "BRE", "EDM", "TRB"}

	assert.Len(t, boardNames, len(expectedBoards))
	for _, expectedBoard := range expectedBoards {
		assert.Contains(t, boardNames, expectedBoard)
	}
}
