// Generated by tomltest<PERSON> for toml-test ref master on 2023-10-26T19:48:52+02:00
package toml_test

import (
	"testing"
)

func TestTOMLTest_Invalid_Tests_Invalid_Array_DoubleComma1(t *testing.T) {
	input := "double-comma-1 = [1,,2]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_DoubleComma2(t *testing.T) {
	input := "double-comma-2 = [1,2,,]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_ExtendDefinedAot(t *testing.T) {
	input := "[[tab.arr]]\n[tab]\narr.val1=1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_ExtendingTable(t *testing.T) {
	input := "a = [{ b = 1 }]\n\n# Cannot extend tables within static arrays\n# https://github.com/toml-lang/toml/issues/908\n[a.c]\nfoo = 1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_MissingSeparator1(t *testing.T) {
	input := "arrr = [true false]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_MissingSeparator2(t *testing.T) {
	input := "wrong = [ 1 2 3 ]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_NoClose1(t *testing.T) {
	input := "no-close-1 = [ 1, 2, 3\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_NoClose2(t *testing.T) {
	input := "no-close-2 = [1,\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_NoClose3(t *testing.T) {
	input := "no-close-3 = [42 #]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_NoClose4(t *testing.T) {
	input := "no-close-4 = [{ key = 42\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_NoClose5(t *testing.T) {
	input := "no-close-5 = [{ key = 42}\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_NoClose6(t *testing.T) {
	input := "no-close-6 = [{ key = 42 #}]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_NoClose7(t *testing.T) {
	input := "no-close-7 = [{ key = 42} #]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_NoClose8(t *testing.T) {
	input := "no-close-8 = [\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_NoCloseTable1(t *testing.T) {
	input := "x = [{ key = 42\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_NoCloseTable2(t *testing.T) {
	input := "x = [{ key = 42 #\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_NoComma1(t *testing.T) {
	input := "no-comma-1 = [true false]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_NoComma2(t *testing.T) {
	input := "no-comma-2 = [ 1 2 3 ]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_NoComma3(t *testing.T) {
	input := "no-comma-3 = [ 1 #,]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_OnlyComma1(t *testing.T) {
	input := "only-comma-1 = [,]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_OnlyComma2(t *testing.T) {
	input := "only-comma-2 = [,,]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_Tables1(t *testing.T) {
	input := "# INVALID TOML DOC\nfruit = []\n\n[[fruit]] # Not allowed\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_Tables2(t *testing.T) {
	input := "# INVALID TOML DOC\n[[fruit]]\n  name = \"apple\"\n\n  [[fruit.variety]]\n    name = \"red delicious\"\n\n  # This table conflicts with the previous table\n  [fruit.variety]\n    name = \"granny smith\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_TextAfterArrayEntries(t *testing.T) {
	input := "array = [\n  \"Is there life after an array separator?\", No\n  \"Entry\"\n]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_TextBeforeArraySeparator(t *testing.T) {
	input := "array = [\n  \"Is there life before an array separator?\" No,\n  \"Entry\"\n]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Array_TextInArray(t *testing.T) {
	input := "array = [\n  \"Entry 1\",\n  I don't belong,\n  \"Entry 2\",\n]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Bool_AlmostFalseWithExtra(t *testing.T) {
	input := "almost-false-with-extra = falsify\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Bool_AlmostFalse(t *testing.T) {
	input := "almost-false            = fals\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Bool_AlmostTrueWithExtra(t *testing.T) {
	input := "almost-true-with-extra  = truthy\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Bool_AlmostTrue(t *testing.T) {
	input := "almost-true             = tru\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Bool_CapitalizedFalse(t *testing.T) {
	input := "capitalized-false        = False\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Bool_CapitalizedTrue(t *testing.T) {
	input := "capitalized-true         = True\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Bool_JustF(t *testing.T) {
	input := "just-f                  = f\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Bool_JustT(t *testing.T) {
	input := "just-t                  = t\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Bool_MixedCaseFalse(t *testing.T) {
	input := "mixed-case-false        = falsE\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Bool_MixedCaseTrue(t *testing.T) {
	input := "mixed-case-true         = trUe\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Bool_MixedCase(t *testing.T) {
	input := "mixed-case              = valid   = False\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Bool_StartingSameFalse(t *testing.T) {
	input := "starting-same-false     = falsey\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Bool_StartingSameTrue(t *testing.T) {
	input := "starting-same-true      = truer\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Bool_WrongCaseFalse(t *testing.T) {
	input := "wrong-case-false        = FALSE\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Bool_WrongCaseTrue(t *testing.T) {
	input := "wrong-case-true         = TRUE\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_BareCr(t *testing.T) {
	input := "# The following line contains a single carriage return control character\r\n\r"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_BareFormfeed(t *testing.T) {
	input := "bare-formfeed     = \f\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_BareNull(t *testing.T) {
	input := "bare-null         = \"some value\" \x00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_BareVerticalTab(t *testing.T) {
	input := "bare-vertical-tab = \v\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_CommentCr(t *testing.T) {
	input := "comment-cr   = \"Carriage return in comment\" # \ra=1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_CommentDel(t *testing.T) {
	input := "comment-del  = \"0x7f\"   # \x7f\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_CommentFf(t *testing.T) {
	input := "comment-ff   = \"0x7f\"   # \f\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_CommentLf(t *testing.T) {
	input := "comment-lf   = \"ctrl-P\" # \x10\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_CommentNull(t *testing.T) {
	input := "comment-null = \"null\"   # \x00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_CommentUs(t *testing.T) {
	input := "comment-us   = \"ctrl-_\" # \x1f\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_MultiCr(t *testing.T) {
	input := "multi-cr   = \"\"\"null\r\"\"\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_MultiDel(t *testing.T) {
	input := "multi-del  = \"\"\"null\x7f\"\"\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_MultiLf(t *testing.T) {
	input := "multi-lf   = \"\"\"null\x10\"\"\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_MultiNull(t *testing.T) {
	input := "multi-null = \"\"\"null\x00\"\"\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_MultiUs(t *testing.T) {
	input := "multi-us   = \"\"\"null\x1f\"\"\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_RawmultiCd(t *testing.T) {
	input := "rawmulti-cd   = '''null\r'''\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_RawmultiDel(t *testing.T) {
	input := "rawmulti-del  = '''null\x7f'''\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_RawmultiLf(t *testing.T) {
	input := "rawmulti-lf   = '''null\x10'''\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_RawmultiNull(t *testing.T) {
	input := "rawmulti-null = '''null\x00'''\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_RawmultiUs(t *testing.T) {
	input := "rawmulti-us   = '''null\x1f'''\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_RawstringCr(t *testing.T) {
	input := "rawstring-cr   = 'null\r'\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_RawstringDel(t *testing.T) {
	input := "rawstring-del  = 'null\x7f'\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_RawstringLf(t *testing.T) {
	input := "rawstring-lf   = 'null\x10'\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_RawstringNull(t *testing.T) {
	input := "rawstring-null = 'null\x00'\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_RawstringUs(t *testing.T) {
	input := "rawstring-us   = 'null\x1f'\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_StringBs(t *testing.T) {
	input := "string-bs   = \"backspace\b\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_StringCr(t *testing.T) {
	input := "string-cr   = \"null\r\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_StringDel(t *testing.T) {
	input := "string-del  = \"null\x7f\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_StringLf(t *testing.T) {
	input := "string-lf   = \"null\x10\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_StringNull(t *testing.T) {
	input := "string-null = \"null\x00\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Control_StringUs(t *testing.T) {
	input := "string-us   = \"null\x1f\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Datetime_Feb29(t *testing.T) {
	input := "\"not a leap year\" = 2100-02-29T15:15:15Z\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Datetime_Feb30(t *testing.T) {
	input := "\"only 28 or 29 days in february\" = 1988-02-30T15:15:15Z\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Datetime_HourOver(t *testing.T) {
	input := "# time-hour       = 2DIGIT  ; 00-23\nd = 2006-01-01T24:00:00-00:00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Datetime_MdayOver(t *testing.T) {
	input := "# date-mday       = 2DIGIT  ; 01-28, 01-29, 01-30, 01-31 based on\n#                           ; month/year\nd = 2006-01-32T00:00:00-00:00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Datetime_MdayUnder(t *testing.T) {
	input := "# date-mday       = 2DIGIT  ; 01-28, 01-29, 01-30, 01-31 based on\n#                           ; month/year\nd = 2006-01-00T00:00:00-00:00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Datetime_MinuteOver(t *testing.T) {
	input := "# time-minute     = 2DIGIT  ; 00-59\nd = 2006-01-01T00:60:00-00:00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Datetime_MonthOver(t *testing.T) {
	input := "# date-month      = 2DIGIT  ; 01-12\nd = 2006-13-01T00:00:00-00:00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Datetime_MonthUnder(t *testing.T) {
	input := "# date-month      = 2DIGIT  ; 01-12\nd = 2007-00-01T00:00:00-00:00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Datetime_NoLeadsMonth(t *testing.T) {
	input := "# Month \"7\" instead of \"07\"; the leading zero is required.\nno-leads = 1987-7-05T17:45:00Z\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Datetime_NoLeadsWithMilli(t *testing.T) {
	input := "# Day \"5\" instead of \"05\"; the leading zero is required.\nwith-milli = 1987-07-5T17:45:00.12Z\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Datetime_NoLeads(t *testing.T) {
	input := "# Month \"7\" instead of \"07\"; the leading zero is required.\nno-leads = 1987-7-05T17:45:00Z\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Datetime_NoSecs(t *testing.T) {
	input := "# No seconds in time.\nno-secs = 1987-07-05T17:45Z\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Datetime_NoT(t *testing.T) {
	input := "# No \"t\" or \"T\" between the date and time.\nno-t = 1987-07-0517:45:00Z\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Datetime_SecondOver(t *testing.T) {
	input := "# time-second     = 2DIGIT  ; 00-58, 00-59, 00-60 based on leap second\n#                           ; rules\nd = 2006-01-01T00:00:61-00:00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Datetime_TimeNoLeads(t *testing.T) {
	input := "# Leading 0 is always required.\nd = 2023-10-01T1:32:00Z\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Encoding_BadCodepoint(t *testing.T) {
	input := "# Invalid codepoint U+D800 : \xed\xa0\x80\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Encoding_BadUtf8AtEnd(t *testing.T) {
	input := "# There is a 0xda at after the quotes, and no EOL at the end of the file.\n#\n# This is a bit of an edge case: This indicates there should be two bytes\n# (0b1101_1010) but there is no byte to follow because it's the end of the file.\nx = \"\"\"\"\"\"\xda"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Encoding_BadUtf8InComment(t *testing.T) {
	input := "# \xc3\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Encoding_BadUtf8InMultilineLiteral(t *testing.T) {
	input := "# The following line contains an invalid UTF-8 sequence.\nbad = '''\xc3'''\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Encoding_BadUtf8InMultiline(t *testing.T) {
	input := "# The following line contains an invalid UTF-8 sequence.\nbad = \"\"\"\xc3\"\"\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Encoding_BadUtf8InStringLiteral(t *testing.T) {
	input := "# The following line contains an invalid UTF-8 sequence.\nbad = '\xc3'\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Encoding_BadUtf8InString(t *testing.T) {
	input := "# The following line contains an invalid UTF-8 sequence.\nbad = \"\xc3\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Encoding_BomNotAtStart1(t *testing.T) {
	input := "bom-not-at-start \xff\xfd\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Encoding_BomNotAtStart2(t *testing.T) {
	input := "bom-not-at-start= \xff\xfd\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Encoding_Utf16Bom(t *testing.T) {
	input := "\xfe\xff\x00#\x00 \x00U\x00T\x00F\x00-\x001\x006\x00 \x00w\x00i\x00t\x00h\x00 \x00B\x00O\x00M\x00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Encoding_Utf16(t *testing.T) {
	input := "\x00#\x00 \x00U\x00T\x00F\x00-\x001\x006\x00 \x00w\x00i\x00t\x00h\x00o\x00u\x00t\x00 \x00B\x00O\x00M\x00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_DoublePoint1(t *testing.T) {
	input := "double-point-1 = 0..1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_DoublePoint2(t *testing.T) {
	input := "double-point-2 = 0.1.2\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_ExpDoubleE1(t *testing.T) {
	input := "exp-double-e-1 = 1ee2\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_ExpDoubleE2(t *testing.T) {
	input := "exp-double-e-2 = 1e2e3\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_ExpDoubleUs(t *testing.T) {
	input := "exp-double-us = 1e__23\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_ExpLeadingUs(t *testing.T) {
	input := "exp-leading-us = 1e_23\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_ExpPoint1(t *testing.T) {
	input := "exp-point-1 = 1e2.3\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_ExpPoint2(t *testing.T) {
	input := "exp-point-2 = 1.e2\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_ExpTrailingUs(t *testing.T) {
	input := "exp-trailing-us = 1e23_\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_InfCapital(t *testing.T) {
	input := "v = Inf\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_InfIncomplete1(t *testing.T) {
	input := "inf-incomplete-1 = in\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_InfIncomplete2(t *testing.T) {
	input := "inf-incomplete-2 = +in\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_InfIncomplete3(t *testing.T) {
	input := "inf-incomplete-3 = -in\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_Inf_underscore(t *testing.T) {
	input := "inf_underscore = in_f\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_LeadingPointNeg(t *testing.T) {
	input := "leading-point-neg = -.12345\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_LeadingPointPlus(t *testing.T) {
	input := "leading-point-plus = +.12345\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_LeadingPoint(t *testing.T) {
	input := "leading-point = .12345\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_LeadingUs(t *testing.T) {
	input := "leading-us = _1.2\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_LeadingZeroNeg(t *testing.T) {
	input := "leading-zero-neg = -03.14\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_LeadingZeroPlus(t *testing.T) {
	input := "leading-zero-plus = +03.14\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_LeadingZero(t *testing.T) {
	input := "leading-zero = 03.14\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_NanCapital(t *testing.T) {
	input := "v = NaN\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_NanIncomplete1(t *testing.T) {
	input := "nan-incomplete-1 = na\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_NanIncomplete2(t *testing.T) {
	input := "nan-incomplete-2 = +na\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_NanIncomplete3(t *testing.T) {
	input := "nan-incomplete-3 = -na\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_Nan_underscore(t *testing.T) {
	input := "nan_underscore = na_n\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_TrailingPointMin(t *testing.T) {
	input := "trailing-point-min = -1.\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_TrailingPointPlus(t *testing.T) {
	input := "trailing-point-plus = +1.\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_TrailingPoint(t *testing.T) {
	input := "trailing-point = 1.\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_TrailingUsExp1(t *testing.T) {
	input := "trailing-us-exp-1 = 1_e2\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_TrailingUsExp2(t *testing.T) {
	input := "trailing-us-exp-2 = 1.2_e2\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_TrailingUs(t *testing.T) {
	input := "trailing-us = 1.2_\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_UsAfterPoint(t *testing.T) {
	input := "us-after-point = 1._2\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Float_UsBeforePoint(t *testing.T) {
	input := "us-before-point = 1_.2\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_BadKeySyntax(t *testing.T) {
	input := "tbl = { a = 1, [b] }\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_DoubleComma(t *testing.T) {
	input := "t = {x=3,,y=4}\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_DuplicateKey1(t *testing.T) {
	input := "# Duplicate keys within an inline table are invalid\na={b=1, b=2}\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_DuplicateKey2(t *testing.T) {
	input := "table1 = { table2.dupe = 1, table2.dupe = 2 }\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_DuplicateKey3(t *testing.T) {
	input := "tbl = { fruit = { apple.color = \"red\" }, fruit.apple.texture = { smooth = true } }\n\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_DuplicateKey4(t *testing.T) {
	input := "tbl = { a.b = \"a_b\", a.b.c = \"a_b_c\" }\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_Empty1(t *testing.T) {
	input := "t = {,}\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_Empty2(t *testing.T) {
	input := "t = {,\n}\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_Empty3(t *testing.T) {
	input := "t = {\n,\n}\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_Linebreak1(t *testing.T) {
	input := "# No newlines are allowed between the curly braces unless they are valid within\n# a value.\nsimple = { a = 1 \n}\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_Linebreak2(t *testing.T) {
	input := "t = {a=1,\nb=2}\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_Linebreak3(t *testing.T) {
	input := "t = {a=1\n,b=2}\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_Linebreak4(t *testing.T) {
	input := "json_like = {\n          first = \"Tom\",\n          last = \"Preston-Werner\"\n}\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_NoClose1(t *testing.T) {
	input := "a={\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_NoClose2(t *testing.T) {
	input := "a={b=1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_NoComma1(t *testing.T) {
	input := "t = {x = 3 y = 4}\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_NoComma2(t *testing.T) {
	input := "arrr = { comma-missing = true valid-toml = false }\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_Overwrite1(t *testing.T) {
	input := "a.b=0\n# Since table \"a\" is already defined, it can't be replaced by an inline table.\na={}\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_Overwrite2(t *testing.T) {
	input := "a={}\n# Inline tables are immutable and can't be extended\n[a.b]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_Overwrite3(t *testing.T) {
	input := "a = { b = 1 }\na.b = 2\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_Overwrite4(t *testing.T) {
	input := "inline-t = { nest = {} }\n\n[[inline-t.nest]]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_Overwrite5(t *testing.T) {
	input := "inline-t = { nest = {} }\n\n[inline-t.nest]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_Overwrite6(t *testing.T) {
	input := "a = { b = 1, b.c = 2 }\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_Overwrite7(t *testing.T) {
	input := "tab = { inner.table = [{}], inner.table.val = \"bad\" }"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_Overwrite8(t *testing.T) {
	input := "tab = { inner = { dog = \"best\" }, inner.cat = \"worst\" }"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_Overwrite9(t *testing.T) {
	input := "[tab.nested]\ninline-t = { nest = {} }\n\n[tab]\nnested.inline-t.nest = 2\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_InlineTable_TrailingComma(t *testing.T) {
	input := "# A terminating comma (also called trailing comma) is not permitted after the\n# last key/value pair in an inline table\nabc = { abc = 123, }\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_CapitalBin(t *testing.T) {
	input := "capital-bin = 0B0\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_CapitalHex(t *testing.T) {
	input := "capital-hex = 0X1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_CapitalOct(t *testing.T) {
	input := "capital-oct = 0O0\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_DoubleSignNex(t *testing.T) {
	input := "double-sign-nex = --99\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_DoubleSignPlus(t *testing.T) {
	input := "double-sign-plus = ++99\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_DoubleUs(t *testing.T) {
	input := "double-us = 1__23\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_IncompleteBin(t *testing.T) {
	input := "incomplete-bin = 0b\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_IncompleteHex(t *testing.T) {
	input := "incomplete-hex = 0x\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_IncompleteOct(t *testing.T) {
	input := "incomplete-oct = 0o\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_InvalidBin(t *testing.T) {
	input := "invalid-bin = 0b0012\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_InvalidHex1(t *testing.T) {
	input := "invalid-hex-1 = 0xaafz\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_InvalidHex2(t *testing.T) {
	input := "invalid-hex-2 = 0xgabba00f1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_InvalidHex(t *testing.T) {
	input := "invalid-hex = 0xaafz\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_InvalidOct(t *testing.T) {
	input := "invalid-oct = 0o778\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_LeadingUsBin(t *testing.T) {
	input := "leading-us-bin = _0b1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_LeadingUsHex(t *testing.T) {
	input := "leading-us-hex = _0x1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_LeadingUsOct(t *testing.T) {
	input := "leading-us-oct = _0o1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_LeadingUs(t *testing.T) {
	input := "leading-us = _123\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_LeadingZero1(t *testing.T) {
	input := "leading-zero-1 = 01\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_LeadingZero2(t *testing.T) {
	input := "leading-zero-2 = 00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_LeadingZero3(t *testing.T) {
	input := "leading-zero-3 = 0_0\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_LeadingZeroSign1(t *testing.T) {
	input := "leading-zero-sign-1 = -01\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_LeadingZeroSign2(t *testing.T) {
	input := "leading-zero-sign-2 = +01\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_LeadingZeroSign3(t *testing.T) {
	input := "leading-zero-sign-3 = +0_1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_NegativeBin(t *testing.T) {
	input := "negative-bin = -0b11010110\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_NegativeHex(t *testing.T) {
	input := "negative-hex = -0xff\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_NegativeOct(t *testing.T) {
	input := "negative-oct = -0o755\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_PositiveBin(t *testing.T) {
	input := "positive-bin = +0b11010110\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_PositiveHex(t *testing.T) {
	input := "positive-hex = +0xff\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_PositiveOct(t *testing.T) {
	input := "positive-oct = +0o755\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_TextAfterInteger(t *testing.T) {
	input := "answer = 42 the ultimate answer?\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_TrailingUsBin(t *testing.T) {
	input := "trailing-us-bin = 0b1_\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_TrailingUsHex(t *testing.T) {
	input := "trailing-us-hex = 0x1_\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_TrailingUsOct(t *testing.T) {
	input := "trailing-us-oct = 0o1_\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_TrailingUs(t *testing.T) {
	input := "trailing-us = 123_\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_UsAfterBin(t *testing.T) {
	input := "us-after-bin = 0b_1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_UsAfterHex(t *testing.T) {
	input := "us-after-hex = 0x_1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Integer_UsAfterOct(t *testing.T) {
	input := "us-after-oct = 0o_1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_AfterArray(t *testing.T) {
	input := "[[agencies]] owner = \"S Cjelli\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_AfterTable(t *testing.T) {
	input := "[error] this = \"should not be here\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_AfterValue(t *testing.T) {
	input := "first = \"Tom\" last = \"Preston-Werner\" # INVALID\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_BareInvalidCharacter(t *testing.T) {
	input := "bare!key = 123\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_DottedRedefineTable1(t *testing.T) {
	input := "a = false\na.b = true\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_DottedRedefineTable2(t *testing.T) {
	input := "# Defined a.b as int\na.b = 1\n# Tries to access it as table: error\na.b.c = 2\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_DuplicateKeys(t *testing.T) {
	input := "dupe = false\ndupe = true\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_Duplicate(t *testing.T) {
	input := "# DO NOT DO THIS\nname = \"Tom\"\nname = \"Pradyun\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_Empty(t *testing.T) {
	input := " = 1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_EndInEscape(t *testing.T) {
	input := "\"backslash is the last char\\\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_Escape(t *testing.T) {
	input := "\\u00c0 = \"latin capital letter A with grave\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_Hash(t *testing.T) {
	input := "a# = 1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_Multiline(t *testing.T) {
	input := "\"\"\"long\nkey\"\"\" = 1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_Newline(t *testing.T) {
	input := "barekey\n   = 123\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_NoEol(t *testing.T) {
	input := "a = 1 b = 2\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_OpenBracket(t *testing.T) {
	input := "[abc = 1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_PartialQuoted(t *testing.T) {
	input := "partial\"quoted\" = 5\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_QuotedUnclosed1(t *testing.T) {
	input := "\"key = x\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_QuotedUnclosed2(t *testing.T) {
	input := "\"key\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_SingleOpenBracket(t *testing.T) {
	input := "[\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_Space(t *testing.T) {
	input := "a b = 1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_SpecialCharacter(t *testing.T) {
	input := "μ = \"greek small letter mu\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_StartBracket(t *testing.T) {
	input := "[a]\n[xyz = 5\n[b]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_StartDot(t *testing.T) {
	input := ".key = 1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_TwoEquals(t *testing.T) {
	input := "key= = 1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_TwoEquals2(t *testing.T) {
	input := "a==1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_TwoEquals3(t *testing.T) {
	input := "a=b=1\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_WithoutValue1(t *testing.T) {
	input := "key\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_WithoutValue2(t *testing.T) {
	input := "key = \n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_WithoutValue3(t *testing.T) {
	input := "\"key\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_WithoutValue4(t *testing.T) {
	input := "\"key\" = \n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_WithoutValue5(t *testing.T) {
	input := "fs.fw\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_WithoutValue6(t *testing.T) {
	input := "fs.fw =\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Key_WithoutValue7(t *testing.T) {
	input := "fs.\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDate_Feb29(t *testing.T) {
	input := "\"not a leap year\" = 2100-02-29\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDate_Feb30(t *testing.T) {
	input := "\"only 28 or 29 days in february\" = 1988-02-30\n\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDate_MdayOver(t *testing.T) {
	input := "# date-mday       = 2DIGIT  ; 01-28, 01-29, 01-30, 01-31 based on\n#                           ; month/year\nd = 2006-01-32\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDate_MdayUnder(t *testing.T) {
	input := "# date-mday       = 2DIGIT  ; 01-28, 01-29, 01-30, 01-31 based on\n#                           ; month/year\nd = 2006-01-00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDate_MonthOver(t *testing.T) {
	input := "# date-month      = 2DIGIT  ; 01-12\nd = 2006-13-01\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDate_MonthUnder(t *testing.T) {
	input := "# date-month      = 2DIGIT  ; 01-12\nd = 2007-00-01\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDate_NoLeadsWithMilli(t *testing.T) {
	input := "# Day \"5\" instead of \"05\"; the leading zero is required.\nwith-milli = 1987-07-5\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDate_NoLeads(t *testing.T) {
	input := "# Month \"7\" instead of \"07\"; the leading zero is required.\nno-leads = 1987-7-05\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDate_TrailingT(t *testing.T) {
	input := "# Date cannot end with trailing T\nd = 2006-01-30T\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDatetime_Feb29(t *testing.T) {
	input := "\"not a leap year\" = 2100-02-29T15:15:15\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDatetime_Feb30(t *testing.T) {
	input := "\"only 28 or 29 days in february\" = 1988-02-30T15:15:15\n\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDatetime_HourOver(t *testing.T) {
	input := "# time-hour       = 2DIGIT  ; 00-23\nd = 2006-01-01T24:00:00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDatetime_MdayOver(t *testing.T) {
	input := "# date-mday       = 2DIGIT  ; 01-28, 01-29, 01-30, 01-31 based on\n#                           ; month/year\nd = 2006-01-32T00:00:00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDatetime_MdayUnder(t *testing.T) {
	input := "# date-mday       = 2DIGIT  ; 01-28, 01-29, 01-30, 01-31 based on\n#                           ; month/year\nd = 2006-01-00T00:00:00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDatetime_MinuteOver(t *testing.T) {
	input := "# time-minute     = 2DIGIT  ; 00-59\nd = 2006-01-01T00:60:00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDatetime_MonthOver(t *testing.T) {
	input := "# date-month      = 2DIGIT  ; 01-12\nd = 2006-13-01T00:00:00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDatetime_MonthUnder(t *testing.T) {
	input := "# date-month      = 2DIGIT  ; 01-12\nd = 2007-00-01T00:00:00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDatetime_NoLeadsWithMilli(t *testing.T) {
	input := "# Day \"5\" instead of \"05\"; the leading zero is required.\nwith-milli = 1987-07-5T17:45:00.12\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDatetime_NoLeads(t *testing.T) {
	input := "# Month \"7\" instead of \"07\"; the leading zero is required.\nno-leads = 1987-7-05T17:45:00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDatetime_NoSecs(t *testing.T) {
	input := "# No seconds in time.\nno-secs = 1987-07-05T17:45\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDatetime_NoT(t *testing.T) {
	input := "# No \"t\" or \"T\" between the date and time.\nno-t = 1987-07-0517:45:00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDatetime_SecondOver(t *testing.T) {
	input := "# time-second     = 2DIGIT  ; 00-58, 00-59, 00-60 based on leap second\n#                           ; rules\nd = 2006-01-01T00:00:61\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalDatetime_TimeNoLeads(t *testing.T) {
	input := "# Leading 0 is always required.\nd = 2023-10-01T1:32:00Z\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalTime_HourOver(t *testing.T) {
	input := "# time-hour       = 2DIGIT  ; 00-23\nd = 24:00:00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalTime_MinuteOver(t *testing.T) {
	input := "# time-minute     = 2DIGIT  ; 00-59\nd = 00:60:00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalTime_NoSecs(t *testing.T) {
	input := "# No seconds in time.\nno-secs = 17:45\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalTime_SecondOver(t *testing.T) {
	input := "# time-second     = 2DIGIT  ; 00-58, 00-59, 00-60 based on leap second\n#                           ; rules\nd = 00:00:61\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalTime_TimeNoLeads2(t *testing.T) {
	input := "# Leading 0 is always required.\nd = 01:32:0\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_LocalTime_TimeNoLeads(t *testing.T) {
	input := "# Leading 0 is always required.\nd = 1:32:00\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Spec_InlineTable20(t *testing.T) {
	input := "[product]\ntype = { name = \"Nail\" }\ntype.edible = false  # INVALID\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Spec_InlineTable30(t *testing.T) {
	input := "[product]\ntype.name = \"Nail\"\ntype = { edible = false }  # INVALID\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Spec_KeyValuePair1(t *testing.T) {
	input := "key = # INVALID\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Spec_Keys2(t *testing.T) {
	input := "= \"no key name\"  # INVALID\n\"\" = \"blank\"     # VALID but discouraged\n'' = 'blank'     # VALID but discouraged\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Spec_String40(t *testing.T) {
	input := "str4 = \"\"\"Here are two quotation marks: \"\". Simple enough.\"\"\"\nstr5 = \"\"\"Here are three quotation marks: \"\"\".\"\"\"  # INVALID\nstr5 = \"\"\"Here are three quotation marks: \"\"\\\".\"\"\"\nstr6 = \"\"\"Here are fifteen quotation marks: \"\"\\\"\"\"\\\"\"\"\\\"\"\"\\\"\"\"\\\".\"\"\"\n\n# \"This,\" she said, \"is just a pointless statement.\"\nstr7 = \"\"\"\"This,\" she said, \"is just a pointless statement.\"\"\"\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Spec_String70(t *testing.T) {
	input := "quot15 = '''Here are fifteen quotation marks: \"\"\"\"\"\"\"\"\"\"\"\"\"\"\"'''\n\napos15 = '''Here are fifteen apostrophes: ''''''''''''''''''  # INVALID\napos15 = \"Here are fifteen apostrophes: '''''''''''''''\"\n\n# 'That,' she said, 'is still pointless.'\nstr = ''''That,' she said, 'is still pointless.''''\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Spec_Table90(t *testing.T) {
	input := "[fruit]\napple.color = \"red\"\napple.taste.sweet = true\n\n[fruit.apple]  # INVALID\n# [fruit.apple.taste]  # INVALID\n\n[fruit.apple.texture]  # you can add sub-tables\nsmooth = true\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Spec_Table91(t *testing.T) {
	input := "[fruit]\napple.color = \"red\"\napple.taste.sweet = true\n\n# [fruit.apple]  # INVALID\n[fruit.apple.taste]  # INVALID\n\n[fruit.apple.texture]  # you can add sub-tables\nsmooth = true\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BadByteEscape(t *testing.T) {
	input := "naughty = \"\\xAg\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BadConcat(t *testing.T) {
	input := "no_concat = \"first\" \"second\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BadEscape1(t *testing.T) {
	input := "invalid-escape = \"This string has a bad \\a escape character.\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BadEscape2(t *testing.T) {
	input := "invalid-escape = \"This string has a bad \\  escape character.\"\n\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BadEscape3(t *testing.T) {
	input := "backslash = \"\\\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BadHexEsc1(t *testing.T) {
	input := "bad-hex-esc-1 = \"\\x0g\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BadHexEsc2(t *testing.T) {
	input := "bad-hex-esc-2 = \"\\xG0\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BadHexEsc3(t *testing.T) {
	input := "bad-hex-esc-3 = \"\\x\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BadHexEsc4(t *testing.T) {
	input := "bad-hex-esc-4 = \"\\x 50\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BadHexEsc5(t *testing.T) {
	input := "bad-hex-esc-5 = \"\\x 50\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BadMultiline(t *testing.T) {
	input := "multi = \"first line\nsecond line\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BadSlashEscape(t *testing.T) {
	input := "invalid-escape = \"This string has a bad \\/ escape character.\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BadUniEsc1(t *testing.T) {
	input := "bad-uni-esc-1 = \"val\\ue\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BadUniEsc2(t *testing.T) {
	input := "bad-uni-esc-2 = \"val\\Ux\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BadUniEsc3(t *testing.T) {
	input := "bad-uni-esc-3 = \"val\\U0000000\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BadUniEsc4(t *testing.T) {
	input := "bad-uni-esc-4 = \"val\\U0000\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BadUniEsc5(t *testing.T) {
	input := "bad-uni-esc-5 = \"val\\Ugggggggg\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BadUniEsc6(t *testing.T) {
	input := "bad-uni-esc-6 = \"This string contains a non scalar unicode codepoint \\uD801\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BadUniEsc7(t *testing.T) {
	input := "bad-uni-esc-7 = \"\\uabag\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BasicByteEscapes(t *testing.T) {
	input := "answer = \"\\x33\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BasicMultilineOutOfRangeUnicodeEscape1(t *testing.T) {
	input := "a = \"\"\"\\UFFFFFFFF\"\"\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BasicMultilineOutOfRangeUnicodeEscape2(t *testing.T) {
	input := "a = \"\"\"\\U00D80000\"\"\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BasicMultilineQuotes(t *testing.T) {
	input := "str5 = \"\"\"Here are three quotation marks: \"\"\".\"\"\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BasicMultilineUnknownEscape(t *testing.T) {
	input := "a = \"\"\"\\@\"\"\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BasicOutOfRangeUnicodeEscape1(t *testing.T) {
	input := "a = \"\\UFFFFFFFF\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BasicOutOfRangeUnicodeEscape2(t *testing.T) {
	input := "a = \"\\U00D80000\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_BasicUnknownEscape(t *testing.T) {
	input := "a = \"\\@\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_LiteralMultilineQuotes1(t *testing.T) {
	input := "a = '''6 apostrophes: ''''''\n\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_LiteralMultilineQuotes2(t *testing.T) {
	input := "a = '''15 apostrophes: ''''''''''''''''''\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_MissingQuotes(t *testing.T) {
	input := "name = value\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_MultilineBadEscape1(t *testing.T) {
	input := "k = \"\"\"t\\a\"\"\"\n\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_MultilineBadEscape2(t *testing.T) {
	input := "# \\<Space> is not a valid escape.\nk = \"\"\"t\\ t\"\"\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_MultilineBadEscape3(t *testing.T) {
	input := "# \\<Space> is not a valid escape.\nk = \"\"\"t\\ \"\"\"\n\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_MultilineBadEscape4(t *testing.T) {
	input := "backslash = \"\"\"\\\"\"\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_MultilineEscapeSpace1(t *testing.T) {
	input := "a = \"\"\"\n  foo \\ \\n\n  bar\"\"\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_MultilineEscapeSpace2(t *testing.T) {
	input := "bee = \"\"\"\nhee \\\n\ngee \\   \"\"\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_MultilineLitNoClose1(t *testing.T) {
	input := "invalid = '''\n    this will fail\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_MultilineLitNoClose2(t *testing.T) {
	input := "x='''\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_MultilineLitNoClose3(t *testing.T) {
	input := "not-closed= '''\ndiibaa\nblibae ete\neteta\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_MultilineLitNoClose4(t *testing.T) {
	input := "bee = '''\nhee\ngee ''\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_MultilineNoClose1(t *testing.T) {
	input := "invalid = \"\"\"\n    this will fail\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_MultilineNoClose2(t *testing.T) {
	input := "x=\"\"\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_MultilineNoClose3(t *testing.T) {
	input := "not-closed= \"\"\"\ndiibaa\nblibae ete\neteta\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_MultilineNoClose4(t *testing.T) {
	input := "bee = \"\"\"\nhee\ngee \"\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_MultilineNoClose5(t *testing.T) {
	input := "bee = \"\"\"\nhee\ngee\\\t \n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_MultilineQuotes1(t *testing.T) {
	input := "a = \"\"\"6 quotes: \"\"\"\"\"\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_NoClose1(t *testing.T) {
	input := "no-ending-quote = \"One time, at band camp\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_NoClose2(t *testing.T) {
	input := "\"a-string\".must-be = \"closed\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_NoClose3(t *testing.T) {
	input := "no-ending-quote = 'One time, at band camp\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_NoClose4(t *testing.T) {
	input := "'a-string'.must-be = 'closed\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_TextAfterString(t *testing.T) {
	input := "string = \"Is there life after strings?\" No.\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_String_WrongClose(t *testing.T) {
	input := "bad-ending-quote = \"double and single'\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_AppendToArrayWithDottedKeys(t *testing.T) {
	input := "[[a.b]]\n\n[a]\nb.y = 2\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_AppendWithDottedKeys1(t *testing.T) {
	input := "# First a.b.c defines a table: a.b.c = {z=9}\n#\n# Then we define a.b.c.t = \"str\" to add a str to the above table, making it:\n#\n#   a.b.c = {z=9, t=\"...\"}\n#\n# While this makes sense, logically, it was decided this is not valid TOML as\n# it's too confusing/convoluted.\n# \n# See: https://github.com/toml-lang/toml/issues/846\n#      https://github.com/toml-lang/toml/pull/859\n\n[a.b.c]\n  z = 9\n\n[a]\n  b.c.t = \"Using dotted keys to add to [a.b.c] after explicitly defining it above is not allowed\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_AppendWithDottedKeys2(t *testing.T) {
	input := "# This is the same issue as in injection-1.toml, except that nests one level\n# deeper. See that file for a more complete description.\n\n[a.b.c.d]\n  z = 9\n\n[a]\n  b.c.d.k.t = \"Using dotted keys to add to [a.b.c.d] after explicitly defining it above is not allowed\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_ArrayEmpty(t *testing.T) {
	input := "[[]]\nname = \"Born to Run\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_ArrayImplicit(t *testing.T) {
	input := "# This test is a bit tricky. It should fail because the first use of\n# `[[albums.songs]]` without first declaring `albums` implies that `albums`\n# must be a table. The alternative would be quite weird. Namely, it wouldn't\n# comply with the TOML spec: \"Each double-bracketed sub-table will belong to \n# the most *recently* defined table element *above* it.\"\n#\n# This is in contrast to the *valid* test, table-array-implicit where\n# `[[albums.songs]]` works by itself, so long as `[[albums]]` isn't declared\n# later. (Although, `[albums]` could be.)\n[[albums.songs]]\nname = \"Glory Days\"\n\n[[albums]]\nname = \"Born in the USA\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_ArrayNoClose1(t *testing.T) {
	input := "[[albums]\nname = \"Born to Run\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_ArrayNoClose2(t *testing.T) {
	input := "[[closing-bracket.missing]\nblaa=2\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_DuplicateKeyDottedArray(t *testing.T) {
	input := "[fruit]\napple.color = \"red\"\n\n[[fruit.apple]]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_DuplicateKeyDottedTable(t *testing.T) {
	input := "[fruit]\napple.color = \"red\"\n\n[fruit.apple] # INVALID\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_DuplicateKeyDottedTable2(t *testing.T) {
	input := "[fruit]\napple.taste.sweet = true\n\n[fruit.apple.taste] # INVALID\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_DuplicateKeyTable(t *testing.T) {
	input := "[fruit]\ntype = \"apple\"\n\n[fruit.type]\napple = \"yes\"\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_DuplicateTableArray(t *testing.T) {
	input := "[tbl]\n[[tbl]]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_DuplicateTableArray2(t *testing.T) {
	input := "[[tbl]]\n[tbl]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_Duplicate(t *testing.T) {
	input := "[a]\nb = 1\n\n[a]\nc = 2\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_EmptyImplicitTable(t *testing.T) {
	input := "[naughty..naughty]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_Empty(t *testing.T) {
	input := "[]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_EqualsSign(t *testing.T) {
	input := "[name=bad]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_Llbrace(t *testing.T) {
	input := "[ [table]]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_NestedBracketsClose(t *testing.T) {
	input := "[a]b]\nzyx = 42\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_NestedBracketsOpen(t *testing.T) {
	input := "[a[b]\nzyx = 42\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_NoClose1(t *testing.T) {
	input := "[where will it end\nname = value\n\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_NoClose2(t *testing.T) {
	input := "[closing-bracket.missingö\nblaa=2\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_NoClose3(t *testing.T) {
	input := "[\"where will it end]\nname = value\n\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_NoClose4(t *testing.T) {
	input := "[\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_NoClose5(t *testing.T) {
	input := "[fwfw.wafw\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_OverwriteArrayInParent(t *testing.T) {
	input := "[[parent-table.arr]]\n[parent-table]\nnot-arr = 1\narr = 2\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_OverwriteBoolWithArray(t *testing.T) {
	input := "a=true\n[[a]]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_OverwriteWithDeepTable(t *testing.T) {
	input := "a=1\n[a.b.c.d]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_Redefine1(t *testing.T) {
	input := "# Define b as int, and try to use it as a table: error\n[a]\nb = 1\n\n[a.b]\nc = 2\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_Redefine2(t *testing.T) {
	input := "[t1]\nt2.t3.v = 0\n[t1.t2]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_Redefine3(t *testing.T) {
	input := "[t1]\nt2.t3.v = 0\n[t1.t2.t3]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_Rrbrace(t *testing.T) {
	input := "[[table] ]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_TextAfterTable(t *testing.T) {
	input := "[error] this shouldn't be here\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_Whitespace(t *testing.T) {
	input := "[invalid key]\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Invalid_Tests_Invalid_Table_WithPound(t *testing.T) {
	input := "[key#group]\nanswer = 42\n"
	testgenInvalid(t, input)
}

func TestTOMLTest_Valid_Array_ArraySubtables(t *testing.T) {
	input := "[[arr]]\n[arr.subtab]\nval=1\n\n[[arr]]\n[arr.subtab]\nval=2\n"
	jsonRef := "{\n  \"arr\": [\n    {\n      \"subtab\": {\n        \"val\": {\n          \"type\": \"integer\",\n          \"value\": \"1\"\n        }\n      }\n    },\n    {\n      \"subtab\": {\n        \"val\": {\n          \"type\": \"integer\",\n          \"value\": \"2\"\n        }\n      }\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Array_Array(t *testing.T) {
	input := "ints = [1, 2, 3, ]\nfloats = [1.1, 2.1, 3.1]\nstrings = [\"a\", \"b\", \"c\"]\ndates = [\n  1987-07-05T17:45:00Z,\n  1979-05-27T07:32:00Z,\n  2006-06-01T11:00:00Z,\n]\ncomments = [\n         1,\n         2, #this is ok\n]\n"
	jsonRef := "{\n  \"comments\": [\n    {\n      \"type\": \"integer\",\n      \"value\": \"1\"\n    },\n    {\n      \"type\": \"integer\",\n      \"value\": \"2\"\n    }\n  ],\n  \"dates\": [\n    {\n      \"type\": \"datetime\",\n      \"value\": \"1987-07-05T17:45:00Z\"\n    },\n    {\n      \"type\": \"datetime\",\n      \"value\": \"1979-05-27T07:32:00Z\"\n    },\n    {\n      \"type\": \"datetime\",\n      \"value\": \"2006-06-01T11:00:00Z\"\n    }\n  ],\n  \"floats\": [\n    {\n      \"type\": \"float\",\n      \"value\": \"1.1\"\n    },\n    {\n      \"type\": \"float\",\n      \"value\": \"2.1\"\n    },\n    {\n      \"type\": \"float\",\n      \"value\": \"3.1\"\n    }\n  ],\n  \"ints\": [\n    {\n      \"type\": \"integer\",\n      \"value\": \"1\"\n    },\n    {\n      \"type\": \"integer\",\n      \"value\": \"2\"\n    },\n    {\n      \"type\": \"integer\",\n      \"value\": \"3\"\n    }\n  ],\n  \"strings\": [\n    {\n      \"type\": \"string\",\n      \"value\": \"a\"\n    },\n    {\n      \"type\": \"string\",\n      \"value\": \"b\"\n    },\n    {\n      \"type\": \"string\",\n      \"value\": \"c\"\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Array_Bool(t *testing.T) {
	input := "a = [true, false]\n"
	jsonRef := "{\n  \"a\": [\n    {\n      \"type\": \"bool\",\n      \"value\": \"true\"\n    },\n    {\n      \"type\": \"bool\",\n      \"value\": \"false\"\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Array_Empty(t *testing.T) {
	input := "thevoid = [[[[[]]]]]\n"
	jsonRef := "{\n  \"thevoid\": [\n    [\n      [\n        [\n          []\n        ]\n      ]\n    ]\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Array_Heterogeneous(t *testing.T) {
	input := "mixed = [[1, 2], [\"a\", \"b\"], [1.1, 2.1]]\n"
	jsonRef := "{\n  \"mixed\": [\n    [\n      {\n        \"type\": \"integer\",\n        \"value\": \"1\"\n      },\n      {\n        \"type\": \"integer\",\n        \"value\": \"2\"\n      }\n    ],\n    [\n      {\n        \"type\": \"string\",\n        \"value\": \"a\"\n      },\n      {\n        \"type\": \"string\",\n        \"value\": \"b\"\n      }\n    ],\n    [\n      {\n        \"type\": \"float\",\n        \"value\": \"1.1\"\n      },\n      {\n        \"type\": \"float\",\n        \"value\": \"2.1\"\n      }\n    ]\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Array_MixedIntArray(t *testing.T) {
	input := "arrays-and-ints =  [1, [\"Arrays are not integers.\"]]\n"
	jsonRef := "{\n  \"arrays-and-ints\": [\n    {\n      \"type\": \"integer\",\n      \"value\": \"1\"\n    },\n    [\n      {\n        \"type\": \"string\",\n        \"value\": \"Arrays are not integers.\"\n      }\n    ]\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Array_MixedIntFloat(t *testing.T) {
	input := "ints-and-floats = [1, 1.1]\n"
	jsonRef := "{\n  \"ints-and-floats\": [\n    {\n      \"type\": \"integer\",\n      \"value\": \"1\"\n    },\n    {\n      \"type\": \"float\",\n      \"value\": \"1.1\"\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Array_MixedIntString(t *testing.T) {
	input := "strings-and-ints = [\"hi\", 42]\n"
	jsonRef := "{\n  \"strings-and-ints\": [\n    {\n      \"type\": \"string\",\n      \"value\": \"hi\"\n    },\n    {\n      \"type\": \"integer\",\n      \"value\": \"42\"\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Array_MixedStringTable(t *testing.T) {
	input := "contributors = [\n  \"Foo Bar <<EMAIL>>\",\n  { name = \"Baz Qux\", email = \"<EMAIL>\", url = \"https://example.com/bazqux\" }\n]\n\n# Start with a table as the first element. This tests a case that some libraries\n# might have where they will check if the first entry is a table/map/hash/assoc\n# array and then encode it as a table array. This was a reasonable thing to do\n# before TOML 1.0 since arrays could only contain one type, but now it's no\n# longer.\nmixed = [{k=\"a\"}, \"b\", 1]\n"
	jsonRef := "{\n  \"contributors\": [\n    {\n      \"type\": \"string\",\n      \"value\": \"Foo Bar \\<EMAIL>\\u003e\"\n    },\n    {\n      \"email\": {\n        \"type\": \"string\",\n        \"value\": \"<EMAIL>\"\n      },\n      \"name\": {\n        \"type\": \"string\",\n        \"value\": \"Baz Qux\"\n      },\n      \"url\": {\n        \"type\": \"string\",\n        \"value\": \"https://example.com/bazqux\"\n      }\n    }\n  ],\n  \"mixed\": [\n    {\n      \"k\": {\n        \"type\": \"string\",\n        \"value\": \"a\"\n      }\n    },\n    {\n      \"type\": \"string\",\n      \"value\": \"b\"\n    },\n    {\n      \"type\": \"integer\",\n      \"value\": \"1\"\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Array_NestedDouble(t *testing.T) {
	input := "nest = [\n\t[\n\t\t[\"a\"],\n\t\t[1, 2, [3]]\n\t]\n]\n"
	jsonRef := "{\n  \"nest\": [\n    [\n      [\n        {\n          \"type\": \"string\",\n          \"value\": \"a\"\n        }\n      ],\n      [\n        {\n          \"type\": \"integer\",\n          \"value\": \"1\"\n        },\n        {\n          \"type\": \"integer\",\n          \"value\": \"2\"\n        },\n        [\n          {\n            \"type\": \"integer\",\n            \"value\": \"3\"\n          }\n        ]\n      ]\n    ]\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Array_NestedInlineTable(t *testing.T) {
	input := "a = [ { b = {} } ]\n"
	jsonRef := "{\n  \"a\": [\n    {\n      \"b\": {}\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Array_Nested(t *testing.T) {
	input := "nest = [[\"a\"], [\"b\"]]\n"
	jsonRef := "{\n  \"nest\": [\n    [\n      {\n        \"type\": \"string\",\n        \"value\": \"a\"\n      }\n    ],\n    [\n      {\n        \"type\": \"string\",\n        \"value\": \"b\"\n      }\n    ]\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Array_Nospaces(t *testing.T) {
	input := "ints = [1,2,3]\n"
	jsonRef := "{\n  \"ints\": [\n    {\n      \"type\": \"integer\",\n      \"value\": \"1\"\n    },\n    {\n      \"type\": \"integer\",\n      \"value\": \"2\"\n    },\n    {\n      \"type\": \"integer\",\n      \"value\": \"3\"\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Array_OpenParentTable(t *testing.T) {
	input := "[[parent-table.arr]]\n[[parent-table.arr]]\n[parent-table]\nnot-arr = 1\n"
	jsonRef := "{\n  \"parent-table\": {\n    \"arr\": [\n      {},\n      {}\n    ],\n    \"not-arr\": {\n      \"type\": \"integer\",\n      \"value\": \"1\"\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Array_StringQuoteComma2(t *testing.T) {
	input := "title = [ \" \\\", \",]\n"
	jsonRef := "{\n  \"title\": [\n    {\n      \"type\": \"string\",\n      \"value\": \" \\\", \"\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Array_StringQuoteComma(t *testing.T) {
	input := "title = [\n\"Client: \\\"XXXX\\\", Job: XXXX\",\n\"Code: XXXX\"\n]\n"
	jsonRef := "{\n  \"title\": [\n    {\n      \"type\": \"string\",\n      \"value\": \"Client: \\\"XXXX\\\", Job: XXXX\"\n    },\n    {\n      \"type\": \"string\",\n      \"value\": \"Code: XXXX\"\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Array_StringWithComma2(t *testing.T) {
	input := "title = [\n\"\"\"Client: XXXX,\nJob: XXXX\"\"\",\n\"Code: XXXX\"\n]\n"
	jsonRef := "{\n  \"title\": [\n    {\n      \"type\": \"string\",\n      \"value\": \"Client: XXXX,\\nJob: XXXX\"\n    },\n    {\n      \"type\": \"string\",\n      \"value\": \"Code: XXXX\"\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Array_StringWithComma(t *testing.T) {
	input := "title = [\n\"Client: XXXX, Job: XXXX\",\n\"Code: XXXX\"\n]\n"
	jsonRef := "{\n  \"title\": [\n    {\n      \"type\": \"string\",\n      \"value\": \"Client: XXXX, Job: XXXX\"\n    },\n    {\n      \"type\": \"string\",\n      \"value\": \"Code: XXXX\"\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Array_Strings(t *testing.T) {
	input := "string_array = [ \"all\", 'strings', \"\"\"are the same\"\"\", '''type''']\n"
	jsonRef := "{\n  \"string_array\": [\n    {\n      \"type\": \"string\",\n      \"value\": \"all\"\n    },\n    {\n      \"type\": \"string\",\n      \"value\": \"strings\"\n    },\n    {\n      \"type\": \"string\",\n      \"value\": \"are the same\"\n    },\n    {\n      \"type\": \"string\",\n      \"value\": \"type\"\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Array_TableArrayStringBackslash(t *testing.T) {
	input := "foo = [ { bar=\"\\\"{{baz}}\\\"\"} ]\n"
	jsonRef := "{\n  \"foo\": [\n    {\n      \"bar\": {\n        \"type\": \"string\",\n        \"value\": \"\\\"{{baz}}\\\"\"\n      }\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Array_TrailingComma(t *testing.T) {
	input := "arr-1 = [1,]\n\narr-2 = [2,3,]\n\narr-3 = [4,\n]\n\narr-4 = [\n\t5,\n\t6,\n]\n"
	jsonRef := "{\n  \"arr-1\": [\n    {\n      \"type\": \"integer\",\n      \"value\": \"1\"\n    }\n  ],\n  \"arr-2\": [\n    {\n      \"type\": \"integer\",\n      \"value\": \"2\"\n    },\n    {\n      \"type\": \"integer\",\n      \"value\": \"3\"\n    }\n  ],\n  \"arr-3\": [\n    {\n      \"type\": \"integer\",\n      \"value\": \"4\"\n    }\n  ],\n  \"arr-4\": [\n    {\n      \"type\": \"integer\",\n      \"value\": \"5\"\n    },\n    {\n      \"type\": \"integer\",\n      \"value\": \"6\"\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Bool_Bool(t *testing.T) {
	input := "t = true\nf = false\n"
	jsonRef := "{\n  \"f\": {\n    \"type\": \"bool\",\n    \"value\": \"false\"\n  },\n  \"t\": {\n    \"type\": \"bool\",\n    \"value\": \"true\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Comment_AfterLiteralNoWs(t *testing.T) {
	input := "inf=inf#infinity\nnan=nan#not a number\ntrue=true#true\nfalse=false#false\n"
	jsonRef := "{\n  \"false\": {\n    \"type\": \"bool\",\n    \"value\": \"false\"\n  },\n  \"inf\": {\n    \"type\": \"float\",\n    \"value\": \"inf\"\n  },\n  \"nan\": {\n    \"type\": \"float\",\n    \"value\": \"nan\"\n  },\n  \"true\": {\n    \"type\": \"bool\",\n    \"value\": \"true\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Comment_AtEof(t *testing.T) {
	input := "# This is a full-line comment\nkey = \"value\" # This is a comment at the end of a line\n"
	jsonRef := "{\n  \"key\": {\n    \"type\": \"string\",\n    \"value\": \"value\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Comment_AtEof2(t *testing.T) {
	input := "# This is a full-line comment\nkey = \"value\" # This is a comment at the end of a line\n"
	jsonRef := "{\n  \"key\": {\n    \"type\": \"string\",\n    \"value\": \"value\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Comment_Everywhere(t *testing.T) {
	input := "# Top comment.\n  # Top comment.\n# Top comment.\n\n# [no-extraneous-groups-please]\n\n[group] # Comment\nanswer = 42 # Comment\n# no-extraneous-keys-please = 999\n# Inbetween comment.\nmore = [ # Comment\n  # What about multiple # comments?\n  # Can you handle it?\n  #\n          # Evil.\n# Evil.\n  42, 42, # Comments within arrays are fun.\n  # What about multiple # comments?\n  # Can you handle it?\n  #\n          # Evil.\n# Evil.\n# ] Did I fool you?\n] # Hopefully not.\n\n# Make sure the space between the datetime and \"#\" isn't lexed.\ndt = 1979-05-27T07:32:12-07:00  # c\nd = 1979-05-27 # Comment\n"
	jsonRef := "{\n  \"group\": {\n    \"answer\": {\n      \"type\": \"integer\",\n      \"value\": \"42\"\n    },\n    \"d\": {\n      \"type\": \"date-local\",\n      \"value\": \"1979-05-27\"\n    },\n    \"dt\": {\n      \"type\": \"datetime\",\n      \"value\": \"1979-05-27T07:32:12-07:00\"\n    },\n    \"more\": [\n      {\n        \"type\": \"integer\",\n        \"value\": \"42\"\n      },\n      {\n        \"type\": \"integer\",\n        \"value\": \"42\"\n      }\n    ]\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Comment_Noeol(t *testing.T) {
	input := "# single comment without any eol characters"
	jsonRef := "{}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Comment_Nonascii(t *testing.T) {
	input := "# ~ \u0080 ÿ \ud7ff \ue000 \uffff 𐀀 \U0010ffff\n"
	jsonRef := "{}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Comment_Tricky(t *testing.T) {
	input := "[section]#attached comment\n#[notsection]\none = \"11\"#cmt\ntwo = \"22#\"\nthree = '#'\n\nfour = \"\"\"# no comment\n# nor this\n#also not comment\"\"\"#is_comment\n\nfive = 5.5#66\nsix = 6#7\n8 = \"eight\"\n#nine = 99\nten = 10e2#1\neleven = 1.11e1#23\n\n[\"hash#tag\"]\n\"#!\" = \"hash bang\"\narr3 = [ \"#\", '#', \"\"\"###\"\"\" ]\narr4 = [ 1,# 9, 9,\n2#,9\n,#9\n3#]\n,4]\narr5 = [[[[#[\"#\"],\n[\"#\"]]]]#]\n]\ntbl1 = { \"#\" = '}#'}#}}\n\n\n"
	jsonRef := "{\n  \"hash#tag\": {\n    \"#!\": {\n      \"type\": \"string\",\n      \"value\": \"hash bang\"\n    },\n    \"arr3\": [\n      {\n        \"type\": \"string\",\n        \"value\": \"#\"\n      },\n      {\n        \"type\": \"string\",\n        \"value\": \"#\"\n      },\n      {\n        \"type\": \"string\",\n        \"value\": \"###\"\n      }\n    ],\n    \"arr4\": [\n      {\n        \"type\": \"integer\",\n        \"value\": \"1\"\n      },\n      {\n        \"type\": \"integer\",\n        \"value\": \"2\"\n      },\n      {\n        \"type\": \"integer\",\n        \"value\": \"3\"\n      },\n      {\n        \"type\": \"integer\",\n        \"value\": \"4\"\n      }\n    ],\n    \"arr5\": [\n      [\n        [\n          [\n            [\n              {\n                \"type\": \"string\",\n                \"value\": \"#\"\n              }\n            ]\n          ]\n        ]\n      ]\n    ],\n    \"tbl1\": {\n      \"#\": {\n        \"type\": \"string\",\n        \"value\": \"}#\"\n      }\n    }\n  },\n  \"section\": {\n    \"8\": {\n      \"type\": \"string\",\n      \"value\": \"eight\"\n    },\n    \"eleven\": {\n      \"type\": \"float\",\n      \"value\": \"11.1\"\n    },\n    \"five\": {\n      \"type\": \"float\",\n      \"value\": \"5.5\"\n    },\n    \"four\": {\n      \"type\": \"string\",\n      \"value\": \"# no comment\\n# nor this\\n#also not comment\"\n    },\n    \"one\": {\n      \"type\": \"string\",\n      \"value\": \"11\"\n    },\n    \"six\": {\n      \"type\": \"integer\",\n      \"value\": \"6\"\n    },\n    \"ten\": {\n      \"type\": \"float\",\n      \"value\": \"1000.0\"\n    },\n    \"three\": {\n      \"type\": \"string\",\n      \"value\": \"#\"\n    },\n    \"two\": {\n      \"type\": \"string\",\n      \"value\": \"22#\"\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Datetime_Datetime(t *testing.T) {
	input := "space = 1987-07-05 17:45:00Z\nlower = 1987-07-05t17:45:00z\n"
	jsonRef := "{\n  \"lower\": {\n    \"type\": \"datetime\",\n    \"value\": \"1987-07-05T17:45:00Z\"\n  },\n  \"space\": {\n    \"type\": \"datetime\",\n    \"value\": \"1987-07-05T17:45:00Z\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Datetime_Edge(t *testing.T) {
	input := "first-offset = 0001-01-01 00:00:00Z\nfirst-local  = 0001-01-01 00:00:00\nfirst-date   = 0001-01-01\n\nlast-offset = 9999-12-31 23:59:59Z\nlast-local  = 9999-12-31 23:59:59\nlast-date   = 9999-12-31\n"
	jsonRef := "{\n  \"first-date\": {\n    \"type\": \"date-local\",\n    \"value\": \"0001-01-01\"\n  },\n  \"first-local\": {\n    \"type\": \"datetime-local\",\n    \"value\": \"0001-01-01T00:00:00\"\n  },\n  \"first-offset\": {\n    \"type\": \"datetime\",\n    \"value\": \"0001-01-01T00:00:00Z\"\n  },\n  \"last-date\": {\n    \"type\": \"date-local\",\n    \"value\": \"9999-12-31\"\n  },\n  \"last-local\": {\n    \"type\": \"datetime-local\",\n    \"value\": \"9999-12-31T23:59:59\"\n  },\n  \"last-offset\": {\n    \"type\": \"datetime\",\n    \"value\": \"9999-12-31T23:59:59Z\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Datetime_LeapYear(t *testing.T) {
	input := "2000-datetime       = 2000-02-29 15:15:15Z\n2000-datetime-local = 2000-02-29 15:15:15\n2000-date           = 2000-02-29\n\n2024-datetime       = 2024-02-29 15:15:15Z\n2024-datetime-local = 2024-02-29 15:15:15\n2024-date           = 2024-02-29\n"
	jsonRef := "{\n  \"2000-date\": {\n    \"type\": \"date-local\",\n    \"value\": \"2000-02-29\"\n  },\n  \"2000-datetime\": {\n    \"type\": \"datetime\",\n    \"value\": \"2000-02-29T15:15:15Z\"\n  },\n  \"2000-datetime-local\": {\n    \"type\": \"datetime-local\",\n    \"value\": \"2000-02-29T15:15:15\"\n  },\n  \"2024-date\": {\n    \"type\": \"date-local\",\n    \"value\": \"2024-02-29\"\n  },\n  \"2024-datetime\": {\n    \"type\": \"datetime\",\n    \"value\": \"2024-02-29T15:15:15Z\"\n  },\n  \"2024-datetime-local\": {\n    \"type\": \"datetime-local\",\n    \"value\": \"2024-02-29T15:15:15\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Datetime_LocalDate(t *testing.T) {
	input := "bestdayever = 1987-07-05\n"
	jsonRef := "{\n  \"bestdayever\": {\n    \"type\": \"date-local\",\n    \"value\": \"1987-07-05\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Datetime_LocalTime(t *testing.T) {
	input := "besttimeever = 17:45:00\nmilliseconds = 10:32:00.555\n"
	jsonRef := "{\n  \"besttimeever\": {\n    \"type\": \"time-local\",\n    \"value\": \"17:45:00\"\n  },\n  \"milliseconds\": {\n    \"type\": \"time-local\",\n    \"value\": \"10:32:00.555\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Datetime_Local(t *testing.T) {
	input := "local = 1987-07-05T17:45:00\nmilli = 1977-12-21T10:32:00.555\nspace = 1987-07-05 17:45:00\n"
	jsonRef := "{\n  \"local\": {\n    \"type\": \"datetime-local\",\n    \"value\": \"1987-07-05T17:45:00\"\n  },\n  \"milli\": {\n    \"type\": \"datetime-local\",\n    \"value\": \"1977-12-21T10:32:00.555\"\n  },\n  \"space\": {\n    \"type\": \"datetime-local\",\n    \"value\": \"1987-07-05T17:45:00\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Datetime_Milliseconds(t *testing.T) {
	input := "utc1  = 1987-07-05T17:45:56.123Z\nutc2  = 1987-07-05T17:45:56.6Z\nwita1 = 1987-07-05T17:45:56.123+08:00\nwita2 = 1987-07-05T17:45:56.6+08:00\n"
	jsonRef := "{\n  \"utc1\": {\n    \"type\": \"datetime\",\n    \"value\": \"1987-07-05T17:45:56.123Z\"\n  },\n  \"utc2\": {\n    \"type\": \"datetime\",\n    \"value\": \"1987-07-05T17:45:56.600Z\"\n  },\n  \"wita1\": {\n    \"type\": \"datetime\",\n    \"value\": \"1987-07-05T17:45:56.123+08:00\"\n  },\n  \"wita2\": {\n    \"type\": \"datetime\",\n    \"value\": \"1987-07-05T17:45:56.600+08:00\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Datetime_Timezone(t *testing.T) {
	input := "utc  = 1987-07-05T17:45:56Z\npdt  = 1987-07-05T17:45:56-05:00\nnzst = 1987-07-05T17:45:56+12:00\nnzdt = 1987-07-05T17:45:56+13:00  # DST\n"
	jsonRef := "{\n  \"nzdt\": {\n    \"type\": \"datetime\",\n    \"value\": \"1987-07-05T17:45:56+13:00\"\n  },\n  \"nzst\": {\n    \"type\": \"datetime\",\n    \"value\": \"1987-07-05T17:45:56+12:00\"\n  },\n  \"pdt\": {\n    \"type\": \"datetime\",\n    \"value\": \"1987-07-05T17:45:56-05:00\"\n  },\n  \"utc\": {\n    \"type\": \"datetime\",\n    \"value\": \"1987-07-05T17:45:56Z\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Float_Exponent(t *testing.T) {
	input := "lower = 3e2\nupper = 3E2\nneg = 3e-2\npos = 3E+2\nzero = 3e0\npointlower = 3.1e2\npointupper = 3.1E2\nminustenth = -1E-1\n"
	jsonRef := "{\n  \"lower\": {\n    \"type\": \"float\",\n    \"value\": \"300.0\"\n  },\n  \"minustenth\": {\n    \"type\": \"float\",\n    \"value\": \"-0.1\"\n  },\n  \"neg\": {\n    \"type\": \"float\",\n    \"value\": \"0.03\"\n  },\n  \"pointlower\": {\n    \"type\": \"float\",\n    \"value\": \"310.0\"\n  },\n  \"pointupper\": {\n    \"type\": \"float\",\n    \"value\": \"310.0\"\n  },\n  \"pos\": {\n    \"type\": \"float\",\n    \"value\": \"300.0\"\n  },\n  \"upper\": {\n    \"type\": \"float\",\n    \"value\": \"300.0\"\n  },\n  \"zero\": {\n    \"type\": \"float\",\n    \"value\": \"3.0\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Float_Float(t *testing.T) {
	input := "pi = 3.14\npospi = +3.14\nnegpi = -3.14\nzero-intpart = 0.123\n"
	jsonRef := "{\n  \"negpi\": {\n    \"type\": \"float\",\n    \"value\": \"-3.14\"\n  },\n  \"pi\": {\n    \"type\": \"float\",\n    \"value\": \"3.14\"\n  },\n  \"pospi\": {\n    \"type\": \"float\",\n    \"value\": \"3.14\"\n  },\n  \"zero-intpart\": {\n    \"type\": \"float\",\n    \"value\": \"0.123\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Float_InfAndNan(t *testing.T) {
	input := "# We don't encode +nan and -nan back with the signs; many languages don't\n# support a sign on NaN (it doesn't really make much sense).\nnan = nan\nnan_neg = -nan\nnan_plus = +nan\ninfinity = inf\ninfinity_neg = -inf\ninfinity_plus = +inf\n"
	jsonRef := "{\n  \"infinity\": {\n    \"type\": \"float\",\n    \"value\": \"inf\"\n  },\n  \"infinity_neg\": {\n    \"type\": \"float\",\n    \"value\": \"-inf\"\n  },\n  \"infinity_plus\": {\n    \"type\": \"float\",\n    \"value\": \"+inf\"\n  },\n  \"nan\": {\n    \"type\": \"float\",\n    \"value\": \"nan\"\n  },\n  \"nan_neg\": {\n    \"type\": \"float\",\n    \"value\": \"nan\"\n  },\n  \"nan_plus\": {\n    \"type\": \"float\",\n    \"value\": \"nan\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Float_Long(t *testing.T) {
	input := "longpi = 3.141592653589793\nneglongpi = -3.141592653589793\n"
	jsonRef := "{\n  \"longpi\": {\n    \"type\": \"float\",\n    \"value\": \"3.141592653589793\"\n  },\n  \"neglongpi\": {\n    \"type\": \"float\",\n    \"value\": \"-3.141592653589793\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Float_Underscore(t *testing.T) {
	input := "before = 3_141.5927\nafter = 3141.592_7\nexponent = 3e1_4\n"
	jsonRef := "{\n  \"after\": {\n    \"type\": \"float\",\n    \"value\": \"3141.5927\"\n  },\n  \"before\": {\n    \"type\": \"float\",\n    \"value\": \"3141.5927\"\n  },\n  \"exponent\": {\n    \"type\": \"float\",\n    \"value\": \"3.0e14\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Float_Zero(t *testing.T) {
	input := "zero = 0.0\nsigned-pos = +0.0\nsigned-neg = -0.0\nexponent = 0e0\nexponent-two-0 = 0e00\nexponent-signed-pos = +0e0\nexponent-signed-neg = -0e0\n"
	jsonRef := "{\n  \"exponent\": {\n    \"type\": \"float\",\n    \"value\": \"0\"\n  },\n  \"exponent-signed-neg\": {\n    \"type\": \"float\",\n    \"value\": \"-0\"\n  },\n  \"exponent-signed-pos\": {\n    \"type\": \"float\",\n    \"value\": \"0\"\n  },\n  \"exponent-two-0\": {\n    \"type\": \"float\",\n    \"value\": \"0\"\n  },\n  \"signed-neg\": {\n    \"type\": \"float\",\n    \"value\": \"-0\"\n  },\n  \"signed-pos\": {\n    \"type\": \"float\",\n    \"value\": \"0\"\n  },\n  \"zero\": {\n    \"type\": \"float\",\n    \"value\": \"0\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_InlineTable_Array(t *testing.T) {
	input := "people = [{first_name = \"Bruce\", last_name = \"Springsteen\"},\n          {first_name = \"Eric\", last_name = \"Clapton\"},\n          {first_name = \"Bob\", last_name = \"Seger\"}]\n"
	jsonRef := "{\n  \"people\": [\n    {\n      \"first_name\": {\n        \"type\": \"string\",\n        \"value\": \"Bruce\"\n      },\n      \"last_name\": {\n        \"type\": \"string\",\n        \"value\": \"Springsteen\"\n      }\n    },\n    {\n      \"first_name\": {\n        \"type\": \"string\",\n        \"value\": \"Eric\"\n      },\n      \"last_name\": {\n        \"type\": \"string\",\n        \"value\": \"Clapton\"\n      }\n    },\n    {\n      \"first_name\": {\n        \"type\": \"string\",\n        \"value\": \"Bob\"\n      },\n      \"last_name\": {\n        \"type\": \"string\",\n        \"value\": \"Seger\"\n      }\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_InlineTable_Bool(t *testing.T) {
	input := "a = {a = true, b = false}\n"
	jsonRef := "{\n  \"a\": {\n    \"a\": {\n      \"type\": \"bool\",\n      \"value\": \"true\"\n    },\n    \"b\": {\n      \"type\": \"bool\",\n      \"value\": \"false\"\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_InlineTable_Empty(t *testing.T) {
	input := "empty1 = {}\nempty2 = { }\nempty_in_array = [ { not_empty = 1 }, {} ]\nempty_in_array2 = [{},{not_empty=1}]\nmany_empty = [{},{},{}]\nnested_empty = {\"empty\"={}}\nwith_cmt ={            }#nothing here\n"
	jsonRef := "{\n  \"empty1\": {},\n  \"empty2\": {},\n  \"empty_in_array\": [\n    {\n      \"not_empty\": {\n        \"type\": \"integer\",\n        \"value\": \"1\"\n      }\n    },\n    {}\n  ],\n  \"empty_in_array2\": [\n    {},\n    {\n      \"not_empty\": {\n        \"type\": \"integer\",\n        \"value\": \"1\"\n      }\n    }\n  ],\n  \"many_empty\": [\n    {},\n    {},\n    {}\n  ],\n  \"nested_empty\": {\n    \"empty\": {}\n  },\n  \"with_cmt\": {}\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_InlineTable_EndInBool(t *testing.T) {
	input := "black = { python=\">3.6\", version=\">=18.9b0\", allow_prereleases=true }\n"
	jsonRef := "{\n  \"black\": {\n    \"allow_prereleases\": {\n      \"type\": \"bool\",\n      \"value\": \"true\"\n    },\n    \"python\": {\n      \"type\": \"string\",\n      \"value\": \"\\u003e3.6\"\n    },\n    \"version\": {\n      \"type\": \"string\",\n      \"value\": \"\\u003e=18.9b0\"\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_InlineTable_InlineTable(t *testing.T) {
	input := "name = { first = \"Tom\", last = \"Preston-Werner\" }\npoint = { x = 1, y = 2 }\nsimple = { a = 1 }\nstr-key = { \"a\" = 1 }\ntable-array = [{ \"a\" = 1 }, { \"b\" = 2 }]\n"
	jsonRef := "{\n  \"name\": {\n    \"first\": {\n      \"type\": \"string\",\n      \"value\": \"Tom\"\n    },\n    \"last\": {\n      \"type\": \"string\",\n      \"value\": \"Preston-Werner\"\n    }\n  },\n  \"point\": {\n    \"x\": {\n      \"type\": \"integer\",\n      \"value\": \"1\"\n    },\n    \"y\": {\n      \"type\": \"integer\",\n      \"value\": \"2\"\n    }\n  },\n  \"simple\": {\n    \"a\": {\n      \"type\": \"integer\",\n      \"value\": \"1\"\n    }\n  },\n  \"str-key\": {\n    \"a\": {\n      \"type\": \"integer\",\n      \"value\": \"1\"\n    }\n  },\n  \"table-array\": [\n    {\n      \"a\": {\n        \"type\": \"integer\",\n        \"value\": \"1\"\n      }\n    },\n    {\n      \"b\": {\n        \"type\": \"integer\",\n        \"value\": \"2\"\n      }\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_InlineTable_KeyDotted(t *testing.T) {
	input := "inline = {a.b = 42}\n\nmany.dots.here.dot.dot.dot = {a.b.c = 1, a.b.d = 2}\n\na = {   a.b  =  1   }\nb = {   \"a\".\"b\"  =  1   }\nc = {   a   .   b  =  1   }\nd = {   'a'   .   \"b\"  =  1   }\ne = {a.b=1}\n\n[tbl]\na.b.c = {d.e=1}\n\n[tbl.x]\na.b.c = {d.e=1}\n\n[[arr]]\nt = {a.b=1}\nT = {a.b=1}\n\n[[arr]]\nt = {a.b=2}\nT = {a.b=2}\n"
	jsonRef := "{\n  \"a\": {\n    \"a\": {\n      \"b\": {\n        \"type\": \"integer\",\n        \"value\": \"1\"\n      }\n    }\n  },\n  \"arr\": [\n    {\n      \"T\": {\n        \"a\": {\n          \"b\": {\n            \"type\": \"integer\",\n            \"value\": \"1\"\n          }\n        }\n      },\n      \"t\": {\n        \"a\": {\n          \"b\": {\n            \"type\": \"integer\",\n            \"value\": \"1\"\n          }\n        }\n      }\n    },\n    {\n      \"T\": {\n        \"a\": {\n          \"b\": {\n            \"type\": \"integer\",\n            \"value\": \"2\"\n          }\n        }\n      },\n      \"t\": {\n        \"a\": {\n          \"b\": {\n            \"type\": \"integer\",\n            \"value\": \"2\"\n          }\n        }\n      }\n    }\n  ],\n  \"b\": {\n    \"a\": {\n      \"b\": {\n        \"type\": \"integer\",\n        \"value\": \"1\"\n      }\n    }\n  },\n  \"c\": {\n    \"a\": {\n      \"b\": {\n        \"type\": \"integer\",\n        \"value\": \"1\"\n      }\n    }\n  },\n  \"d\": {\n    \"a\": {\n      \"b\": {\n        \"type\": \"integer\",\n        \"value\": \"1\"\n      }\n    }\n  },\n  \"e\": {\n    \"a\": {\n      \"b\": {\n        \"type\": \"integer\",\n        \"value\": \"1\"\n      }\n    }\n  },\n  \"inline\": {\n    \"a\": {\n      \"b\": {\n        \"type\": \"integer\",\n        \"value\": \"42\"\n      }\n    }\n  },\n  \"many\": {\n    \"dots\": {\n      \"here\": {\n        \"dot\": {\n          \"dot\": {\n            \"dot\": {\n              \"a\": {\n                \"b\": {\n                  \"c\": {\n                    \"type\": \"integer\",\n                    \"value\": \"1\"\n                  },\n                  \"d\": {\n                    \"type\": \"integer\",\n                    \"value\": \"2\"\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  },\n  \"tbl\": {\n    \"a\": {\n      \"b\": {\n        \"c\": {\n          \"d\": {\n            \"e\": {\n              \"type\": \"integer\",\n              \"value\": \"1\"\n            }\n          }\n        }\n      }\n    },\n    \"x\": {\n      \"a\": {\n        \"b\": {\n          \"c\": {\n            \"d\": {\n              \"e\": {\n                \"type\": \"integer\",\n                \"value\": \"1\"\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_InlineTable_Multiline(t *testing.T) {
	input := "tbl_multiline = { a = 1, b = \"\"\"\nmultiline\n\"\"\", c = \"\"\"and yet\nanother line\"\"\", d = 4 }\n"
	jsonRef := "{\n  \"tbl_multiline\": {\n    \"a\": {\n      \"type\": \"integer\",\n      \"value\": \"1\"\n    },\n    \"b\": {\n      \"type\": \"string\",\n      \"value\": \"multiline\\n\"\n    },\n    \"c\": {\n      \"type\": \"string\",\n      \"value\": \"and yet\\nanother line\"\n    },\n    \"d\": {\n      \"type\": \"integer\",\n      \"value\": \"4\"\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_InlineTable_Nest(t *testing.T) {
	input := "tbl_tbl_empty = { tbl_0 = {} }\ntbl_tbl_val   = { tbl_1 = { one = 1 } }\ntbl_arr_tbl   = { arr_tbl = [ { one = 1 } ] }\narr_tbl_tbl   = [ { tbl = { one = 1 } } ]\n\n# Array-of-array-of-table is interesting because it can only\n# be represented in inline form.\narr_arr_tbl_empty = [ [ {} ] ]\narr_arr_tbl_val = [ [ { one = 1 } ] ]\narr_arr_tbls  = [ [ { one = 1 }, { two = 2 } ] ]\n"
	jsonRef := "{\n  \"arr_arr_tbl_empty\": [\n    [\n      {}\n    ]\n  ],\n  \"arr_arr_tbl_val\": [\n    [\n      {\n        \"one\": {\n          \"type\": \"integer\",\n          \"value\": \"1\"\n        }\n      }\n    ]\n  ],\n  \"arr_arr_tbls\": [\n    [\n      {\n        \"one\": {\n          \"type\": \"integer\",\n          \"value\": \"1\"\n        }\n      },\n      {\n        \"two\": {\n          \"type\": \"integer\",\n          \"value\": \"2\"\n        }\n      }\n    ]\n  ],\n  \"arr_tbl_tbl\": [\n    {\n      \"tbl\": {\n        \"one\": {\n          \"type\": \"integer\",\n          \"value\": \"1\"\n        }\n      }\n    }\n  ],\n  \"tbl_arr_tbl\": {\n    \"arr_tbl\": [\n      {\n        \"one\": {\n          \"type\": \"integer\",\n          \"value\": \"1\"\n        }\n      }\n    ]\n  },\n  \"tbl_tbl_empty\": {\n    \"tbl_0\": {}\n  },\n  \"tbl_tbl_val\": {\n    \"tbl_1\": {\n      \"one\": {\n        \"type\": \"integer\",\n        \"value\": \"1\"\n      }\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_InlineTable_Spaces(t *testing.T) {
	input := "# https://github.com/toml-lang/toml-test/issues/146\nclap-1 = { version = \"4\"  , features = [\"derive\", \"cargo\"] }\n\n# Contains some literal tabs!\nclap-2 = { version = \"4\"\t   \t,\t  \tfeatures = [   \"derive\" \t  ,  \t  \"cargo\"   ]   , nest   =   {  \t  \"a\"   =   'x'  , \t  'b'   = [ 1.5    ,   9.0  ]  }  }\n"
	jsonRef := "{\n  \"clap-1\": {\n    \"features\": [\n      {\n        \"type\": \"string\",\n        \"value\": \"derive\"\n      },\n      {\n        \"type\": \"string\",\n        \"value\": \"cargo\"\n      }\n    ],\n    \"version\": {\n      \"type\": \"string\",\n      \"value\": \"4\"\n    }\n  },\n  \"clap-2\": {\n    \"features\": [\n      {\n        \"type\": \"string\",\n        \"value\": \"derive\"\n      },\n      {\n        \"type\": \"string\",\n        \"value\": \"cargo\"\n      }\n    ],\n    \"nest\": {\n      \"a\": {\n        \"type\": \"string\",\n        \"value\": \"x\"\n      },\n      \"b\": [\n        {\n          \"type\": \"float\",\n          \"value\": \"1.5\"\n        },\n        {\n          \"type\": \"float\",\n          \"value\": \"9\"\n        }\n      ]\n    },\n    \"version\": {\n      \"type\": \"string\",\n      \"value\": \"4\"\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Integer_Integer(t *testing.T) {
	input := "answer = 42\nposanswer = +42\nneganswer = -42\nzero = 0\n"
	jsonRef := "{\n  \"answer\": {\n    \"type\": \"integer\",\n    \"value\": \"42\"\n  },\n  \"neganswer\": {\n    \"type\": \"integer\",\n    \"value\": \"-42\"\n  },\n  \"posanswer\": {\n    \"type\": \"integer\",\n    \"value\": \"42\"\n  },\n  \"zero\": {\n    \"type\": \"integer\",\n    \"value\": \"0\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Integer_Literals(t *testing.T) {
	input := "bin1 = 0b11010110\nbin2 = 0b1_0_1\n\noct1 = 0o01234567\noct2 = 0o755\noct3 = 0o7_6_5\n\nhex1 = 0xDEADBEEF\nhex2 = 0xdeadbeef\nhex3 = 0xdead_beef\nhex4 = 0x00987\n"
	jsonRef := "{\n  \"bin1\": {\n    \"type\": \"integer\",\n    \"value\": \"214\"\n  },\n  \"bin2\": {\n    \"type\": \"integer\",\n    \"value\": \"5\"\n  },\n  \"hex1\": {\n    \"type\": \"integer\",\n    \"value\": \"3735928559\"\n  },\n  \"hex2\": {\n    \"type\": \"integer\",\n    \"value\": \"3735928559\"\n  },\n  \"hex3\": {\n    \"type\": \"integer\",\n    \"value\": \"3735928559\"\n  },\n  \"hex4\": {\n    \"type\": \"integer\",\n    \"value\": \"2439\"\n  },\n  \"oct1\": {\n    \"type\": \"integer\",\n    \"value\": \"342391\"\n  },\n  \"oct2\": {\n    \"type\": \"integer\",\n    \"value\": \"493\"\n  },\n  \"oct3\": {\n    \"type\": \"integer\",\n    \"value\": \"501\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Integer_Long(t *testing.T) {
	input := "int64-max = 9223372036854775807\nint64-max-neg = -9223372036854775808\n"
	jsonRef := "{\n  \"int64-max\": {\n    \"type\": \"integer\",\n    \"value\": \"9223372036854775807\"\n  },\n  \"int64-max-neg\": {\n    \"type\": \"integer\",\n    \"value\": \"-9223372036854775808\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Integer_Underscore(t *testing.T) {
	input := "kilo = 1_000\nx = 1_1_1_1\n"
	jsonRef := "{\n  \"kilo\": {\n    \"type\": \"integer\",\n    \"value\": \"1000\"\n  },\n  \"x\": {\n    \"type\": \"integer\",\n    \"value\": \"1111\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Integer_Zero(t *testing.T) {
	input := "d1 = 0\nd2 = +0\nd3 = -0\n\nh1 = 0x0\nh2 = 0x00\nh3 = 0x00000\n\no1 = 0o0\na2 = 0o00\na3 = 0o00000\n\nb1 = 0b0\nb2 = 0b00\nb3 = 0b00000\n"
	jsonRef := "{\n  \"a2\": {\n    \"type\": \"integer\",\n    \"value\": \"0\"\n  },\n  \"a3\": {\n    \"type\": \"integer\",\n    \"value\": \"0\"\n  },\n  \"b1\": {\n    \"type\": \"integer\",\n    \"value\": \"0\"\n  },\n  \"b2\": {\n    \"type\": \"integer\",\n    \"value\": \"0\"\n  },\n  \"b3\": {\n    \"type\": \"integer\",\n    \"value\": \"0\"\n  },\n  \"d1\": {\n    \"type\": \"integer\",\n    \"value\": \"0\"\n  },\n  \"d2\": {\n    \"type\": \"integer\",\n    \"value\": \"0\"\n  },\n  \"d3\": {\n    \"type\": \"integer\",\n    \"value\": \"0\"\n  },\n  \"h1\": {\n    \"type\": \"integer\",\n    \"value\": \"0\"\n  },\n  \"h2\": {\n    \"type\": \"integer\",\n    \"value\": \"0\"\n  },\n  \"h3\": {\n    \"type\": \"integer\",\n    \"value\": \"0\"\n  },\n  \"o1\": {\n    \"type\": \"integer\",\n    \"value\": \"0\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Key_Alphanum(t *testing.T) {
	input := "alpha = \"a\"\n123 = \"num\"\n000111 = \"leading\"\n10e3 = \"false float\"\none1two2 = \"mixed\"\nwith-dash = \"dashed\"\nunder_score = \"___\"\n34-11 = 23\n\n[2018_10]\n001 = 1\n\n[a-a-a]\n_ = false\n"
	jsonRef := "{\n  \"000111\": {\n    \"type\": \"string\",\n    \"value\": \"leading\"\n  },\n  \"10e3\": {\n    \"type\": \"string\",\n    \"value\": \"false float\"\n  },\n  \"123\": {\n    \"type\": \"string\",\n    \"value\": \"num\"\n  },\n  \"2018_10\": {\n    \"001\": {\n      \"type\": \"integer\",\n      \"value\": \"1\"\n    }\n  },\n  \"34-11\": {\n    \"type\": \"integer\",\n    \"value\": \"23\"\n  },\n  \"a-a-a\": {\n    \"_\": {\n      \"type\": \"bool\",\n      \"value\": \"false\"\n    }\n  },\n  \"alpha\": {\n    \"type\": \"string\",\n    \"value\": \"a\"\n  },\n  \"one1two2\": {\n    \"type\": \"string\",\n    \"value\": \"mixed\"\n  },\n  \"under_score\": {\n    \"type\": \"string\",\n    \"value\": \"___\"\n  },\n  \"with-dash\": {\n    \"type\": \"string\",\n    \"value\": \"dashed\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Key_CaseSensitive(t *testing.T) {
	input := "sectioN = \"NN\"\n\n[section]\nname = \"lower\"\nNAME = \"upper\"\nName = \"capitalized\"\n\n[Section]\nname = \"different section!!\"\n\"μ\" = \"greek small letter mu\"\n\"Μ\" = \"greek capital letter MU\"\nM = \"latin letter M\"\n\n"
	jsonRef := "{\n  \"Section\": {\n    \"M\": {\n      \"type\": \"string\",\n      \"value\": \"latin letter M\"\n    },\n    \"name\": {\n      \"type\": \"string\",\n      \"value\": \"different section!!\"\n    },\n    \"Μ\": {\n      \"type\": \"string\",\n      \"value\": \"greek capital letter MU\"\n    },\n    \"μ\": {\n      \"type\": \"string\",\n      \"value\": \"greek small letter mu\"\n    }\n  },\n  \"sectioN\": {\n    \"type\": \"string\",\n    \"value\": \"NN\"\n  },\n  \"section\": {\n    \"NAME\": {\n      \"type\": \"string\",\n      \"value\": \"upper\"\n    },\n    \"Name\": {\n      \"type\": \"string\",\n      \"value\": \"capitalized\"\n    },\n    \"name\": {\n      \"type\": \"string\",\n      \"value\": \"lower\"\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Key_DottedEmpty(t *testing.T) {
	input := "''.x = \"empty.x\"\nx.\"\" = \"x.empty\"\n[a]\n\"\".'' = \"empty.empty\"\n"
	jsonRef := "{\n  \"\": {\n    \"x\": {\n      \"type\": \"string\",\n      \"value\": \"empty.x\"\n    }\n  },\n  \"a\": {\n    \"\": {\n      \"\": {\n        \"type\": \"string\",\n        \"value\": \"empty.empty\"\n      }\n    }\n  },\n  \"x\": {\n    \"\": {\n      \"type\": \"string\",\n      \"value\": \"x.empty\"\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Key_Dotted(t *testing.T) {
	input := "# Note: this file contains literal tab characters.\n\nname.first = \"Arthur\"\n\"name\".'last' = \"Dent\"\n\nmany.dots.here.dot.dot.dot = 42\n\n# Space are ignored, and key parts can be quoted.\ncount.a       = 1\ncount . b     = 2\n\"count\".\"c\"   = 3\n\"count\" . \"d\" = 4\n'count'.'e'   = 5\n'count' . 'f' = 6\n\"count\".'g'   = 7\n\"count\" . 'h' = 8\ncount.'i'     = 9\ncount \t.\t 'j'\t   = 10\n\"count\".k     = 11\n\"count\" . l   = 12\n\n[tbl]\na.b.c = 42.666\n\n[a.few.dots]\npolka.dot = \"again?\"\npolka.dance-with = \"Dot\"\n\n[[arr]]\na.b.c=1\na.b.d=2\n\n[[arr]]\na.b.c=3\na.b.d=4\n"
	jsonRef := "{\n  \"a\": {\n    \"few\": {\n      \"dots\": {\n        \"polka\": {\n          \"dance-with\": {\n            \"type\": \"string\",\n            \"value\": \"Dot\"\n          },\n          \"dot\": {\n            \"type\": \"string\",\n            \"value\": \"again?\"\n          }\n        }\n      }\n    }\n  },\n  \"arr\": [\n    {\n      \"a\": {\n        \"b\": {\n          \"c\": {\n            \"type\": \"integer\",\n            \"value\": \"1\"\n          },\n          \"d\": {\n            \"type\": \"integer\",\n            \"value\": \"2\"\n          }\n        }\n      }\n    },\n    {\n      \"a\": {\n        \"b\": {\n          \"c\": {\n            \"type\": \"integer\",\n            \"value\": \"3\"\n          },\n          \"d\": {\n            \"type\": \"integer\",\n            \"value\": \"4\"\n          }\n        }\n      }\n    }\n  ],\n  \"count\": {\n    \"a\": {\n      \"type\": \"integer\",\n      \"value\": \"1\"\n    },\n    \"b\": {\n      \"type\": \"integer\",\n      \"value\": \"2\"\n    },\n    \"c\": {\n      \"type\": \"integer\",\n      \"value\": \"3\"\n    },\n    \"d\": {\n      \"type\": \"integer\",\n      \"value\": \"4\"\n    },\n    \"e\": {\n      \"type\": \"integer\",\n      \"value\": \"5\"\n    },\n    \"f\": {\n      \"type\": \"integer\",\n      \"value\": \"6\"\n    },\n    \"g\": {\n      \"type\": \"integer\",\n      \"value\": \"7\"\n    },\n    \"h\": {\n      \"type\": \"integer\",\n      \"value\": \"8\"\n    },\n    \"i\": {\n      \"type\": \"integer\",\n      \"value\": \"9\"\n    },\n    \"j\": {\n      \"type\": \"integer\",\n      \"value\": \"10\"\n    },\n    \"k\": {\n      \"type\": \"integer\",\n      \"value\": \"11\"\n    },\n    \"l\": {\n      \"type\": \"integer\",\n      \"value\": \"12\"\n    }\n  },\n  \"many\": {\n    \"dots\": {\n      \"here\": {\n        \"dot\": {\n          \"dot\": {\n            \"dot\": {\n              \"type\": \"integer\",\n              \"value\": \"42\"\n            }\n          }\n        }\n      }\n    }\n  },\n  \"name\": {\n    \"first\": {\n      \"type\": \"string\",\n      \"value\": \"Arthur\"\n    },\n    \"last\": {\n      \"type\": \"string\",\n      \"value\": \"Dent\"\n    }\n  },\n  \"tbl\": {\n    \"a\": {\n      \"b\": {\n        \"c\": {\n          \"type\": \"float\",\n          \"value\": \"42.666\"\n        }\n      }\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Key_Empty1(t *testing.T) {
	input := "\"\" = \"blank\"\n"
	jsonRef := "{\n  \"\": {\n    \"type\": \"string\",\n    \"value\": \"blank\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Key_Empty2(t *testing.T) {
	input := "'' = \"blank\"\n"
	jsonRef := "{\n  \"\": {\n    \"type\": \"string\",\n    \"value\": \"blank\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Key_Empty3(t *testing.T) {
	input := "''=0\n"
	jsonRef := "{\n  \"\": {\n    \"type\": \"integer\",\n    \"value\": \"0\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Key_EqualsNospace(t *testing.T) {
	input := "answer=42\n"
	jsonRef := "{\n  \"answer\": {\n    \"type\": \"integer\",\n    \"value\": \"42\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Key_Escapes(t *testing.T) {
	input := "\"\\n\" = \"newline\"\n\"\\b\" = \"bell\"\n\"\\u00c0\" = \"latin capital letter A with grave\"\n\"\\\"\" = \"just a quote\"\n\n[\"backsp\\b\\b\"]\n\n[\"\\\"quoted\\\"\"]\nquote = true\n\n[\"a.b\".\"\\u00c0\"]\n"
	jsonRef := "{\n  \"\\u0008\": {\n    \"type\": \"string\",\n    \"value\": \"bell\"\n  },\n  \"\\n\": {\n    \"type\": \"string\",\n    \"value\": \"newline\"\n  },\n  \"\\\"\": {\n    \"type\": \"string\",\n    \"value\": \"just a quote\"\n  },\n  \"\\\"quoted\\\"\": {\n    \"quote\": {\n      \"type\": \"bool\",\n      \"value\": \"true\"\n    }\n  },\n  \"a.b\": {\n    \"À\": {}\n  },\n  \"backsp\\u0008\\u0008\": {},\n  \"À\": {\n    \"type\": \"string\",\n    \"value\": \"latin capital letter A with grave\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Key_NumericDotted(t *testing.T) {
	input := "1.2 = 3\n"
	jsonRef := "{\n  \"1\": {\n    \"2\": {\n      \"type\": \"integer\",\n      \"value\": \"3\"\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Key_Numeric(t *testing.T) {
	input := "1 = 1\n"
	jsonRef := "{\n  \"1\": {\n    \"type\": \"integer\",\n    \"value\": \"1\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Key_QuotedDots(t *testing.T) {
	input := "plain = 1\n\"with.dot\" = 2\n\n[plain_table]\nplain = 3\n\"with.dot\" = 4\n\n[table.withdot]\nplain = 5\n\"key.with.dots\" = 6\n"
	jsonRef := "{\n  \"plain\": {\n    \"type\": \"integer\",\n    \"value\": \"1\"\n  },\n  \"plain_table\": {\n    \"plain\": {\n      \"type\": \"integer\",\n      \"value\": \"3\"\n    },\n    \"with.dot\": {\n      \"type\": \"integer\",\n      \"value\": \"4\"\n    }\n  },\n  \"table\": {\n    \"withdot\": {\n      \"key.with.dots\": {\n        \"type\": \"integer\",\n        \"value\": \"6\"\n      },\n      \"plain\": {\n        \"type\": \"integer\",\n        \"value\": \"5\"\n      }\n    }\n  },\n  \"with.dot\": {\n    \"type\": \"integer\",\n    \"value\": \"2\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Key_QuotedUnicode(t *testing.T) {
	input := "\n\"\\u0000\" = \"null\"\n'\\u0000' = \"different key\"\n\"\\u0008 \\u000c \\U00000041 \\u007f \\u0080 \\u00ff \\ud7ff \\ue000 \\uffff \\U00010000 \\U0010ffff\" = \"escaped key\"\n\n\"~ \u0080 ÿ \ud7ff \ue000 \uffff 𐀀 \U0010ffff\" = \"basic key\"\n'l ~ \u0080 ÿ \ud7ff \ue000 \uffff 𐀀 \U0010ffff' = \"literal key\"\n"
	jsonRef := "{\n  \"\\u0000\": {\n    \"type\": \"string\",\n    \"value\": \"null\"\n  },\n  \"\\u0008 \\u000c A \x7f \u0080 ÿ \ud7ff \ue000 \uffff 𐀀 \U0010ffff\": {\n    \"type\": \"string\",\n    \"value\": \"escaped key\"\n  },\n  \"\\\\u0000\": {\n    \"type\": \"string\",\n    \"value\": \"different key\"\n  },\n  \"l ~ \u0080 ÿ \ud7ff \ue000 \uffff 𐀀 \U0010ffff\": {\n    \"type\": \"string\",\n    \"value\": \"literal key\"\n  },\n  \"~ \u0080 ÿ \ud7ff \ue000 \uffff 𐀀 \U0010ffff\": {\n    \"type\": \"string\",\n    \"value\": \"basic key\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Key_Space(t *testing.T) {
	input := "# Keep whitespace inside quotes keys at all positions.\n\"a b\"   = 1\n\" c d \" = 2\n\n[ \" tbl \" ]\n\"\\ttab\\ttab\\t\" = \"tab\"\n"
	jsonRef := "{\n  \" c d \": {\n    \"type\": \"integer\",\n    \"value\": \"2\"\n  },\n  \" tbl \": {\n    \"\\ttab\\ttab\\t\": {\n      \"type\": \"string\",\n      \"value\": \"tab\"\n    }\n  },\n  \"a b\": {\n    \"type\": \"integer\",\n    \"value\": \"1\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Key_SpecialChars(t *testing.T) {
	input := "\"=~!@$^&*()_+-`1234567890[]|/?><.,;:'=\" = 1\n"
	jsonRef := "{\n  \"=~!@$^\\u0026*()_+-`1234567890[]|/?\\u003e\\u003c.,;:'=\": {\n    \"type\": \"integer\",\n    \"value\": \"1\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Key_SpecialWord(t *testing.T) {
	input := "false = false\ntrue = 1\ninf = 100000000\nnan = \"ceci n'est pas un nombre\"\n\n"
	jsonRef := "{\n  \"false\": {\n    \"type\": \"bool\",\n    \"value\": \"false\"\n  },\n  \"inf\": {\n    \"type\": \"integer\",\n    \"value\": \"100000000\"\n  },\n  \"nan\": {\n    \"type\": \"string\",\n    \"value\": \"ceci n'est pas un nombre\"\n  },\n  \"true\": {\n    \"type\": \"integer\",\n    \"value\": \"1\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Key_Zero(t *testing.T) {
	input := "0=0\n"
	jsonRef := "{\n  \"0\": {\n    \"type\": \"integer\",\n    \"value\": \"0\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Array0(t *testing.T) {
	input := "integers = [ 1, 2, 3 ]\ncolors = [ \"red\", \"yellow\", \"green\" ]\nnested_arrays_of_ints = [ [ 1, 2 ], [3, 4, 5] ]\nnested_mixed_array = [ [ 1, 2 ], [\"a\", \"b\", \"c\"] ]\nstring_array = [ \"all\", 'strings', \"\"\"are the same\"\"\", '''type''' ]\n\n# <AUTHOR> <EMAIL>\",\n  { name = \"Baz Qux\", email = \"<EMAIL>\", url = \"https://example.com/bazqux\" }\n]\n"
	jsonRef := "{\n  \"colors\": [\n    {\n      \"type\": \"string\",\n      \"value\": \"red\"\n    },\n    {\n      \"type\": \"string\",\n      \"value\": \"yellow\"\n    },\n    {\n      \"type\": \"string\",\n      \"value\": \"green\"\n    }\n  ],\n  \"contributors\": [\n    {\n      \"type\": \"string\",\n      \"value\": \"Foo Bar \\<EMAIL>\\u003e\"\n    },\n    {\n      \"email\": {\n        \"type\": \"string\",\n        \"value\": \"<EMAIL>\"\n      },\n      \"name\": {\n        \"type\": \"string\",\n        \"value\": \"Baz Qux\"\n      },\n      \"url\": {\n        \"type\": \"string\",\n        \"value\": \"https://example.com/bazqux\"\n      }\n    }\n  ],\n  \"integers\": [\n    {\n      \"type\": \"integer\",\n      \"value\": \"1\"\n    },\n    {\n      \"type\": \"integer\",\n      \"value\": \"2\"\n    },\n    {\n      \"type\": \"integer\",\n      \"value\": \"3\"\n    }\n  ],\n  \"nested_arrays_of_ints\": [\n    [\n      {\n        \"type\": \"integer\",\n        \"value\": \"1\"\n      },\n      {\n        \"type\": \"integer\",\n        \"value\": \"2\"\n      }\n    ],\n    [\n      {\n        \"type\": \"integer\",\n        \"value\": \"3\"\n      },\n      {\n        \"type\": \"integer\",\n        \"value\": \"4\"\n      },\n      {\n        \"type\": \"integer\",\n        \"value\": \"5\"\n      }\n    ]\n  ],\n  \"nested_mixed_array\": [\n    [\n      {\n        \"type\": \"integer\",\n        \"value\": \"1\"\n      },\n      {\n        \"type\": \"integer\",\n        \"value\": \"2\"\n      }\n    ],\n    [\n      {\n        \"type\": \"string\",\n        \"value\": \"a\"\n      },\n      {\n        \"type\": \"string\",\n        \"value\": \"b\"\n      },\n      {\n        \"type\": \"string\",\n        \"value\": \"c\"\n      }\n    ]\n  ],\n  \"numbers\": [\n    {\n      \"type\": \"float\",\n      \"value\": \"0.1\"\n    },\n    {\n      \"type\": \"float\",\n      \"value\": \"0.2\"\n    },\n    {\n      \"type\": \"float\",\n      \"value\": \"0.5\"\n    },\n    {\n      \"type\": \"integer\",\n      \"value\": \"1\"\n    },\n    {\n      \"type\": \"integer\",\n      \"value\": \"2\"\n    },\n    {\n      \"type\": \"integer\",\n      \"value\": \"5\"\n    }\n  ],\n  \"string_array\": [\n    {\n      \"type\": \"string\",\n      \"value\": \"all\"\n    },\n    {\n      \"type\": \"string\",\n      \"value\": \"strings\"\n    },\n    {\n      \"type\": \"string\",\n      \"value\": \"are the same\"\n    },\n    {\n      \"type\": \"string\",\n      \"value\": \"type\"\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Array1(t *testing.T) {
	input := "integers2 = [\n  1, 2, 3\n]\n\nintegers3 = [\n  1,\n  2, # this is ok\n]\n"
	jsonRef := "{\n  \"integers2\": [\n    {\n      \"type\": \"integer\",\n      \"value\": \"1\"\n    },\n    {\n      \"type\": \"integer\",\n      \"value\": \"2\"\n    },\n    {\n      \"type\": \"integer\",\n      \"value\": \"3\"\n    }\n  ],\n  \"integers3\": [\n    {\n      \"type\": \"integer\",\n      \"value\": \"1\"\n    },\n    {\n      \"type\": \"integer\",\n      \"value\": \"2\"\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_ArrayOfTables0(t *testing.T) {
	input := "[[products]]\nname = \"Hammer\"\nsku = 738594937\n\n[[products]]  # empty table within the array\n\n[[products]]\nname = \"Nail\"\nsku = 284758393\n\ncolor = \"gray\"\n"
	jsonRef := "{\n  \"products\": [\n    {\n      \"name\": {\n        \"type\": \"string\",\n        \"value\": \"Hammer\"\n      },\n      \"sku\": {\n        \"type\": \"integer\",\n        \"value\": \"738594937\"\n      }\n    },\n    {},\n    {\n      \"color\": {\n        \"type\": \"string\",\n        \"value\": \"gray\"\n      },\n      \"name\": {\n        \"type\": \"string\",\n        \"value\": \"Nail\"\n      },\n      \"sku\": {\n        \"type\": \"integer\",\n        \"value\": \"284758393\"\n      }\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_ArrayOfTables1(t *testing.T) {
	input := "[[fruits]]\nname = \"apple\"\n\n[fruits.physical]  # subtable\ncolor = \"red\"\nshape = \"round\"\n\n[[fruits.varieties]]  # nested array of tables\nname = \"red delicious\"\n\n[[fruits.varieties]]\nname = \"granny smith\"\n\n\n[[fruits]]\nname = \"banana\"\n\n[[fruits.varieties]]\nname = \"plantain\"\n"
	jsonRef := "{\n  \"fruits\": [\n    {\n      \"name\": {\n        \"type\": \"string\",\n        \"value\": \"apple\"\n      },\n      \"physical\": {\n        \"color\": {\n          \"type\": \"string\",\n          \"value\": \"red\"\n        },\n        \"shape\": {\n          \"type\": \"string\",\n          \"value\": \"round\"\n        }\n      },\n      \"varieties\": [\n        {\n          \"name\": {\n            \"type\": \"string\",\n            \"value\": \"red delicious\"\n          }\n        },\n        {\n          \"name\": {\n            \"type\": \"string\",\n            \"value\": \"granny smith\"\n          }\n        }\n      ]\n    },\n    {\n      \"name\": {\n        \"type\": \"string\",\n        \"value\": \"banana\"\n      },\n      \"varieties\": [\n        {\n          \"name\": {\n            \"type\": \"string\",\n            \"value\": \"plantain\"\n          }\n        }\n      ]\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_ArrayOfTables2(t *testing.T) {
	input := "points = [ { x = 1, y = 2, z = 3 },\n           { x = 7, y = 8, z = 9 },\n           { x = 2, y = 4, z = 8 } ]\n"
	jsonRef := "{\n  \"points\": [\n    {\n      \"x\": {\n        \"type\": \"integer\",\n        \"value\": \"1\"\n      },\n      \"y\": {\n        \"type\": \"integer\",\n        \"value\": \"2\"\n      },\n      \"z\": {\n        \"type\": \"integer\",\n        \"value\": \"3\"\n      }\n    },\n    {\n      \"x\": {\n        \"type\": \"integer\",\n        \"value\": \"7\"\n      },\n      \"y\": {\n        \"type\": \"integer\",\n        \"value\": \"8\"\n      },\n      \"z\": {\n        \"type\": \"integer\",\n        \"value\": \"9\"\n      }\n    },\n    {\n      \"x\": {\n        \"type\": \"integer\",\n        \"value\": \"2\"\n      },\n      \"y\": {\n        \"type\": \"integer\",\n        \"value\": \"4\"\n      },\n      \"z\": {\n        \"type\": \"integer\",\n        \"value\": \"8\"\n      }\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Boolean0(t *testing.T) {
	input := "bool1 = true\nbool2 = false\n"
	jsonRef := "{\n  \"bool1\": {\n    \"type\": \"bool\",\n    \"value\": \"true\"\n  },\n  \"bool2\": {\n    \"type\": \"bool\",\n    \"value\": \"false\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Comment0(t *testing.T) {
	input := "# This is a full-line comment\nkey = \"value\"  # This is a comment at the end of a line\nanother = \"# This is not a comment\"\n"
	jsonRef := "{\n  \"another\": {\n    \"type\": \"string\",\n    \"value\": \"# This is not a comment\"\n  },\n  \"key\": {\n    \"type\": \"string\",\n    \"value\": \"value\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Float0(t *testing.T) {
	input := "# fractional\nflt1 = +1.0\nflt2 = 3.1415\nflt3 = -0.01\n\n# exponent\nflt4 = 5e+22\nflt5 = 1e06\nflt6 = -2E-2\n\n# both\nflt7 = 6.626e-34\n"
	jsonRef := "{\n  \"flt1\": {\n    \"type\": \"float\",\n    \"value\": \"1\"\n  },\n  \"flt2\": {\n    \"type\": \"float\",\n    \"value\": \"3.1415\"\n  },\n  \"flt3\": {\n    \"type\": \"float\",\n    \"value\": \"-0.01\"\n  },\n  \"flt4\": {\n    \"type\": \"float\",\n    \"value\": \"5e+22\"\n  },\n  \"flt5\": {\n    \"type\": \"float\",\n    \"value\": \"1e+06\"\n  },\n  \"flt6\": {\n    \"type\": \"float\",\n    \"value\": \"-0.02\"\n  },\n  \"flt7\": {\n    \"type\": \"float\",\n    \"value\": \"6.626e-34\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Float1(t *testing.T) {
	input := "flt8 = 224_617.445_991_228\n"
	jsonRef := "{\n  \"flt8\": {\n    \"type\": \"float\",\n    \"value\": \"224617.445991228\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Float2(t *testing.T) {
	input := "# infinity\nsf1 = inf  # positive infinity\nsf2 = +inf # positive infinity\nsf3 = -inf # negative infinity\n\n# not a number\nsf4 = nan  # actual sNaN/qNaN encoding is implementation-specific\nsf5 = +nan # same as `nan`\nsf6 = -nan # valid, actual encoding is implementation-specific\n"
	jsonRef := "{\n  \"sf1\": {\n    \"type\": \"float\",\n    \"value\": \"+inf\"\n  },\n  \"sf2\": {\n    \"type\": \"float\",\n    \"value\": \"+inf\"\n  },\n  \"sf3\": {\n    \"type\": \"float\",\n    \"value\": \"-inf\"\n  },\n  \"sf4\": {\n    \"type\": \"float\",\n    \"value\": \"nan\"\n  },\n  \"sf5\": {\n    \"type\": \"float\",\n    \"value\": \"nan\"\n  },\n  \"sf6\": {\n    \"type\": \"float\",\n    \"value\": \"nan\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_InlineTable0(t *testing.T) {
	input := "name = { first = \"Tom\", last = \"Preston-Werner\" }\npoint = { x = 1, y = 2 }\nanimal = { type.name = \"pug\" }\n"
	jsonRef := "{\n  \"animal\": {\n    \"type\": {\n      \"name\": {\n        \"type\": \"string\",\n        \"value\": \"pug\"\n      }\n    }\n  },\n  \"name\": {\n    \"first\": {\n      \"type\": \"string\",\n      \"value\": \"Tom\"\n    },\n    \"last\": {\n      \"type\": \"string\",\n      \"value\": \"Preston-Werner\"\n    }\n  },\n  \"point\": {\n    \"x\": {\n      \"type\": \"integer\",\n      \"value\": \"1\"\n    },\n    \"y\": {\n      \"type\": \"integer\",\n      \"value\": \"2\"\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_InlineTable1(t *testing.T) {
	input := "[name]\nfirst = \"Tom\"\nlast = \"Preston-Werner\"\n\n[point]\nx = 1\ny = 2\n\n[animal]\ntype.name = \"pug\"\n"
	jsonRef := "{\n  \"animal\": {\n    \"type\": {\n      \"name\": {\n        \"type\": \"string\",\n        \"value\": \"pug\"\n      }\n    }\n  },\n  \"name\": {\n    \"first\": {\n      \"type\": \"string\",\n      \"value\": \"Tom\"\n    },\n    \"last\": {\n      \"type\": \"string\",\n      \"value\": \"Preston-Werner\"\n    }\n  },\n  \"point\": {\n    \"x\": {\n      \"type\": \"integer\",\n      \"value\": \"1\"\n    },\n    \"y\": {\n      \"type\": \"integer\",\n      \"value\": \"2\"\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_InlineTable2(t *testing.T) {
	input := "[product]\ntype = { name = \"Nail\" }\n# type.edible = false  # INVALID\n"
	jsonRef := "{\n  \"product\": {\n    \"type\": {\n      \"name\": {\n        \"type\": \"string\",\n        \"value\": \"Nail\"\n      }\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_InlineTable3(t *testing.T) {
	input := "[product]\ntype.name = \"Nail\"\n# type = { edible = false }  # INVALID\n"
	jsonRef := "{\n  \"product\": {\n    \"type\": {\n      \"name\": {\n        \"type\": \"string\",\n        \"value\": \"Nail\"\n      }\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Integer0(t *testing.T) {
	input := "int1 = +99\nint2 = 42\nint3 = 0\nint4 = -17\n"
	jsonRef := "{\n  \"int1\": {\n    \"type\": \"integer\",\n    \"value\": \"99\"\n  },\n  \"int2\": {\n    \"type\": \"integer\",\n    \"value\": \"42\"\n  },\n  \"int3\": {\n    \"type\": \"integer\",\n    \"value\": \"0\"\n  },\n  \"int4\": {\n    \"type\": \"integer\",\n    \"value\": \"-17\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Integer1(t *testing.T) {
	input := "int5 = 1_000\nint6 = 5_349_221\nint7 = 53_49_221  # Indian number system grouping\nint8 = 1_2_3_4_5  # VALID but discouraged\n"
	jsonRef := "{\n  \"int5\": {\n    \"type\": \"integer\",\n    \"value\": \"1000\"\n  },\n  \"int6\": {\n    \"type\": \"integer\",\n    \"value\": \"5349221\"\n  },\n  \"int7\": {\n    \"type\": \"integer\",\n    \"value\": \"5349221\"\n  },\n  \"int8\": {\n    \"type\": \"integer\",\n    \"value\": \"12345\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Integer2(t *testing.T) {
	input := "# hexadecimal with prefix `0x`\nhex1 = 0xDEADBEEF\nhex2 = 0xdeadbeef\nhex3 = 0xdead_beef\n\n# octal with prefix `0o`\noct1 = 0o01234567\noct2 = 0o755 # useful for Unix file permissions\n\n# binary with prefix `0b`\nbin1 = 0b11010110\n"
	jsonRef := "{\n  \"bin1\": {\n    \"type\": \"integer\",\n    \"value\": \"214\"\n  },\n  \"hex1\": {\n    \"type\": \"integer\",\n    \"value\": \"3735928559\"\n  },\n  \"hex2\": {\n    \"type\": \"integer\",\n    \"value\": \"3735928559\"\n  },\n  \"hex3\": {\n    \"type\": \"integer\",\n    \"value\": \"3735928559\"\n  },\n  \"oct1\": {\n    \"type\": \"integer\",\n    \"value\": \"342391\"\n  },\n  \"oct2\": {\n    \"type\": \"integer\",\n    \"value\": \"493\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_KeyValuePair0(t *testing.T) {
	input := "key = \"value\"\n"
	jsonRef := "{\n  \"key\": {\n    \"type\": \"string\",\n    \"value\": \"value\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Keys0(t *testing.T) {
	input := "key = \"value\"\nbare_key = \"value\"\nbare-key = \"value\"\n1234 = \"value\"\n"
	jsonRef := "{\n  \"1234\": {\n    \"type\": \"string\",\n    \"value\": \"value\"\n  },\n  \"bare-key\": {\n    \"type\": \"string\",\n    \"value\": \"value\"\n  },\n  \"bare_key\": {\n    \"type\": \"string\",\n    \"value\": \"value\"\n  },\n  \"key\": {\n    \"type\": \"string\",\n    \"value\": \"value\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Keys1(t *testing.T) {
	input := "\"127.0.0.1\" = \"value\"\n\"character encoding\" = \"value\"\n\"ʎǝʞ\" = \"value\"\n'key2' = \"value\"\n'quoted \"value\"' = \"value\"\n"
	jsonRef := "{\n  \"127.0.0.1\": {\n    \"type\": \"string\",\n    \"value\": \"value\"\n  },\n  \"character encoding\": {\n    \"type\": \"string\",\n    \"value\": \"value\"\n  },\n  \"key2\": {\n    \"type\": \"string\",\n    \"value\": \"value\"\n  },\n  \"quoted \\\"value\\\"\": {\n    \"type\": \"string\",\n    \"value\": \"value\"\n  },\n  \"ʎǝʞ\": {\n    \"type\": \"string\",\n    \"value\": \"value\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Keys3(t *testing.T) {
	input := "name = \"Orange\"\nphysical.color = \"orange\"\nphysical.shape = \"round\"\nsite.\"google.com\" = true\n"
	jsonRef := "{\n  \"name\": {\n    \"type\": \"string\",\n    \"value\": \"Orange\"\n  },\n  \"physical\": {\n    \"color\": {\n      \"type\": \"string\",\n      \"value\": \"orange\"\n    },\n    \"shape\": {\n      \"type\": \"string\",\n      \"value\": \"round\"\n    }\n  },\n  \"site\": {\n    \"google.com\": {\n      \"type\": \"bool\",\n      \"value\": \"true\"\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Keys4(t *testing.T) {
	input := "fruit.name = \"banana\"     # this is best practice\nfruit. color = \"yellow\"    # same as fruit.color\nfruit . flavor = \"banana\"   # same as fruit.flavor\n"
	jsonRef := "{\n  \"fruit\": {\n    \"color\": {\n      \"type\": \"string\",\n      \"value\": \"yellow\"\n    },\n    \"flavor\": {\n      \"type\": \"string\",\n      \"value\": \"banana\"\n    },\n    \"name\": {\n      \"type\": \"string\",\n      \"value\": \"banana\"\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Keys5(t *testing.T) {
	input := "# VALID BUT DISCOURAGED\n\napple.type = \"fruit\"\norange.type = \"fruit\"\n\napple.skin = \"thin\"\norange.skin = \"thick\"\n\napple.color = \"red\"\norange.color = \"orange\"\n"
	jsonRef := "{\n  \"apple\": {\n    \"color\": {\n      \"type\": \"string\",\n      \"value\": \"red\"\n    },\n    \"skin\": {\n      \"type\": \"string\",\n      \"value\": \"thin\"\n    },\n    \"type\": {\n      \"type\": \"string\",\n      \"value\": \"fruit\"\n    }\n  },\n  \"orange\": {\n    \"color\": {\n      \"type\": \"string\",\n      \"value\": \"orange\"\n    },\n    \"skin\": {\n      \"type\": \"string\",\n      \"value\": \"thick\"\n    },\n    \"type\": {\n      \"type\": \"string\",\n      \"value\": \"fruit\"\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Keys6(t *testing.T) {
	input := "# RECOMMENDED\n\napple.type = \"fruit\"\napple.skin = \"thin\"\napple.color = \"red\"\n\norange.type = \"fruit\"\norange.skin = \"thick\"\norange.color = \"orange\"\n"
	jsonRef := "{\n  \"apple\": {\n    \"color\": {\n      \"type\": \"string\",\n      \"value\": \"red\"\n    },\n    \"skin\": {\n      \"type\": \"string\",\n      \"value\": \"thin\"\n    },\n    \"type\": {\n      \"type\": \"string\",\n      \"value\": \"fruit\"\n    }\n  },\n  \"orange\": {\n    \"color\": {\n      \"type\": \"string\",\n      \"value\": \"orange\"\n    },\n    \"skin\": {\n      \"type\": \"string\",\n      \"value\": \"thick\"\n    },\n    \"type\": {\n      \"type\": \"string\",\n      \"value\": \"fruit\"\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Keys7(t *testing.T) {
	input := "3.14159 = \"pi\"\n"
	jsonRef := "{\n  \"3\": {\n    \"14159\": {\n      \"type\": \"string\",\n      \"value\": \"pi\"\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_LocalDate0(t *testing.T) {
	input := "ld1 = 1979-05-27\n"
	jsonRef := "{\n  \"ld1\": {\n    \"type\": \"date-local\",\n    \"value\": \"1979-05-27\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_LocalDateTime0(t *testing.T) {
	input := "ldt1 = 1979-05-27T07:32:00\nldt2 = 1979-05-27T00:32:00.999999\n"
	jsonRef := "{\n  \"ldt1\": {\n    \"type\": \"datetime-local\",\n    \"value\": \"1979-05-27T07:32:00\"\n  },\n  \"ldt2\": {\n    \"type\": \"datetime-local\",\n    \"value\": \"1979-05-27T00:32:00.999999\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_LocalTime0(t *testing.T) {
	input := "lt1 = 07:32:00\nlt2 = 00:32:00.999999\n"
	jsonRef := "{\n  \"lt1\": {\n    \"type\": \"time-local\",\n    \"value\": \"07:32:00\"\n  },\n  \"lt2\": {\n    \"type\": \"time-local\",\n    \"value\": \"00:32:00.999999\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_OffsetDateTime0(t *testing.T) {
	input := "odt1 = 1979-05-27T07:32:00Z\nodt2 = 1979-05-27T00:32:00-07:00\nodt3 = 1979-05-27T00:32:00.999999-07:00\n"
	jsonRef := "{\n  \"odt1\": {\n    \"type\": \"datetime\",\n    \"value\": \"1979-05-27T07:32:00Z\"\n  },\n  \"odt2\": {\n    \"type\": \"datetime\",\n    \"value\": \"1979-05-27T00:32:00-07:00\"\n  },\n  \"odt3\": {\n    \"type\": \"datetime\",\n    \"value\": \"1979-05-27T00:32:00.999999-07:00\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_OffsetDateTime1(t *testing.T) {
	input := "odt4 = 1979-05-27 07:32:00Z\n"
	jsonRef := "{\n  \"odt4\": {\n    \"type\": \"datetime\",\n    \"value\": \"1979-05-27T07:32:00Z\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_String0(t *testing.T) {
	input := "str = \"I'm a string. \\\"You can quote me\\\". Name\\tJos\\u00E9\\nLocation\\tSF.\"\n"
	jsonRef := "{\n  \"str\": {\n    \"type\": \"string\",\n    \"value\": \"I'm a string. \\\"You can quote me\\\". Name\\tJosé\\nLocation\\tSF.\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_String1(t *testing.T) {
	input := "str1 = \"\"\"\nRoses are red\nViolets are blue\"\"\"\n"
	jsonRef := "{\n  \"str1\": {\n    \"type\": \"string\",\n    \"value\": \"Roses are red\\nViolets are blue\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_String2(t *testing.T) {
	input := "# On a Unix system, the above multi-line string will most likely be the same as:\nstr2 = \"Roses are red\\nViolets are blue\"\n\n# On a Windows system, it will most likely be equivalent to:\nstr3 = \"Roses are red\\r\\nViolets are blue\"\n"
	jsonRef := "{\n  \"str2\": {\n    \"type\": \"string\",\n    \"value\": \"Roses are red\\nViolets are blue\"\n  },\n  \"str3\": {\n    \"type\": \"string\",\n    \"value\": \"Roses are red\\r\\nViolets are blue\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_String3(t *testing.T) {
	input := "# The following strings are byte-for-byte equivalent:\nstr1 = \"The quick brown fox jumps over the lazy dog.\"\n\nstr2 = \"\"\"\nThe quick brown \\\n\n\n  fox jumps over \\\n    the lazy dog.\"\"\"\n\nstr3 = \"\"\"\\\n       The quick brown \\\n       fox jumps over \\\n       the lazy dog.\\\n       \"\"\"\n"
	jsonRef := "{\n  \"str1\": {\n    \"type\": \"string\",\n    \"value\": \"The quick brown fox jumps over the lazy dog.\"\n  },\n  \"str2\": {\n    \"type\": \"string\",\n    \"value\": \"The quick brown fox jumps over the lazy dog.\"\n  },\n  \"str3\": {\n    \"type\": \"string\",\n    \"value\": \"The quick brown fox jumps over the lazy dog.\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_String4(t *testing.T) {
	input := "str4 = \"\"\"Here are two quotation marks: \"\". Simple enough.\"\"\"\n# str5 = \"\"\"Here are three quotation marks: \"\"\".\"\"\"  # INVALID\nstr5 = \"\"\"Here are three quotation marks: \"\"\\\".\"\"\"\nstr6 = \"\"\"Here are fifteen quotation marks: \"\"\\\"\"\"\\\"\"\"\\\"\"\"\\\"\"\"\\\".\"\"\"\n\n# \"This,\" she said, \"is just a pointless statement.\"\nstr7 = \"\"\"\"This,\" she said, \"is just a pointless statement.\"\"\"\"\n"
	jsonRef := "{\n  \"str4\": {\n    \"type\": \"string\",\n    \"value\": \"Here are two quotation marks: \\\"\\\". Simple enough.\"\n  },\n  \"str5\": {\n    \"type\": \"string\",\n    \"value\": \"Here are three quotation marks: \\\"\\\"\\\".\"\n  },\n  \"str6\": {\n    \"type\": \"string\",\n    \"value\": \"Here are fifteen quotation marks: \\\"\\\"\\\"\\\"\\\"\\\"\\\"\\\"\\\"\\\"\\\"\\\"\\\"\\\"\\\".\"\n  },\n  \"str7\": {\n    \"type\": \"string\",\n    \"value\": \"\\\"This,\\\" she said, \\\"is just a pointless statement.\\\"\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_String5(t *testing.T) {
	input := "# What you see is what you get.\nwinpath  = 'C:\\Users\\<USER>\\templates'\nwinpath2 = '\\\\ServerX\\admin$\\system32\\'\nquoted   = 'Tom \"Dubs\" Preston-Werner'\nregex    = '<\\i\\c*\\s*>'\n"
	jsonRef := "{\n  \"quoted\": {\n    \"type\": \"string\",\n    \"value\": \"Tom \\\"Dubs\\\" Preston-Werner\"\n  },\n  \"regex\": {\n    \"type\": \"string\",\n    \"value\": \"\\u003c\\\\i\\\\c*\\\\s*\\u003e\"\n  },\n  \"winpath\": {\n    \"type\": \"string\",\n    \"value\": \"C:\\\\Users\\\\<USER>\\\\templates\"\n  },\n  \"winpath2\": {\n    \"type\": \"string\",\n    \"value\": \"\\\\\\\\ServerX\\\\admin$\\\\system32\\\\\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_String6(t *testing.T) {
	input := "regex2 = '''I [dw]on't need \\d{2} apples'''\nlines  = '''\nThe first newline is\ntrimmed in raw strings.\n   All other whitespace\n   is preserved.\n'''\n"
	jsonRef := "{\n  \"lines\": {\n    \"type\": \"string\",\n    \"value\": \"The first newline is\\ntrimmed in raw strings.\\n   All other whitespace\\n   is preserved.\\n\"\n  },\n  \"regex2\": {\n    \"type\": \"string\",\n    \"value\": \"I [dw]on't need \\\\d{2} apples\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_String7(t *testing.T) {
	input := "quot15 = '''Here are fifteen quotation marks: \"\"\"\"\"\"\"\"\"\"\"\"\"\"\"'''\n\n# apos15 = '''Here are fifteen apostrophes: ''''''''''''''''''  # INVALID\napos15 = \"Here are fifteen apostrophes: '''''''''''''''\"\n\n# 'That,' she said, 'is still pointless.'\nstr = ''''That,' she said, 'is still pointless.''''\n"
	jsonRef := "{\n  \"apos15\": {\n    \"type\": \"string\",\n    \"value\": \"Here are fifteen apostrophes: '''''''''''''''\"\n  },\n  \"quot15\": {\n    \"type\": \"string\",\n    \"value\": \"Here are fifteen quotation marks: \\\"\\\"\\\"\\\"\\\"\\\"\\\"\\\"\\\"\\\"\\\"\\\"\\\"\\\"\\\"\"\n  },\n  \"str\": {\n    \"type\": \"string\",\n    \"value\": \"'That,' she said, 'is still pointless.'\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Table0(t *testing.T) {
	input := "[table]\n"
	jsonRef := "{\n  \"table\": {}\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Table1(t *testing.T) {
	input := "[table-1]\nkey1 = \"some string\"\nkey2 = 123\n\n[table-2]\nkey1 = \"another string\"\nkey2 = 456\n"
	jsonRef := "{\n  \"table-1\": {\n    \"key1\": {\n      \"type\": \"string\",\n      \"value\": \"some string\"\n    },\n    \"key2\": {\n      \"type\": \"integer\",\n      \"value\": \"123\"\n    }\n  },\n  \"table-2\": {\n    \"key1\": {\n      \"type\": \"string\",\n      \"value\": \"another string\"\n    },\n    \"key2\": {\n      \"type\": \"integer\",\n      \"value\": \"456\"\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Table2(t *testing.T) {
	input := "[dog.\"tater.man\"]\ntype.name = \"pug\"\n"
	jsonRef := "{\n  \"dog\": {\n    \"tater.man\": {\n      \"type\": {\n        \"name\": {\n          \"type\": \"string\",\n          \"value\": \"pug\"\n        }\n      }\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Table3(t *testing.T) {
	input := "[a.b.c]            # this is best practice\n[ d.e.f ]          # same as [d.e.f]\n[ g .  h  . i ]    # same as [g.h.i]\n[ j . \"ʞ\" . 'l' ]  # same as [j.\"ʞ\".'l']\n"
	jsonRef := "{\n  \"a\": {\n    \"b\": {\n      \"c\": {}\n    }\n  },\n  \"d\": {\n    \"e\": {\n      \"f\": {}\n    }\n  },\n  \"g\": {\n    \"h\": {\n      \"i\": {}\n    }\n  },\n  \"j\": {\n    \"ʞ\": {\n      \"l\": {}\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Table4(t *testing.T) {
	input := "# [x] you\n# [x.y] don't\n# [x.y.z] need these\n[x.y.z.w] # for this to work\n\n[x] # defining a super-table afterward is ok\n"
	jsonRef := "{\n  \"x\": {\n    \"y\": {\n      \"z\": {\n        \"w\": {}\n      }\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Table5(t *testing.T) {
	input := "# VALID BUT DISCOURAGED\n[fruit.apple]\n[animal]\n[fruit.orange]\n"
	jsonRef := "{\n  \"animal\": {},\n  \"fruit\": {\n    \"apple\": {},\n    \"orange\": {}\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Table6(t *testing.T) {
	input := "# RECOMMENDED\n[fruit.apple]\n[fruit.orange]\n[animal]\n"
	jsonRef := "{\n  \"animal\": {},\n  \"fruit\": {\n    \"apple\": {},\n    \"orange\": {}\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Table7(t *testing.T) {
	input := "# Top-level table begins.\nname = \"Fido\"\nbreed = \"pug\"\n\n# Top-level table ends.\n[owner]\nname = \"Regina Dogman\"\nmember_since = 1999-08-04\n"
	jsonRef := "{\n  \"breed\": {\n    \"type\": \"string\",\n    \"value\": \"pug\"\n  },\n  \"name\": {\n    \"type\": \"string\",\n    \"value\": \"Fido\"\n  },\n  \"owner\": {\n    \"member_since\": {\n      \"type\": \"date-local\",\n      \"value\": \"1999-08-04\"\n    },\n    \"name\": {\n      \"type\": \"string\",\n      \"value\": \"Regina Dogman\"\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Table8(t *testing.T) {
	input := "fruit.apple.color = \"red\"\n# Defines a table named fruit\n# Defines a table named fruit.apple\n\nfruit.apple.taste.sweet = true\n# Defines a table named fruit.apple.taste\n# fruit and fruit.apple were already created\n"
	jsonRef := "{\n  \"fruit\": {\n    \"apple\": {\n      \"color\": {\n        \"type\": \"string\",\n        \"value\": \"red\"\n      },\n      \"taste\": {\n        \"sweet\": {\n          \"type\": \"bool\",\n          \"value\": \"true\"\n        }\n      }\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Spec_Table9(t *testing.T) {
	input := "[fruit]\napple.color = \"red\"\napple.taste.sweet = true\n\n# [fruit.apple]  # INVALID\n# [fruit.apple.taste]  # INVALID\n\n[fruit.apple.texture]  # you can add sub-tables\nsmooth = true\n"
	jsonRef := "{\n  \"fruit\": {\n    \"apple\": {\n      \"color\": {\n        \"type\": \"string\",\n        \"value\": \"red\"\n      },\n      \"taste\": {\n        \"sweet\": {\n          \"type\": \"bool\",\n          \"value\": \"true\"\n        }\n      },\n      \"texture\": {\n        \"smooth\": {\n          \"type\": \"bool\",\n          \"value\": \"true\"\n        }\n      }\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_String_DoubleQuoteEscape(t *testing.T) {
	input := "test = \"\\\"one\\\"\"\n"
	jsonRef := "{\n  \"test\": {\n    \"type\": \"string\",\n    \"value\": \"\\\"one\\\"\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_String_Empty(t *testing.T) {
	input := "answer = \"\"\n"
	jsonRef := "{\n  \"answer\": {\n    \"type\": \"string\",\n    \"value\": \"\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_String_EndsInWhitespaceEscape(t *testing.T) {
	input := "beee = \"\"\"\nheeee\ngeeee\\  \n\n\n      \"\"\"\n"
	jsonRef := "{\n  \"beee\": {\n    \"type\": \"string\",\n    \"value\": \"heeee\\ngeeee\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_String_EscapeTricky(t *testing.T) {
	input := "end_esc = \"String does not end here\\\" but ends here\\\\\"\nlit_end_esc = 'String ends here\\'\n\nmultiline_unicode = \"\"\"\n\\u00a0\"\"\"\n\nmultiline_not_unicode = \"\"\"\n\\\\u0041\"\"\"\n\nmultiline_end_esc = \"\"\"When will it end? \\\"\"\"...\"\"\\\" should be here\\\"\"\"\"\n\nlit_multiline_not_unicode = '''\n\\u007f'''\n\nlit_multiline_end = '''There is no escape\\'''\n"
	jsonRef := "{\n  \"end_esc\": {\n    \"type\": \"string\",\n    \"value\": \"String does not end here\\\" but ends here\\\\\"\n  },\n  \"lit_end_esc\": {\n    \"type\": \"string\",\n    \"value\": \"String ends here\\\\\"\n  },\n  \"lit_multiline_end\": {\n    \"type\": \"string\",\n    \"value\": \"There is no escape\\\\\"\n  },\n  \"lit_multiline_not_unicode\": {\n    \"type\": \"string\",\n    \"value\": \"\\\\u007f\"\n  },\n  \"multiline_end_esc\": {\n    \"type\": \"string\",\n    \"value\": \"When will it end? \\\"\\\"\\\"...\\\"\\\"\\\" should be here\\\"\"\n  },\n  \"multiline_not_unicode\": {\n    \"type\": \"string\",\n    \"value\": \"\\\\u0041\"\n  },\n  \"multiline_unicode\": {\n    \"type\": \"string\",\n    \"value\": \"\u00a0\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_String_EscapedEscape(t *testing.T) {
	input := "answer = \"\\\\x64\"\n"
	jsonRef := "{\n  \"answer\": {\n    \"type\": \"string\",\n    \"value\": \"\\\\x64\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_String_Escapes(t *testing.T) {
	input := "backspace = \"This string has a \\b backspace character.\"\ntab = \"This string has a \\t tab character.\"\nnewline = \"This string has a \\n new line character.\"\nformfeed = \"This string has a \\f form feed character.\"\ncarriage = \"This string has a \\r carriage return character.\"\nquote = \"This string has a \\\" quote character.\"\nbackslash = \"This string has a \\\\ backslash character.\"\nnotunicode1 = \"This string does not have a unicode \\\\u escape.\"\nnotunicode2 = \"This string does not have a unicode \\u005Cu escape.\"\nnotunicode3 = \"This string does not have a unicode \\\\u0075 escape.\"\nnotunicode4 = \"This string does not have a unicode \\\\\\u0075 escape.\"\ndelete = \"This string has a \\u007F delete control code.\"\nunitseparator = \"This string has a \\u001F unit separator control code.\"\n"
	jsonRef := "{\n  \"backslash\": {\n    \"type\": \"string\",\n    \"value\": \"This string has a \\\\ backslash character.\"\n  },\n  \"backspace\": {\n    \"type\": \"string\",\n    \"value\": \"This string has a \\u0008 backspace character.\"\n  },\n  \"carriage\": {\n    \"type\": \"string\",\n    \"value\": \"This string has a \\r carriage return character.\"\n  },\n  \"delete\": {\n    \"type\": \"string\",\n    \"value\": \"This string has a \x7f delete control code.\"\n  },\n  \"formfeed\": {\n    \"type\": \"string\",\n    \"value\": \"This string has a \\u000c form feed character.\"\n  },\n  \"newline\": {\n    \"type\": \"string\",\n    \"value\": \"This string has a \\n new line character.\"\n  },\n  \"notunicode1\": {\n    \"type\": \"string\",\n    \"value\": \"This string does not have a unicode \\\\u escape.\"\n  },\n  \"notunicode2\": {\n    \"type\": \"string\",\n    \"value\": \"This string does not have a unicode \\\\u escape.\"\n  },\n  \"notunicode3\": {\n    \"type\": \"string\",\n    \"value\": \"This string does not have a unicode \\\\u0075 escape.\"\n  },\n  \"notunicode4\": {\n    \"type\": \"string\",\n    \"value\": \"This string does not have a unicode \\\\u escape.\"\n  },\n  \"quote\": {\n    \"type\": \"string\",\n    \"value\": \"This string has a \\\" quote character.\"\n  },\n  \"tab\": {\n    \"type\": \"string\",\n    \"value\": \"This string has a \\t tab character.\"\n  },\n  \"unitseparator\": {\n    \"type\": \"string\",\n    \"value\": \"This string has a \\u001f unit separator control code.\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_String_MultilineEscapedCrlf(t *testing.T) {
	input := "# The following line should be an unescaped backslash followed by a Windows\r\n# newline sequence (\"\\r\\n\")\r\n0=\"\"\"\\\r\n\"\"\"\r\n"
	jsonRef := "{\n  \"0\": {\n    \"type\": \"string\",\n    \"value\": \"\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_String_MultilineQuotes(t *testing.T) {
	input := "# Make sure that quotes inside multiline strings are allowed, including right\n# after the opening '''/\"\"\" and before the closing '''/\"\"\"\n\nlit_one = ''''one quote''''\nlit_two = '''''two quotes'''''\nlit_one_space = ''' 'one quote' '''\nlit_two_space = ''' ''two quotes'' '''\n\none = \"\"\"\"one quote\"\"\"\"\ntwo = \"\"\"\"\"two quotes\"\"\"\"\"\none_space = \"\"\" \"one quote\" \"\"\"\ntwo_space = \"\"\" \"\"two quotes\"\" \"\"\"\n\nmismatch1 = \"\"\"aaa'''bbb\"\"\"\nmismatch2 = '''aaa\"\"\"bbb'''\n\n# Three opening \"\"\", then one escaped \", then two \"\" (allowed), and then three\n# closing \"\"\"\nescaped = \"\"\"lol\\\"\"\"\"\"\"\n\nfive-quotes = \"\"\"\nClosing with five quotes\n\"\"\"\"\"\nfour-quotes = \"\"\"\nClosing with four quotes\n\"\"\"\"\n"
	jsonRef := "{\n  \"escaped\": {\n    \"type\": \"string\",\n    \"value\": \"lol\\\"\\\"\\\"\"\n  },\n  \"five-quotes\": {\n    \"type\": \"string\",\n    \"value\": \"Closing with five quotes\\n\\\"\\\"\"\n  },\n  \"four-quotes\": {\n    \"type\": \"string\",\n    \"value\": \"Closing with four quotes\\n\\\"\"\n  },\n  \"lit_one\": {\n    \"type\": \"string\",\n    \"value\": \"'one quote'\"\n  },\n  \"lit_one_space\": {\n    \"type\": \"string\",\n    \"value\": \" 'one quote' \"\n  },\n  \"lit_two\": {\n    \"type\": \"string\",\n    \"value\": \"''two quotes''\"\n  },\n  \"lit_two_space\": {\n    \"type\": \"string\",\n    \"value\": \" ''two quotes'' \"\n  },\n  \"mismatch1\": {\n    \"type\": \"string\",\n    \"value\": \"aaa'''bbb\"\n  },\n  \"mismatch2\": {\n    \"type\": \"string\",\n    \"value\": \"aaa\\\"\\\"\\\"bbb\"\n  },\n  \"one\": {\n    \"type\": \"string\",\n    \"value\": \"\\\"one quote\\\"\"\n  },\n  \"one_space\": {\n    \"type\": \"string\",\n    \"value\": \" \\\"one quote\\\" \"\n  },\n  \"two\": {\n    \"type\": \"string\",\n    \"value\": \"\\\"\\\"two quotes\\\"\\\"\"\n  },\n  \"two_space\": {\n    \"type\": \"string\",\n    \"value\": \" \\\"\\\"two quotes\\\"\\\" \"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_String_Multiline(t *testing.T) {
	input := "# NOTE: this file includes some literal tab characters.\n\nmultiline_empty_one = \"\"\"\"\"\"\n\n# A newline immediately following the opening delimiter will be trimmed.\nmultiline_empty_two = \"\"\"\n\"\"\"\n\n# \\ at the end of line trims newlines as well; note that last \\ is followed by\n# two spaces, which are ignored.\nmultiline_empty_three = \"\"\"\\\n    \"\"\"\nmultiline_empty_four = \"\"\"\\\n   \\\n   \\  \n   \"\"\"\n\nequivalent_one = \"The quick brown fox jumps over the lazy dog.\"\nequivalent_two = \"\"\"\nThe quick brown \\\n\n\n  fox jumps over \\\n    the lazy dog.\"\"\"\n\nequivalent_three = \"\"\"\\\n       The quick brown \\\n       fox jumps over \\\n       the lazy dog.\\\n       \"\"\"\n\nwhitespace-after-bs = \"\"\"\\\n       The quick brown \\\n       fox jumps over \\   \n       the lazy dog.\\\t\n       \"\"\"\n\nno-space = \"\"\"a\\\n    b\"\"\"\n\n# Has tab character.\nkeep-ws-before = \"\"\"a   \t\\\n   b\"\"\"\n\nescape-bs-1 = \"\"\"a \\\\\nb\"\"\"\n\nescape-bs-2 = \"\"\"a \\\\\\\nb\"\"\"\n\nescape-bs-3 = \"\"\"a \\\\\\\\\n  b\"\"\"\n"
	jsonRef := "{\n  \"equivalent_one\": {\n    \"type\": \"string\",\n    \"value\": \"The quick brown fox jumps over the lazy dog.\"\n  },\n  \"equivalent_three\": {\n    \"type\": \"string\",\n    \"value\": \"The quick brown fox jumps over the lazy dog.\"\n  },\n  \"equivalent_two\": {\n    \"type\": \"string\",\n    \"value\": \"The quick brown fox jumps over the lazy dog.\"\n  },\n  \"escape-bs-1\": {\n    \"type\": \"string\",\n    \"value\": \"a \\\\\\nb\"\n  },\n  \"escape-bs-2\": {\n    \"type\": \"string\",\n    \"value\": \"a \\\\b\"\n  },\n  \"escape-bs-3\": {\n    \"type\": \"string\",\n    \"value\": \"a \\\\\\\\\\n  b\"\n  },\n  \"keep-ws-before\": {\n    \"type\": \"string\",\n    \"value\": \"a   \\tb\"\n  },\n  \"multiline_empty_four\": {\n    \"type\": \"string\",\n    \"value\": \"\"\n  },\n  \"multiline_empty_one\": {\n    \"type\": \"string\",\n    \"value\": \"\"\n  },\n  \"multiline_empty_three\": {\n    \"type\": \"string\",\n    \"value\": \"\"\n  },\n  \"multiline_empty_two\": {\n    \"type\": \"string\",\n    \"value\": \"\"\n  },\n  \"no-space\": {\n    \"type\": \"string\",\n    \"value\": \"ab\"\n  },\n  \"whitespace-after-bs\": {\n    \"type\": \"string\",\n    \"value\": \"The quick brown fox jumps over the lazy dog.\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_String_Nl(t *testing.T) {
	input := "nl_mid = \"val\\nue\"\nnl_end = \"\"\"value\\n\"\"\"\n\nlit_nl_end = '''value\\n'''\nlit_nl_mid = 'val\\nue'\nlit_nl_uni = 'val\\ue'\n"
	jsonRef := "{\n  \"lit_nl_end\": {\n    \"type\": \"string\",\n    \"value\": \"value\\\\n\"\n  },\n  \"lit_nl_mid\": {\n    \"type\": \"string\",\n    \"value\": \"val\\\\nue\"\n  },\n  \"lit_nl_uni\": {\n    \"type\": \"string\",\n    \"value\": \"val\\\\ue\"\n  },\n  \"nl_end\": {\n    \"type\": \"string\",\n    \"value\": \"value\\n\"\n  },\n  \"nl_mid\": {\n    \"type\": \"string\",\n    \"value\": \"val\\nue\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_String_QuotedUnicode(t *testing.T) {
	input := "\nescaped_string = \"\\u0000 \\u0008 \\u000c \\U00000041 \\u007f \\u0080 \\u00ff \\ud7ff \\ue000 \\uffff \\U00010000 \\U0010ffff\"\nnot_escaped_string = '\\u0000 \\u0008 \\u000c \\U00000041 \\u007f \\u0080 \\u00ff \\ud7ff \\ue000 \\uffff \\U00010000 \\U0010ffff'\n\nbasic_string = \"~ \u0080 ÿ \ud7ff \ue000 \uffff 𐀀 \U0010ffff\"\nliteral_string = '~ \u0080 ÿ \ud7ff \ue000 \uffff 𐀀 \U0010ffff'\n"
	jsonRef := "{\n  \"basic_string\": {\n    \"type\": \"string\",\n    \"value\": \"~ \u0080 ÿ \ud7ff \ue000 \uffff 𐀀 \U0010ffff\"\n  },\n  \"escaped_string\": {\n    \"type\": \"string\",\n    \"value\": \"\\u0000 \\u0008 \\u000c A \x7f \u0080 ÿ \ud7ff \ue000 \uffff 𐀀 \U0010ffff\"\n  },\n  \"literal_string\": {\n    \"type\": \"string\",\n    \"value\": \"~ \u0080 ÿ \ud7ff \ue000 \uffff 𐀀 \U0010ffff\"\n  },\n  \"not_escaped_string\": {\n    \"type\": \"string\",\n    \"value\": \"\\\\u0000 \\\\u0008 \\\\u000c \\\\U00000041 \\\\u007f \\\\u0080 \\\\u00ff \\\\ud7ff \\\\ue000 \\\\uffff \\\\U00010000 \\\\U0010ffff\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_String_RawMultiline(t *testing.T) {
	input := "# Single ' should be allowed.\noneline = '''This string has a ' quote character.'''\n\n# A newline immediately following the opening delimiter will be trimmed.\nfirstnl = '''\nThis string has a ' quote character.'''\n\n# All other whitespace and newline characters remain intact.\nmultiline = '''\nThis string\nhas ' a quote character\nand more than\none newline\nin it.'''\n\n# Tab character in literal string does not need to be escaped\nmultiline_with_tab = '''First line\n\t Followed by a tab'''\n\nthis-str-has-apostrophes='''' there's one already\n'' two more\n'''''\n"
	jsonRef := "{\n  \"firstnl\": {\n    \"type\": \"string\",\n    \"value\": \"This string has a ' quote character.\"\n  },\n  \"multiline\": {\n    \"type\": \"string\",\n    \"value\": \"This string\\nhas ' a quote character\\nand more than\\none newline\\nin it.\"\n  },\n  \"multiline_with_tab\": {\n    \"type\": \"string\",\n    \"value\": \"First line\\n\\t Followed by a tab\"\n  },\n  \"oneline\": {\n    \"type\": \"string\",\n    \"value\": \"This string has a ' quote character.\"\n  },\n  \"this-str-has-apostrophes\": {\n    \"type\": \"string\",\n    \"value\": \"' there's one already\\n'' two more\\n''\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_String_Raw(t *testing.T) {
	input := "backspace = 'This string has a \\b backspace character.'\ntab = 'This string has a \\t tab character.'\nunescaped_tab = 'This string has an \t unescaped tab character.'\nnewline = 'This string has a \\n new line character.'\nformfeed = 'This string has a \\f form feed character.'\ncarriage = 'This string has a \\r carriage return character.'\nslash = 'This string has a \\/ slash character.'\nbackslash = 'This string has a \\\\ backslash character.'\n"
	jsonRef := "{\n  \"backslash\": {\n    \"type\": \"string\",\n    \"value\": \"This string has a \\\\\\\\ backslash character.\"\n  },\n  \"backspace\": {\n    \"type\": \"string\",\n    \"value\": \"This string has a \\\\b backspace character.\"\n  },\n  \"carriage\": {\n    \"type\": \"string\",\n    \"value\": \"This string has a \\\\r carriage return character.\"\n  },\n  \"formfeed\": {\n    \"type\": \"string\",\n    \"value\": \"This string has a \\\\f form feed character.\"\n  },\n  \"newline\": {\n    \"type\": \"string\",\n    \"value\": \"This string has a \\\\n new line character.\"\n  },\n  \"slash\": {\n    \"type\": \"string\",\n    \"value\": \"This string has a \\\\/ slash character.\"\n  },\n  \"tab\": {\n    \"type\": \"string\",\n    \"value\": \"This string has a \\\\t tab character.\"\n  },\n  \"unescaped_tab\": {\n    \"type\": \"string\",\n    \"value\": \"This string has an \\t unescaped tab character.\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_String_Simple(t *testing.T) {
	input := "answer = \"You are not drinking enough whisky.\"\n"
	jsonRef := "{\n  \"answer\": {\n    \"type\": \"string\",\n    \"value\": \"You are not drinking enough whisky.\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_String_UnicodeEscape(t *testing.T) {
	input := "delta-1 = \"\\u03B4\"\ndelta-2 = \"\\U000003B4\"\na       = \"\\u0061\"\nb       = \"\\u0062\"\nc       = \"\\U00000063\"\nnull-1  = \"\\u0000\"\nnull-2  = \"\\U00000000\"\n\nml-delta-1 = \"\"\"\\u03B4\"\"\"\nml-delta-2 = \"\"\"\\U000003B4\"\"\"\nml-a       = \"\"\"\\u0061\"\"\"\nml-b       = \"\"\"\\u0062\"\"\"\nml-c       = \"\"\"\\U00000063\"\"\"\nml-null-1  = \"\"\"\\u0000\"\"\"\nml-null-2  = \"\"\"\\U00000000\"\"\"\n"
	jsonRef := "{\n  \"a\": {\n    \"type\": \"string\",\n    \"value\": \"a\"\n  },\n  \"b\": {\n    \"type\": \"string\",\n    \"value\": \"b\"\n  },\n  \"c\": {\n    \"type\": \"string\",\n    \"value\": \"c\"\n  },\n  \"delta-1\": {\n    \"type\": \"string\",\n    \"value\": \"δ\"\n  },\n  \"delta-2\": {\n    \"type\": \"string\",\n    \"value\": \"δ\"\n  },\n  \"ml-a\": {\n    \"type\": \"string\",\n    \"value\": \"a\"\n  },\n  \"ml-b\": {\n    \"type\": \"string\",\n    \"value\": \"b\"\n  },\n  \"ml-c\": {\n    \"type\": \"string\",\n    \"value\": \"c\"\n  },\n  \"ml-delta-1\": {\n    \"type\": \"string\",\n    \"value\": \"δ\"\n  },\n  \"ml-delta-2\": {\n    \"type\": \"string\",\n    \"value\": \"δ\"\n  },\n  \"ml-null-1\": {\n    \"type\": \"string\",\n    \"value\": \"\\u0000\"\n  },\n  \"ml-null-2\": {\n    \"type\": \"string\",\n    \"value\": \"\\u0000\"\n  },\n  \"null-1\": {\n    \"type\": \"string\",\n    \"value\": \"\\u0000\"\n  },\n  \"null-2\": {\n    \"type\": \"string\",\n    \"value\": \"\\u0000\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_String_UnicodeLiteral(t *testing.T) {
	input := "answer = \"δ\"\n"
	jsonRef := "{\n  \"answer\": {\n    \"type\": \"string\",\n    \"value\": \"δ\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_String_WithPound(t *testing.T) {
	input := "pound = \"We see no # comments here.\"\npoundcomment = \"But there are # some comments here.\" # Did I # mess you up?\n"
	jsonRef := "{\n  \"pound\": {\n    \"type\": \"string\",\n    \"value\": \"We see no # comments here.\"\n  },\n  \"poundcomment\": {\n    \"type\": \"string\",\n    \"value\": \"But there are # some comments here.\"\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Table_ArrayImplicitAndExplicitAfter(t *testing.T) {
	input := "[[a.b]]\nx = 1\n\n[a]\ny = 2\n"
	jsonRef := "{\n  \"a\": {\n    \"b\": [\n      {\n        \"x\": {\n          \"type\": \"integer\",\n          \"value\": \"1\"\n        }\n      }\n    ],\n    \"y\": {\n      \"type\": \"integer\",\n      \"value\": \"2\"\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Table_ArrayImplicit(t *testing.T) {
	input := "[[albums.songs]]\nname = \"Glory Days\"\n"
	jsonRef := "{\n  \"albums\": {\n    \"songs\": [\n      {\n        \"name\": {\n          \"type\": \"string\",\n          \"value\": \"Glory Days\"\n        }\n      }\n    ]\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Table_ArrayMany(t *testing.T) {
	input := "[[people]]\nfirst_name = \"Bruce\"\nlast_name = \"Springsteen\"\n\n[[people]]\nfirst_name = \"Eric\"\nlast_name = \"Clapton\"\n\n[[people]]\nfirst_name = \"Bob\"\nlast_name = \"Seger\"\n"
	jsonRef := "{\n  \"people\": [\n    {\n      \"first_name\": {\n        \"type\": \"string\",\n        \"value\": \"Bruce\"\n      },\n      \"last_name\": {\n        \"type\": \"string\",\n        \"value\": \"Springsteen\"\n      }\n    },\n    {\n      \"first_name\": {\n        \"type\": \"string\",\n        \"value\": \"Eric\"\n      },\n      \"last_name\": {\n        \"type\": \"string\",\n        \"value\": \"Clapton\"\n      }\n    },\n    {\n      \"first_name\": {\n        \"type\": \"string\",\n        \"value\": \"Bob\"\n      },\n      \"last_name\": {\n        \"type\": \"string\",\n        \"value\": \"Seger\"\n      }\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Table_ArrayNest(t *testing.T) {
	input := "[[albums]]\nname = \"Born to Run\"\n\n  [[albums.songs]]\n  name = \"Jungleland\"\n\n  [[albums.songs]]\n  name = \"Meeting Across the River\"\n\n[[albums]]\nname = \"Born in the USA\"\n  \n  [[albums.songs]]\n  name = \"Glory Days\"\n\n  [[albums.songs]]\n  name = \"Dancing in the Dark\"\n"
	jsonRef := "{\n  \"albums\": [\n    {\n      \"name\": {\n        \"type\": \"string\",\n        \"value\": \"Born to Run\"\n      },\n      \"songs\": [\n        {\n          \"name\": {\n            \"type\": \"string\",\n            \"value\": \"Jungleland\"\n          }\n        },\n        {\n          \"name\": {\n            \"type\": \"string\",\n            \"value\": \"Meeting Across the River\"\n          }\n        }\n      ]\n    },\n    {\n      \"name\": {\n        \"type\": \"string\",\n        \"value\": \"Born in the USA\"\n      },\n      \"songs\": [\n        {\n          \"name\": {\n            \"type\": \"string\",\n            \"value\": \"Glory Days\"\n          }\n        },\n        {\n          \"name\": {\n            \"type\": \"string\",\n            \"value\": \"Dancing in the Dark\"\n          }\n        }\n      ]\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Table_ArrayOne(t *testing.T) {
	input := "[[people]]\nfirst_name = \"Bruce\"\nlast_name = \"Springsteen\"\n"
	jsonRef := "{\n  \"people\": [\n    {\n      \"first_name\": {\n        \"type\": \"string\",\n        \"value\": \"Bruce\"\n      },\n      \"last_name\": {\n        \"type\": \"string\",\n        \"value\": \"Springsteen\"\n      }\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Table_ArrayTableArray(t *testing.T) {
	input := "[[a]]\n    [[a.b]]\n        [a.b.c]\n            d = \"val0\"\n    [[a.b]]\n        [a.b.c]\n            d = \"val1\"\n"
	jsonRef := "{\n  \"a\": [\n    {\n      \"b\": [\n        {\n          \"c\": {\n            \"d\": {\n              \"type\": \"string\",\n              \"value\": \"val0\"\n            }\n          }\n        },\n        {\n          \"c\": {\n            \"d\": {\n              \"type\": \"string\",\n              \"value\": \"val1\"\n            }\n          }\n        }\n      ]\n    }\n  ]\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Table_ArrayWithinDotted(t *testing.T) {
	input := "[fruit]\napple.color = \"red\"\n\n[[fruit.apple.seeds]]\nsize = 2\n"
	jsonRef := "{\n  \"fruit\": {\n    \"apple\": {\n      \"color\": {\n        \"type\": \"string\",\n        \"value\": \"red\"\n      },\n      \"seeds\": [\n        {\n          \"size\": {\n            \"type\": \"integer\",\n            \"value\": \"2\"\n          }\n        }\n      ]\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Table_EmptyName(t *testing.T) {
	input := "['']\nx = 1\n\n[\"\".a]\nx = 2\n\n[a.'']\nx = 3\n"
	jsonRef := "{\n  \"\": {\n    \"a\": {\n      \"x\": {\n        \"type\": \"integer\",\n        \"value\": \"2\"\n      }\n    },\n    \"x\": {\n      \"type\": \"integer\",\n      \"value\": \"1\"\n    }\n  },\n  \"a\": {\n    \"\": {\n      \"x\": {\n        \"type\": \"integer\",\n        \"value\": \"3\"\n      }\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Table_Empty(t *testing.T) {
	input := "[a]\n"
	jsonRef := "{\n  \"a\": {}\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Table_Keyword(t *testing.T) {
	input := "[true]\n\n[false]\n\n[inf]\n\n[nan]\n\n\n"
	jsonRef := "{\n  \"false\": {},\n  \"inf\": {},\n  \"nan\": {},\n  \"true\": {}\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Table_Names(t *testing.T) {
	input := "[a.b.c]\n[a.\"b.c\"]\n[a.'d.e']\n[a.' x ']\n[ d.e.f ]\n[ g . h . i ]\n[ j . \"ʞ\" . 'l' ]\n\n[x.1.2]\n"
	jsonRef := "{\n  \"a\": {\n    \" x \": {},\n    \"b\": {\n      \"c\": {}\n    },\n    \"b.c\": {},\n    \"d.e\": {}\n  },\n  \"d\": {\n    \"e\": {\n      \"f\": {}\n    }\n  },\n  \"g\": {\n    \"h\": {\n      \"i\": {}\n    }\n  },\n  \"j\": {\n    \"ʞ\": {\n      \"l\": {}\n    }\n  },\n  \"x\": {\n    \"1\": {\n      \"2\": {}\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Table_NoEol(t *testing.T) {
	input := "[table]\n"
	jsonRef := "{\n  \"table\": {}\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Table_SubEmpty(t *testing.T) {
	input := "[a]\n[a.b]\n"
	jsonRef := "{\n  \"a\": {\n    \"b\": {}\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Table_Sub(t *testing.T) {
	input := "[a]\nkey = 1\n\n# a.extend is a key inside the \"a\" table.\n[a.extend]\nkey = 2\n\n[a.extend.more]\nkey = 3\n"
	jsonRef := "{\n  \"a\": {\n    \"extend\": {\n      \"key\": {\n        \"type\": \"integer\",\n        \"value\": \"2\"\n      },\n      \"more\": {\n        \"key\": {\n          \"type\": \"integer\",\n          \"value\": \"3\"\n        }\n      }\n    },\n    \"key\": {\n      \"type\": \"integer\",\n      \"value\": \"1\"\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Table_Whitespace(t *testing.T) {
	input := "[\"valid key\"]\n"
	jsonRef := "{\n  \"valid key\": {}\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Table_WithLiteralString(t *testing.T) {
	input := "['a']\n[a.'\"b\"']\n[a.'\"b\"'.c]\nanswer = 42 \n"
	jsonRef := "{\n  \"a\": {\n    \"\\\"b\\\"\": {\n      \"c\": {\n        \"answer\": {\n          \"type\": \"integer\",\n          \"value\": \"42\"\n        }\n      }\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Table_WithPound(t *testing.T) {
	input := "[\"key#group\"]\nanswer = 42\n"
	jsonRef := "{\n  \"key#group\": {\n    \"answer\": {\n      \"type\": \"integer\",\n      \"value\": \"42\"\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Table_WithSingleQuotes(t *testing.T) {
	input := "['a']\n[a.'b']\n[a.'b'.c]\nanswer = 42 \n"
	jsonRef := "{\n  \"a\": {\n    \"b\": {\n      \"c\": {\n        \"answer\": {\n          \"type\": \"integer\",\n          \"value\": \"42\"\n        }\n      }\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}

func TestTOMLTest_Valid_Table_WithoutSuper(t *testing.T) {
	input := "# [x] you\n# [x.y] don't\n# [x.y.z] need these\n[x.y.z.w] # for this to work\n[x] # defining a super-table afterwards is ok\n"
	jsonRef := "{\n  \"x\": {\n    \"y\": {\n      \"z\": {\n        \"w\": {}\n      }\n    }\n  }\n}\n"
	testgenValid(t, input, jsonRef)
}
