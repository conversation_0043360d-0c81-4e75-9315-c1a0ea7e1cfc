# GoStreaming

GoStreaming is a Go package that provides a powerful and efficient way to process streaming data with concurrent workers. It's designed to handle high-throughput data processing with built-in performance monitoring and error handling.

## Features

- Concurrent stream processing with configurable worker count
- Built-in performance monitoring and speed calculation
- Configurable error handling and callbacks
- Thread-safe operations
- Support for custom stream implementations
- Detailed logging with configurable verbosity
- Context-based cancellation support

## Installation

```bash
go get github.com/real-rm/gostreaming
```

## Usage

### Basic Usage

```go
package main

import (
    "context"
    "log"

    "github.com/real-rm/gostreaming"
)

func main() {
    // Create streaming options
    opts := &gostreaming.StreamingOptions{
        Stream: yourStream, // Your stream implementation
        Process: func(item interface{}) error {
            // Process each item
            log.Printf("Processing item: %v", item)
            return nil
        },
        High: 100, // Maximum concurrent workers
    }

    // Start streaming
    err := gostreaming.Streaming(context.Background(), opts)
    if err != nil {
        log.Printf("Streaming error: %v", err)
    }
}
```

### Advanced Usage with Custom Options

```go
opts := &gostreaming.StreamingOptions{
    Stream: yourStream,
    Process: func(item interface{}) error {
        // Process each item
        return processItem(item)
    },
    End: func(err error) {
        // Called when streaming ends
        if err != nil {
            log.Printf("Stream ended with error: %v", err)
        } else {
            log.Println("Stream completed successfully")
        }
    },
    Error: func(err error) {
        // Custom error handling
        log.Printf("Error processing item: %v", err)
    },
    High: 200,                    // Maximum concurrent workers
    SpeedInterval: 5000,          // Speed logging interval (ms)
    Verbose: 2,                   // Verbosity level
    Event: "custom_event_name",   // Custom event name
}

err := gostreaming.Streaming(context.Background(), opts)
```

### Stream Interface

Your stream implementation must satisfy the following interface:

```go
type Stream interface {
    Next(context.Context) bool    // Move to next item
    Decode(interface{}) error     // Decode current item
    Err() error                   // Get stream error
}
```

## Configuration Options

### StreamingOptions

```go
type StreamingOptions struct {
    Stream        interface{}             // Stream object with Next(), Decode() and Err() methods
    Process       func(interface{}) error // Process function for each item
    End           func(error)             // Callback when streaming ends
    Error         func(error)             // Error handler
    High          int                     // Max number of concurrent processing goroutines
    SpeedInterval int64                   // Interval for speed logging (in milliseconds)
    Verbose       int                     // Verbosity level
    Event         string                  // Event name (default: "data")
}
```

### Default Values

- `High`: 100 (maximum concurrent workers)
- `SpeedInterval`: 1000ms (speed logging interval)
- `Event`: "data" (default event name)
- `Verbose`: 0 (no verbose logging)

## Performance Monitoring

The package automatically calculates and logs:
- Current processing speed (items/second)
- Average processing speed
- Total items processed
- Success/failure counts

Example log output:
```
Speed: 150.25/s (current), 145.80/s (average)
Stream done. Processed 1000/1000 items
```

## Error Handling

The package provides comprehensive error handling:
- Stream errors (via `Err()` method)
- Processing errors (via `Process` function)
- Custom error handling (via `Error` callback)
- Context cancellation

## Thread Safety

- All operations are thread-safe
- Uses mutex locks for shared resources
- Atomic operations for counters
- Safe logging with buffered channel

## Best Practices

1. Always provide a context for cancellation support
2. Implement proper error handling in your Process function
3. Use appropriate High value based on your system resources
4. Monitor memory usage with large concurrent worker counts
5. Implement proper cleanup in the End callback

## License

MIT License