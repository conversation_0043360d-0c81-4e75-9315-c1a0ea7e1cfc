package gostreaming

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	golog "github.com/real-rm/golog"
)

// StreamingOptions defines the configuration for stream processing
type StreamingOptions struct {
	Stream        interface{}             // Stream object with Next(), Decode() and Err() methods
	Process       func(interface{}) error // Process function for each item
	End           func(error)             // Callback when streaming ends
	Error         func(error)             // Error handler
	High          int                     // Max number of concurrent processing goroutines
	SpeedInterval int64                   // Interval for speed logging (in milliseconds)
	Verbose       int                     // Verbosity level
	Event         string                  // Event name (default: "data")
}

// calculateSpeed returns current and accumulated speed metrics
func calculateSpeed(startTime, speedTime time.Time, speedCount, totalCount int) (float64, float64) {
	elapsed := time.Since(speedTime).Milliseconds()
	if elapsed == 0 {
		elapsed = 1
	}
	currentSpeed := float64(speedCount) * 1000.0 / float64(elapsed)

	totalElapsed := time.Since(startTime).Milliseconds()
	if totalElapsed == 0 {
		totalElapsed = 1
	}
	accuSpeed := float64(totalCount) * 1000.0 / float64(totalElapsed)

	return currentSpeed, accuSpeed
}

// Streaming processes items from a stream using concurrent workers, up to High
func Streaming(ctx context.Context, opts *StreamingOptions) error {
	if opts.Stream == nil {
		return fmt.Errorf("stream not provided")
	}

	if opts.High <= 0 {
		opts.High = 100
	}

	if opts.SpeedInterval <= 0 {
		opts.SpeedInterval = 1000
	}

	if opts.Process == nil {
		opts.Process = func(item interface{}) error {
			golog.Infof("%v", item)
			return nil
		}
	}

	if opts.End == nil {
		opts.End = func(err error) {}
	}

	if opts.Event == "" {
		opts.Event = "data"
	}

	stream, ok := opts.Stream.(interface {
		Next(context.Context) bool
		Decode(interface{}) error
		Err() error
	})
	if !ok {
		return fmt.Errorf("stream does not implement required methods")
	}

	// Create a cancellable context
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	// Create a logging channel and start a dedicated logger goroutine
	logChan := make(chan string, 1000) // Buffer size of 1000
	loggerDone := make(chan struct{})
	go func() {
		defer close(loggerDone)
		for msg := range logChan {
			golog.Info(msg)
		}
	}()

	var (
		startTime     = time.Now()
		speedTimeNano = time.Now().UnixNano() // Use atomic int64 for time tracking
		speedCounter  int64                   // Use int64 for atomic operations
		total         int64                   // Use int64 for atomic operations
		done          int64                   // Use int64 for atomic operations
		wg            sync.WaitGroup
		semaphore     = make(chan struct{}, opts.High) // Controls max concurrent goroutines
		endOnce       sync.Once
		curErr        error // Store the first error encountered
		errMu         sync.Mutex
		logMu         sync.Mutex
		logClosed     bool
	)

	// Safe logging function
	log := func(msg string) {
		logMu.Lock()
		closed := logClosed
		logMu.Unlock()
		if !closed {
			logChan <- msg // Block until message can be sent
		}
	}

	end := func(err error) {
		endOnce.Do(func() {
			if opts.Verbose > 0 {
				log(fmt.Sprintf("Stream done. Processed %d/%d items", atomic.LoadInt64(&done), atomic.LoadInt64(&total)))
			}
			opts.End(err)

			// Close logging channel
			logMu.Lock()
			if !logClosed {
				logClosed = true
				close(logChan)
			}
			logMu.Unlock()
		})
	}

	for {
		if ctx.Err() != nil {
			end(ctx.Err())
			break
		}

		if !stream.Next(ctx) {
			if err := stream.Err(); err != nil {
				errMu.Lock()
				curErr = err
				errMu.Unlock()
				log(fmt.Sprintf("Cursor error occurred: %v", err))
				cancel() // Cancel context to stop processing
				break
			}
			log("No more documents")
			break
		}

		var doc interface{}
		if err := stream.Decode(&doc); err != nil {
			errMu.Lock()
			curErr = err
			errMu.Unlock()
			cancel() // Cancel context to stop processing
			break
		}

		atomic.AddInt64(&total, 1)
		semaphore <- struct{}{} // Acquire slot (blocks if full)
		wg.Add(1)

		go func(d interface{}) {
			defer func() {
				if r := recover(); r != nil {
					errMu.Lock()
					if curErr == nil {
						curErr = fmt.Errorf("panic: %v", r)
						cancel()
					}
					errMu.Unlock()
					if opts.Error != nil {
						opts.Error(fmt.Errorf("panic: %v", r))
					} else {
						log(fmt.Sprintf("panic recovered: %v", r))
					}
				}
				<-semaphore
				wg.Done()
			}()
			if err := opts.Process(d); err != nil {
				errMu.Lock()
				if curErr == nil {
					curErr = err
					cancel() // Cancel context to stop processing
				}
				errMu.Unlock()

				if opts.Error != nil {
					opts.Error(err)
				} else {
					log(fmt.Sprintf("Process error: %v", err))
				}
				return
			}

			atomic.AddInt64(&done, 1)
			atomic.AddInt64(&speedCounter, 1)

			if opts.Verbose > 1 {
				last := time.Unix(0, atomic.LoadInt64(&speedTimeNano))
				if time.Since(last).Milliseconds() >= opts.SpeedInterval {
					curSpeed, accSpeed := calculateSpeed(startTime, last,
						int(atomic.LoadInt64(&speedCounter)), int(atomic.LoadInt64(&done)))
					log(fmt.Sprintf("Speed: %.2f/s (current), %.2f/s (average)", curSpeed, accSpeed))
					atomic.StoreInt64(&speedTimeNano, time.Now().UnixNano())
					atomic.StoreInt64(&speedCounter, 0)
				}
			}
		}(doc)
	}

	wg.Wait()
	end(curErr)
	<-loggerDone // Wait for logger to finish
	return curErr
}
