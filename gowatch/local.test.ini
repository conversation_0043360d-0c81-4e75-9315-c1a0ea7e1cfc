[dbs]
verbose = 3

[dbs.tmp]
uri = "***************************************************************"

[golog]
dir = "/tmp/test_logs"
level = "info"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "text"

[preDefColls.emailWhitelist]
collName = "email_whitelist"
dbName = "tmp"

[preDefColls.mailLog]
collName = "mail_log"
dbName = "tmp"

  [preDefColls.mailLog.options]
  expireAfterSeconds = 2_592_000

    [preDefColls.mailLog.options.timeseries]
    metaField = "metadata"
    timeField = "timestamp"