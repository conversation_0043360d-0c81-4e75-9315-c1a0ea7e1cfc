package gowatch

import (
	"context"
	"fmt"
	"sync"
	"time"

	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

// ProcessChangedObjectOptions contains options for processing changed objects
type ProcessChangedObjectOptions struct {
	ChangedObj            bson.M
	WatchedColl           *gomongo.MongoCollection
	DeleteOneFn           func(interface{}) error
	IgnoreDelete          bool
	UpdateOneFn           func(bson.M) error
	UpdateOneFnWithFields func(bson.M, bson.M) error
	ReplaceOneFn          func(bson.M) error
	InsertOneFn           func(bson.M) error
	ChangeStatusOnlyFn    func(bson.M) error
	SkipChangeMtOnly      bool
	WatchedStream         *WatchObject
}

// ProcessChangedObject processes a changed object from the change stream
func ProcessChangedObject(opt ProcessChangedObjectOptions) error {
	golog.Debug("processChangedObject", opt.ChangedObj["operationType"], opt.ChangedObj)

	// Handle invalidate event
	if opt.ChangedObj["operationType"] == "invalidate" {
		golog.Error("invalid event")
		return fmt.Errorf("invalid event, please do init again")
	}

	// Check if operation type is valid
	validTypes := map[string]bool{
		"insert":  true,
		"update":  true,
		"delete":  true,
		"replace": true,
	}

	opRaw, ok := opt.ChangedObj["operationType"]
	if !ok {
		golog.Warn("operationType missing", "operationType", opt.ChangedObj)
		return nil
	}
	opType, ok := opRaw.(string)
	if !ok {
		golog.Error("operationType is not string", opRaw)
		return nil
	}

	if !validTypes[opType] {
		golog.Verbose("ignore:", "operationType", opt.ChangedObj["operationType"], "opt", opt.ChangedObj)
		return nil
	}

	// Get document ID
	docKey, ok := opt.ChangedObj["documentKey"].(bson.M)
	if !ok {
		golog.Error("no documentKey", opt.ChangedObj)
		return nil
	}

	id, ok := docKey["_id"]
	if !ok {
		golog.Error("no id", opt.ChangedObj)
		return nil
	}

	var err error
	// Delete
	doDelete := shouldDelete(opt)
	if doDelete {
		if opt.DeleteOneFn == nil {
			return fmt.Errorf("need deleteOne function")
		}
		golog.Info("delete", "id", id, "in target, no update")
		if err = opt.DeleteOneFn(id); err != nil {
			golog.Error("delete error:", err)
			return err
		}
		golog.Verbose("delete success:", "documentKey", opt.ChangedObj["documentKey"])
		opt.WatchedStream.updateTokenFromChangeEvent(opt.ChangedObj)
		return nil
	}
	if opt.ChangedObj["operationType"] == "delete" {
		golog.Info("delete", "id", id, "in target, no update")
		opt.WatchedStream.updateTokenFromChangeEvent(opt.ChangedObj)
		return nil
	}

	// Check for _mt only updates
	golog.Debug("updateDesc", "changobj", opt.ChangedObj)
	var updateDesc bson.M
	if updateDescRaw, ok := opt.ChangedObj["updateDescription"]; ok {
		updateDesc, ok = updateDescRaw.(bson.M)
		if !ok || updateDesc == nil {
			golog.Error("invalid updateDescription format in change stream")
			return nil
		}
		if isMtOnlyUpdate(updateDesc, opt.SkipChangeMtOnly) {
			opt.WatchedStream.updateTokenFromChangeEvent(opt.ChangedObj)
			return nil
		}

		// Status only update
		if isStatusOnlyUpdate(updateDesc) && opt.ChangeStatusOnlyFn != nil {
			err = opt.ChangeStatusOnlyFn(bson.M{
				"id":              id,
				"prop":            updateDesc["updatedFields"],
				"watchedCollName": opt.WatchedColl.Name(),
			})
			opt.WatchedStream.updateTokenFromChangeEvent(opt.ChangedObj)
			return err
		}
	}

	// Full document update
	fullDoc, ok := opt.ChangedObj["fullDocument"].(bson.M)
	if !ok {
		golog.Error("invalid fullDocument format in change stream")
		return nil
	}
	if fullDoc != nil {
		golog.Debug("change fullDocument", fullDoc)
		var updatedFields bson.M
		if updateDesc != nil {
			updatedFields = getUpdatedFields(updateDesc)
		}
		if updatedFields == nil {
			updatedFields = bson.M{}
		}

		err = processUpdate(ProcessUpdateOptions{
			ReplaceOneFn:          opt.ReplaceOneFn,
			UpdateOneFn:           opt.UpdateOneFn,
			UpdateOneFnWithFields: opt.UpdateOneFnWithFields,
			InsertOneFn:           opt.InsertOneFn,
			OperationType:         opt.ChangedObj["operationType"].(string),
			FullDocument:          fullDoc,
			UpdatedFields:         updatedFields,
		})
	} else {
		// Fallback: query the document manually
		var prop bson.M
		err = opt.WatchedColl.FindOne(context.Background(), bson.M{"_id": id}).Decode(&prop)
		if err != nil {
			if err == mongo.ErrNoDocuments {
				golog.Error("no fullDocument, cannot find", id, "in watchedColl")
				return nil
			}
			return err
		}

		golog.Info("no fullDocument, find", id, "in watchedColl")
		var updateFields bson.M
		if updateDesc != nil {
			updateFields, _ = updateDesc["updatedFields"].(bson.M)
		}
		if updateFields == nil {
			updateFields = bson.M{}
		}

		err = processUpdate(ProcessUpdateOptions{
			ReplaceOneFn:          opt.ReplaceOneFn,
			UpdateOneFn:           opt.UpdateOneFn,
			UpdateOneFnWithFields: opt.UpdateOneFnWithFields,
			InsertOneFn:           opt.InsertOneFn,
			OperationType:         opt.ChangedObj["operationType"].(string),
			FullDocument:          prop,
			UpdatedFields:         updateFields,
		})
	}

	// Update token after processing if no error
	if err == nil && opt.WatchedStream != nil {
		opt.WatchedStream.updateTokenFromChangeEvent(opt.ChangedObj)
	}
	return err
}

// getUpdatedFields gets the updated fields from the update description
func getUpdatedFields(desc bson.M) bson.M {
	fields, _ := desc["updatedFields"].(bson.M)
	return fields
}

// shouldDelete checks if the object should be deleted
func shouldDelete(opt ProcessChangedObjectOptions) bool {
	if opt.IgnoreDelete {
		return false
	}
	if opt.ChangedObj["operationType"] == "delete" {
		return true
	}
	fullDoc, ok := opt.ChangedObj["fullDocument"].(bson.M)
	if !ok {
		return false
	}
	return fullDoc["del"] == 1
}

// isMtOnlyUpdate checks if the update is a mt only update
func isMtOnlyUpdate(desc bson.M, skipMt bool) bool {
	fields := getUpdatedFields(desc)
	removedFields, _ := desc["removedFields"].(bson.A)
	if len(fields) == 1 && len(removedFields) == 0 {
		if fields["_mt"] != nil {
			golog.Debug("change _mt only, no update")
			return true
		}
		if skipMt && fields["mt"] != nil {
			golog.Debug("change mt only, no update")
			return true
		}
	}
	return false
}

// isStatusOnlyUpdate checks if the update is a status only update
func isStatusOnlyUpdate(desc bson.M) bool {
	fields := getUpdatedFields(desc)
	if fields["status"] == nil {
		return false
	}
	if len(fields) == 1 && fields["status"] != nil {
		return true
	}
	if len(fields) == 2 && fields["status"] != nil && fields["mt"] != nil {
		return true
	}
	return false
}

// ProcessUpdateOptions contains options for processing updates
type ProcessUpdateOptions struct {
	UpdateOneFn           func(bson.M) error
	UpdateOneFnWithFields func(bson.M, bson.M) error
	InsertOneFn           func(bson.M) error
	ReplaceOneFn          func(bson.M) error
	OperationType         string
	FullDocument          bson.M
	UpdatedFields         bson.M
}

var (
	gImmediateUpdatedId = struct {
		ID   interface{}
		Type string
		TS   time.Time
	}{}
	gImmediateUpdatedIdMutex sync.Mutex
)

// processUpdate processes an update operation
func processUpdate(opt ProcessUpdateOptions) error {
	// Check for duplicate updates
	gImmediateUpdatedIdMutex.Lock()
	if gImmediateUpdatedId.ID == opt.FullDocument["_id"] &&
		time.Since(gImmediateUpdatedId.TS) < 2*time.Second {
		gImmediateUpdatedIdMutex.Unlock()
		golog.Error("Error: watch update with same record_id:", opt.FullDocument["_id"])
		return nil
	}

	// Update global tracking
	gImmediateUpdatedId = struct {
		ID   interface{}
		Type string
		TS   time.Time
	}{
		ID:   opt.FullDocument["_id"],
		Type: opt.OperationType,
		TS:   time.Now(),
	}
	gImmediateUpdatedIdMutex.Unlock()

	// Handle replace operation
	if opt.OperationType == "replace" {
		if opt.ReplaceOneFn == nil {
			return fmt.Errorf("need replaceOne function")
		}
		return opt.ReplaceOneFn(opt.FullDocument)
	}

	// Handle insert operation with insertOneFn
	if opt.OperationType == "insert" && opt.InsertOneFn != nil {
		return opt.InsertOneFn(opt.FullDocument)
	}

	// Handle insert or update operations
	if opt.OperationType == "insert" || opt.OperationType == "update" {
		if opt.UpdateOneFnWithFields != nil {
			if err := opt.UpdateOneFnWithFields(opt.FullDocument, opt.UpdatedFields); err != nil {
				golog.Error("update/insert error:", err)
				return err
			}
			golog.Debug("update/insert success:", opt.FullDocument, opt.UpdatedFields)
			return nil
		}
		if opt.UpdateOneFn != nil {
			if err := opt.UpdateOneFn(opt.FullDocument); err != nil {
				golog.Error("update error:", err)
				return err
			}
			golog.Debug("update success:", opt.FullDocument)
			return nil
		}
		return fmt.Errorf("need updateOne function")
	}

	golog.Error("Not supported operationType", opt.OperationType)
	return nil
}

// saveChangedObj saves the changed object to the import collection
func saveChangedObj(col *gomongo.MongoCollection, changedObj bson.M) error {
	if col == nil {
		return nil
	}
	insertObj := bson.M{
		"id":                changedObj["documentKey"],
		"operationType":     changedObj["operationType"],
		"ns":                changedObj["ns"],
		"updateDescription": changedObj["updateDescription"],
		"clusterTime":       changedObj["clusterTime"],
		"token":             changedObj["_id"],
	}
	_, err := col.InsertOne(context.Background(), insertObj)
	if err != nil {
		golog.Error("when insert into importWatchedRecordsCol, error:", err, "changedObj", changedObj)
	}
	return err
}
