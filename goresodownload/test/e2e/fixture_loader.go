package e2e

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

// FixtureConfig holds configuration for fixture loading
type FixtureConfig struct {
	Folder         string
	UseMongoImport bool
}

// FixtureData represents the structure of fixture JSON files
type FixtureData struct {
	Rows []bson.M `json:"rows"`
}

// LoadFixtures loads test fixtures into MongoDB
func LoadFixtures(cfg *FixtureConfig) error {
	if cfg == nil {
		cfg = &FixtureConfig{}
	}

	// Get fixtures directory
	fixturesDir := filepath.Join(cfg.Folder, "fixtures")
	if _, err := os.Stat(fixturesDir); os.IsNotExist(err) {
		return fmt.Errorf("fixtures directory not found: %s", fixturesDir)
	}

	// Read database directories
	dbs, err := os.ReadDir(fixturesDir)
	if err != nil {
		return fmt.Errorf("failed to read fixtures directory: %w", err)
	}

	// Process each database directory
	for _, db := range dbs {
		if strings.HasPrefix(db.Name(), ".") || strings.HasSuffix(db.Name(), ".md") {
			continue
		}

		dbPath := filepath.Join(fixturesDir, db.Name())
		colFiles, err := os.ReadDir(dbPath)
		if err != nil {
			return fmt.Errorf("failed to read database directory %s: %w", db.Name(), err)
		}

		// Process each collection file
		for _, colFile := range colFiles {
			if strings.HasPrefix(colFile.Name(), ".") {
				continue
			}

			colName := strings.TrimSuffix(colFile.Name(), filepath.Ext(colFile.Name()))
			filePath := filepath.Join(dbPath, colFile.Name())

			// Load fixture data
			if err := loadFixtureFile(db.Name(), colName, filePath); err != nil {
				return fmt.Errorf("failed to load fixture %s: %w", filePath, err)
			}
		}
	}

	return nil
}

// loadFixtureFile loads a single fixture file into MongoDB
func loadFixtureFile(dbName, colName, filePath string) error {
	golog.Info("Loading fixture", "db", dbName, "collection", colName, "file", filePath)

	// Read fixture file
	data, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read fixture file: %w", err)
	}

	// Parse fixture data
	var fixtureData FixtureData
	if err := json.Unmarshal(data, &fixtureData); err != nil {
		return fmt.Errorf("failed to parse fixture data: %w", err)
	}

	// Get collection
	collection := gomongo.Coll(dbName, colName)
	if collection == nil {
		return fmt.Errorf("failed to get collection %s.%s", dbName, colName)
	}

	// Clear existing data
	if _, err := collection.DeleteMany(context.Background(), bson.M{}); err != nil {
		return fmt.Errorf("failed to clear collection: %w", err)
	}

	// Insert fixture data
	if len(fixtureData.Rows) > 0 {
		// Convert []bson.M to []interface{}
		docs := make([]interface{}, len(fixtureData.Rows))
		for i, row := range fixtureData.Rows {
			// Convert _mt field to proper Date type if it exists
			if mt, ok := row["ts"].(map[string]interface{}); ok {
				if dateStr, ok := mt["$date"].(string); ok {
					// Parse the date string
					dateStr = dateStr[:len(dateStr)-2] + ":00"
					if t, err := time.Parse(time.RFC3339, dateStr); err == nil {
						row["ts"] = t
					}
				}
			}
			docs[i] = row
		}

		if _, err := collection.InsertMany(context.Background(), docs); err != nil {
			return fmt.Errorf("failed to insert fixture data: %w", err)
		}
	}

	golog.Info("Loaded fixture", "db", dbName, "collection", colName, "rows", len(fixtureData.Rows))
	return nil
}
