### 3. Downloader 下载器

负责下载媒体文件。

```go
interface Downloader {
    Download(task MediaTask) error
}

struct MediaTask {
    // TaskID    string
    Sid       string
    Order     int
    MediaKey  string
    URL       string
    Type      string // photo/pdf/zip/img
    DestPath  string
    PropTs    time.Time
}
```


media_diff_analyzer.go 中处理数据后生成结果：
AnalysisResult{
		DownloadTasks: downloadTasks,
		DeleteTasks:   deleteTasks,
		PhoLH:         phoLH,
		DocLH:         docLH,
	}
Downloader 需要把这个作为输入，进行数据的下载和删除，

1. 下载需要并行，并行数目用一个参数，可以设置
2. 删除也需要并行，和下载分开
3. 需要知道所有下载都成功，然后，在merged表中添加字段PhoLH和DocLH。
4. 下载还需要增加个输入，是一个list，里面存放的磁盘位置，每个磁盘位置 都需要连接MediaTask里面的DestPath，保存/删除
5. 下载使用 gofile/fetch_and_save.go 中function
6. 删除时候，文件可能不存在，只做记录，不error
7. 如果是磁盘满了，或者其他硬件导致的失败，直接报错退出
同时需要增加：
重试机制	每张失败的图片可尝试下载 2-3 次，仍失败再放弃
失败记录表	存入 DB：prop_id、image_url、err_msg、retry_count 等
告警通知	如果图片连续失败，可通知开发者/发出告警


数据库相关：
失败记录表的具体表结构是什么？帮我设计，记录prop_id、image_url、err_msg、retry_count
merged 表的具体结构是什么？{_id:'TRBX1234',media:[]}
使用什么数据库？ mongo

配置相关：
并行下载和删除的并发数具体是多少？ 默认10， 可以通过参数修改
重试次数是使用 DEFAULT_RETRIES(3次) 还是需要可配置 DEFAULT_RETRIES 3次

告警机制：
告警通知的具体实现方式是什么？(邮件、webhook等)  先留着TODO，后续补充gosms 和 gomail 的使用
连续失败的定义是什么？(比如连续失败多少次触发告警)连续3个下载的连续3次 次数可以先DEFAULT

其他：
MediaTask 中的 Type 字段是否会影响下载处理逻辑？ 不会，都用url
是否需要支持断点续传？不需要
是否需要支持下载进度监控？不需要，都是小图片