# 需求 [fix_graceful_exit]

## 反馈

1. 现在的graceful exit 会导致prop下载了部分图片

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-07-28

## 原因

1. 目前的graceful exit 没有做相应处理

## 解决办法

### 1. 基于生产数据的精确超时配置
- 基于实际测量数据（214ms下载+305ms编码=0.52秒/张）重新计算超时时间
- 每个prop内部5个并发下载：50张图片÷5并发=10批次×0.52秒=5.2秒/prop
- 70个prop考虑系统限制约4轮处理=21秒理论时间，设置15分钟安全缓冲
- SystemD超时设置为20分钟，确保不会过早终止

### 2. 确保所有图片下载完成的机制
- Worker收到shutdown信号时继续完成当前prop的所有图片下载
- 停止接收新任务，但等待正在处理的任务完全完成
- 下载过程不会被graceful shutdown中断，只依赖gofile内置超时机制


## 是否需要补充UT

1. 现有的graceful shutdown测试用例已覆盖主要功能
2. 下载超时机制的简化不影响现有测试覆盖率
3. 队列处理和worker机制通过现有的ProcessAnalysisResult测试覆盖

## 确认日期:    2025-07-28

## online-step
0. 更新rmconfig PR https://github.com/real-rm/rmconfig/pull/26

1. 重启goresodownload服务
  ```
  systemctl --user stop batch@goresodownloadTRB
  cd goresodownload
  make build
  cd rmconfig
  ./start.sh -t batch -n goresodownloadTRB -d "goresodownload" -cmd "bin/goresodownload.bin -board TRB -force"



