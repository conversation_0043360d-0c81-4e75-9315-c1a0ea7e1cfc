# 需求 [fix_queue]

## 反馈

1. reso_photo_download_queue一直有12个大于4000的没有被处理。

## 需求提出人:   Maggie

## 修改人：      Maggie

## 提出日期:     2025-07-18

## 原因
1. GetNextBatch 函数使用两步查询：先获取 priority >= 100 的项目，然后随机补充剩余项目
2. 没有真正按优先级排序，硬编码优先级阈值 100
3. 测试失败：请求 3 个项目但返回了 5 个项目
4. 随机采样可能选择低优先级项目而忽略中等优先级项目

## 解决办法
1. 移除复杂的两步查询逻辑，改为单一的优先级排序查询
2. 使用 MongoDB 原生排序 `SetSort(bson.M{"priority": -1})`
3. 确保 `SetLimit(int64(batchSize))` 严格限制返回数量
4. 移除硬编码的优先级阈值
5. 利用现有的复合索引提高查询性能
6. 添加专门的单元测试验证 bug 修复

## 是否需要补充UT

1. 已补充 `TestResourceDownloadQueue_ComprehensiveCoverage/GetNextBatch_BugFix_PriorityAndBatchSize` 测试

## 确认日期:    2025-07-18

## online-step

1. 重启相关服务（如果需要）
  ```
  systemctl --user stop batch@goresodownloadTRB
  cd goresodownload
  make build
  cd rmconfig
  ./start.sh -t batch -n goresodownloadTRB -d "goresodownload" -cmd "bin/goresodownload.bin -board TRB -force"
  ```

