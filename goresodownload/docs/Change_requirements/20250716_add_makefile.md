# 需求 [add_makefile]

## 反馈

1. 需要添加makefile，使用编译后.bin 运行
2. gofile 和 golevelstore拆分
3. disk_size改用可读文件大小
4. golevelstore添加GetUserUploadConfig
5. GC过于频繁，readBody添加重试
6. coffee 计算的l2和go的不一致

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-07-14

## 原因
1. 新的goresodownload systemdrun的时候 名称显示为component，不符合预期
2. golevelstore gofile需要拆分
3. 代码中使用了disk_size，但是这个值是bytes，需要改用可读文件大小
4. 视频，rr等需要获取用户上传配置，所以需要在golevelstore中添加 GetUserUploadConfig
5. 之前每个图片处理后就会GC，过于频繁

## 解决办法
1. 在goresodownload中添加makefile，使用编译后.bin 运行，并在rmconfig中添加对.bin文件的支持
2. 将gofile和golevelstore拆分
3. 将disk_size改用可读文件大小
4. 在golevelstore中添加GetUserUploadConfig
5. 将GC改为智能GC，每1000张图片GC一次，或者内存超过2GB且距离上次GC超过5分钟
6. 之前readBody没有添加重试，为readBody添加重试
7. 将路径写入merged表：phoP:"/L1/L2"
8. batchSize 默认修改为 3
9. 添加batch 给之前添加过phoLH的添加phoP字段


## 是否需要补充UT

1. 已补充

## 确认日期:    2025-07-16

## online-step

1. 重启goresodownload
  ```
  systemctl --user stop  batch@goresodownloadTRB
  cd goresodownload
  make build
  cd rmconfig
  ./start.sh -t batch -n goresodownloadTRB -d "goresodownload" -cmd "bin/goresodownload.bin -board TRB -force"
  ```
2. 运行batch
```
./start.sh  -n goAddPhoP -d "goresodownload" -cmd "cmd/batch/AddPhoP.go -dryrun"
```
dryrun:[processed(30196):19.55K/m dryrun(30196):19.55K/m]