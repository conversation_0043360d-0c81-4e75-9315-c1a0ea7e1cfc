# 需求 [handle_old_reso_data]

## 反馈

1. 目前goresodownload图片下载只处理了20250714开始的数据，需要处理旧的数据，先处理reso的

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-07-14

## 原因
1. 旧数据也需要按照现在目录结构存储

## 解决办法
1. 旧的reso图片，直接batch添加到现在的reso_photo_download_queue中。


## 是否需要补充UT

1. 不需要

## 确认日期:    2025-07-17

## online-step
1. 运行batch
```
./start.sh  -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -dryrun -board=TRB"
```
