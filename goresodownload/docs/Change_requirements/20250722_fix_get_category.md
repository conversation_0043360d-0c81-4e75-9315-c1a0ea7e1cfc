# 需求 [fix_get_category]

## 反馈

1. TRB error
2025-07-22T00:03:27.684-04:00 level=ERROR msg=Missing media category, sid="trreb/e9770483/audio", media={
  "hash": 838514494,
  "id": 0,
  "key": "trreb/e9770483/audio",
  "m": "Listing Audio",
  "perm": [
    "Public"
  ],
  "status": "Active",
  "tp": "audio/mpeg",
  "url": "https://trreb-image.ampre.ca/LuZGkJY5hku2rRw-IatFIscJ7U876NMXoQkp9uc2oH0/raw:1/bG9jYWw
6Ly8vdHJyZWIvbGlzdGluZ3MvMzMvMDYvOTYvMjgvYS9lblVQY3FMZUw0bjRERzJrd0ZwTWZRVG0ySVE2Q2JRX3VYejBUWWY
yMEljLm1wMw"
}, file="media_diff_analyzer.go:764"


## 需求提出人:   

## 修改人：      Maggie

## 提出日期:     2025-07-22

## 原因
1. TRB media中存在 `cat` 字段为空的情况，最早download没有添加
2. 需要通过 `tp` 字段来正确识别图片类型，确保图片能被正确分类和处理

## 解决办法
### 1. 创建专门的TRB处理函数
- 新增 `ensureCatFieldForTRB()` 函数，专门处理TRB board的"cat"字段设置
- 只在TRB board中应用此逻辑，遵循DRY原则

### 2. 统一媒体处理流程
- 新增 `processMediaList()` 函数，统一处理媒体列表
- 避免代码重复，在多个地方调用相同的逻辑

### 3. 条件判断逻辑
当满足以下条件时，自动设置 `"cat": "Photo"`：
- "cat"字段不存在或为空
- "tp"字段以"image/"开头，或
- "tp"字段包含"jpeg"（不区分大小写）

### 5. addToQueue 
支持输入-id=[TRBC12248877,TRBC12248878,TRBC12248879] 添加到queue（priority 50000）


## 是否需要补充UT

1. **需要** - 已添加完整的单元测试


## 确认日期:    2025-07-22


## online-step

1. 重启相关服务
  ```
  systemctl --user stop batch@goresodownloadTRB
  cd goresodownload
  make build
  cd rmconfig
  ./start.sh -t batch -n goresodownloadTRB -d "goresodownload" -cmd "bin/goresodownload.bin -board TRB -force"
  ```
2. 运行batch，将之前被忽略掉的，添加如queue
```
./start.sh  -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -dryrun -board=TRB"
```