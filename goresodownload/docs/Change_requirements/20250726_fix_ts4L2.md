# 需求 [fix_ts4L2]

## 反馈

1. Fred反馈目录'/1200'下的文件数量过多


## 需求提出人:   Fred

## 修改人：      <PERSON><PERSON><PERSON>

## 提出日期:     2025-07-26

## 原因
1. trebReso在24年最后一周import上线,存在大量`ts`为24年最后一周的房源,计算`L1`时根据房源`ts`,导致目录'/1200'下存在大量图片

## 解决办法

1. 由`ts`改用`onD`计算`L1`。
2. `onD`需要针对不同board添加mapping,根据不同字段来计算。
3. 补充batch迁移目录'/1200'下的图片,修改rni下的`phoP`字段。


## 是否需要补充UT

1. **需要** - 已添加完整的单元测试


## 确认日期:    2025-07-26


## online-step

1. 如果先上线pr`https://github.com/real-rm/gofile/pull/11`,需要更新`go.mod`,如果后上线该pr,需要更新`go.mod`后重启相关服务
2. 重启相关服务
  ```
  systemctl --user stop batch@goresodownloadTRB
  cd goresodownload
  make build
  cd rmconfig
  ./start.sh -t batch -n goresodownloadTRB -d "goresodownload" -cmd "bin/goresodownload.bin -board TRB -force"
  ```
3. 在目录'/1200'图片全部下载完成后运行batch，对图片进行迁移
```
./start.sh  -n migrate_trbPic -d "goresodownload" -cmd "cmd/batch/migrate_trbPic/main.go -board=TRB -dir=/1200 -dryrun"
```