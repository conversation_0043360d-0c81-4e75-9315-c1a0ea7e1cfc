# 需求 [divide_disk_download]

## 反馈

1. Fred反馈图片下载改用分盘下载保存
2. 这次修改把addqueue/needanalyze去掉一个，然后下载速度也统计一下，比如70线程，每个1M/s，应该显示70M/s。这个写盘的统计有些问题，也订正一下吧。

## 需求提出人:   Fred/Maggie

## 修改人：      <PERSON><PERSON>ia<PERSON>ei

## 提出日期:     2025-08-01

## 原因

1. 目前是服务器下载图片后保存到固态盘和挂载盘,但是写挂载盘很慢,改用两个服务器分别执行下载任务,一个写挂载盘,一个写固态盘

## 解决办法

1. 两台服务器各自有一份自己的`queue collection`。eg: `reso_photo_download_queue_ca6`, `reso_photo_download_queue_ca7`
  1. 代码中设置queue表: `ca6Queue:'reso_photo_download_queue_ca6'`, `ca7Queue:'reso_photo_download_queue_ca7'`
  2. 启动命令添加参数 disk(ca6,ca7), 通过该参数获取对应的`queue`表
  3. 写入queue时,同时写入 ca6Queue,ca7Queue
2. 启动命令添加一个参数开关(eg: `isWatch`),当开关打开时watch rni的房源表
3. 在监听到数据变化(watch的rni表)更新`queue`表时, 根据房源信息计算`phoP`(merged表不存在时计算,存在时使用merged表的`phoP`字段) 并分别写入两个`queue collection`中。
4. merged表新增`phoDlOs`字段来记录各服务器的下载情况(`phoDlOs:{ca6:new Date(),ca7:new Date()}`)
5. 在图片下载完成更新`merged`表`['phoP','phoLH','docLH','tnLH']`字段时, 获取房源信息并通过`media`字段来计算`phoLH`,`docLH`,`tnLH`,与需要更新的信息(下载完成后得到的`['phoLH','docLH','tnLH']`)比较(修改`getNewMediaInfo`函数来支持)
  1. 与media数据不一致时,将信息写入到中间表`reso_photo_inconsistent`, 重新添加任务到queue, 需要修改`Analyze`函数,改为先读取`reso_photo_inconsistent`表,如果数据存在,使用`['actualPhoLH','actualDocLH','actualTnLH']`字段来代替merged表的`['phoLH','docLH','tnLH']`字段,
  2. 与media数据一致时,更新merged表并删除`reso_photo_inconsistent`表的相关信息,更新时需要判断merged表中是否已经存在`['phoLH','docLH','tnLH']`等字段并进行比较
    1. 存在`['phoLH','docLH','tnLH']`字段并相同,仅更新`phoDlOs`字段
    2. 不存在`['phoLH','docLH','tnLH']`字段或数据不同,更新`['phoP','phoLH','docLH','tnLH','phoDlOs']`字段
6. 统计log信息`addedToQueue/needAnalyze`去掉一个,订正下载/写盘速度统计
7. speedmeter需要支持自动清零(默认24h)
<!-- 测试与media数据不一致的情况, 可以不真实下载，改用sleep等进行mock测试 -->

### reso_photo_inconsistent

```json
{
  "_id": "", // 复合key, propId + disk
  "board": "",
  "expectedPhoLH": [1234, 5678],
  "expectedDocLH": ["abc.pdf", "def.doc"],
  "expectedTnLH": 9999,
  "actualPhoLH": [1234, 5679], // 实际计算出的值
  "actualDocLH": ["abc.pdf"],
  "actualTnLH": 9998,
  "phoP":"",
  "ts": "",
  "_mt": ""
}
```

## 是否需要补充UT

1. 需要补充并修复UT

## 确认日期:    2025-08

## online-step

1. 在`https://github.com/real-rm/gospeedmeter/pull/3`上线后，更新go.mod
2. 把现在`reso_photo_download_queue`表复制成`reso_photo_download_queue_ca6`,`reso_photo_download_queue_ca7`
3. 分别在ca6和ca7重启goresodownload服务,有watch的服务添加isWatch参数
  ```
  systemctl --user stop batch@goresodownloadTRB
  cd goresodownload
  make build
  cd rmconfig
  ./start.sh -t batch -n goresodownloadTRB -d "goresodownload" -cmd "bin/goresodownload.bin -board TRB -force -disk ca6/ca7 -isWatch"
  ```
