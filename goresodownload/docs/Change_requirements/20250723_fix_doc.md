# 需求 [fix_doc]

## 反馈

1. W9355038 本地有文件 `/mnt/md0/imgs/MLS/TRB/1200/c50ce/W9355038_8mCKu.mp3`，但是 docLH 后缀不对
   ```
   docLH: [ '-107903121.pdf', '895644978.pdf', '-1573013184.pdf' ]
   ```
   音频文件 (audio/mpeg) docLH扩展名不正确

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-07-23

## 原因
1. `generateHashList` 函数只区分照片和非照片，将所有非照片媒体都归类为文档（这个逻辑是正确的）
2. 问题在于扩展名获取逻辑：音频文件 (`tp: "audio/mpeg"`) 在 docLH 中的扩展名不正确
3. 扩展名获取逻辑分散在多个地方，违反 DRY 原则
4. 缺少对各种 MIME 类型的统一处理，导致非图像类型的扩展名映射不准确

## 解决办法

### 1. 创建统一的扩展名映射函数
- 新增 `getFileExtensionFromMimeType()` 函数，统一处理 MIME 类型到文件扩展名的映射
- 支持所有常见的媒体类型：图像、文档、音频、视频、文本、归档等
- 遵循 DRY 原则，避免重复的扩展名映射逻辑

### 2. 修正扩展名映射逻辑
- 修改 `generateFileName()` 函数使用新的扩展名映射函数
- 修改 `generateHashList()` 函数使用新的扩展名映射函数
- 确保音频文件在 docLH 中显示正确的 `.mp3` 扩展名
- 为文档上下文添加特殊处理：无 MIME 类型时默认使用 `.pdf`

### 3. 支持的 MIME 类型
**图像类型**：
- `image/jpeg`, `image/png`, `image/gif`, `image/webp`, `image/bmp`, `image/tiff`, `image/svg+xml`, `image/heic`, `image/vnd.microsoft.icon`
- 特殊处理：`immge/jpeg` (拼写错误), `jpeg` (简化格式)

**文档类型**：
- `application/pdf`, `application/msword`, `application/vnd.openxmlformats-officedocument.wordprocessingml.document`
- `application/vnd.ms-excel`, `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- `application/rtf`, `text/rtf`, `application/vnd.oasis.opendocument.text`

**音频/视频类型**：
- `audio/mpeg` → `.mp3`
- `video/mp4` → `.mp4`, `video/quicktime` → `.mov`

**其他类型**：
- 文本、归档、二进制文件等

### 4. 增强 addToQueue 功能
- 支持 `-id` 参数：处理特定 ID 列表
- **新增 `-query` 参数**：支持自定义 MongoDB 查询条件
- 示例：`-query='{"docLH":{"$ne":null}}'` 查询有 docLH 且不为 null 的文档


## 实际修改内容

### 1. 新增函数
- `getFileExtensionFromMimeType(mimeType string) string`：统一的 MIME 类型到扩展名映射

### 2. 修改的函数
- `generateFileName()`：使用新的扩展名映射函数
- `generateHashList()`：使用新的扩展名映射函数，并为无 MIME 类型的文档添加 `.pdf` 默认值

### 3. 删除的常量
- 移除了 `DocExtension` 常量，逻辑整合到新函数中

### 4. addToQueue 增强
- 新增 `-query` 参数支持自定义查询条件
- 更新使用文档和帮助信息

## 是否需要补充UT

1. **已完成** - 添加了完整的单元测试：
   - `TestGetFileExtensionFromMimeType`：测试所有支持的 MIME 类型
   - `TestGenerateHashListWithMediaTypes`：测试不同媒体类型的哈希列表生成
   - 所有现有测试继续通过

## 确认日期:    2025-07-23


## online-step

1. 重启相关服务
   ```bash
   systemctl --user stop batch@goresodownloadTRB
   cd goresodownload
   make build
   cd rmconfig
   ./start.sh -t batch -n goresodownloadTRB -d "goresodownload" -cmd "bin/goresodownload.bin -board TRB -force"
   ```

2. 运行 batch，将之有docLH的文档添加到队列
   ```bash

   # 自定义查询：处理有 docLH 但没有 phoLH 的文档
   ./start.sh -n addToQueue -d "goresodownload" -cmd "cmd/batch/addToQueue/main.go -board=TRB -query='{docLH:{\$ne:null}}' -dryrun"

   ```
】