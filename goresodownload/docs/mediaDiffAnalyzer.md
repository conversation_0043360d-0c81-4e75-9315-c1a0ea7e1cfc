#### 1.1 Media 字段监控逻辑（car/crea/bcre/rae）

* 监听对应的源表名中 Media 字段的变更：updatedFields.Media 存在时触发

A. 获取旧列表
从 logs 表读取该 prop 第二新的logs文件， 取旧 Media 字段，按 MediaKey 映射为 map。

区分：Photo → oldPhotosMap，Others → oldDocsMap

B. 获取新列表
从变更事件 updatedFields.Media 中取新 Media 列表。

执行 diff 比对（新增、删除、顺序变更），并构造任务队列：

* 下载新增图片，删除旧图片
* 下载成功后，更新 merged 表：添加 phoLH/docLH

  * 每个媒体任务携带：媒体类型（Photo/PDF）、MediaKey、文件 URL、原始顺序字段。
  * 使用带顺序约束的任务通道（channel + buffer + taskID），保持下载后按顺序写入本地。
  * 下载任务支持并发执行（每个 prop 限并发 3-5 个）。

#### 1.2 TREB 特有处理逻辑

* `phoUrls` 字段包含一个压缩 JSON 图片列表，解压后获取到目前需要的图片list。

* 需要读取 `reso_treb_evow_media` 表，按 `RRK` 提取所有关联记录：


  * 去除已标记为 `MS = deleted` 的记录。删除对应图
  * 从本地路径获取已经存在的图，然后差集运算获取 toDownload 集合

* 非 "Photo" 类型（如 PDF、文档、视频）也会参与后续下载及 docLH 填充。


```go
// MediaChangeEvent 表示一次媒体字段的变更信息
struct MediaChangeEvent {
    Sid       string
    OldMedia  []MediaInfo
    NewMedia  []MediaInfo
    PropTs    time.Time
}
```
API
---

### 2. MediaDiffAnalyzer 差异分析器

根据 Old/New 列表对比生成下载和删除任务。

```go
interface MediaDiffAnalyzer {
    Analyze(event MediaChangeEvent) ([]MediaTask, []DeleteTask)
}
```

处理逻辑流程图:
```
          +-------------------------+
          |   MongoDB Watcher      |
          +-------------------------+
                      |
          +-----------v-----------+
          |   判断字段变更类型     |
          +-----------+-----------+
                      |
        +-------------+--------------+
        |                            |
  [字段为 Media]              [字段为 phoUrls]
        |                            |
  对比更新前后 MediaKey      解压 phoUrls 获取图链
        |                            |
   下载新增图 / 删除旧图         下载新图 / 删除 MS:deleted 图
        |                            |
        +-------------+--------------+
                      |
         更新 merged 表字段
           - phoLH
           - docLH
```

逻辑要点：
## car等board下载处理：

旧图片列表为 [pic1, pic2, pic3, pic4]，
新图片列表为 [pic1, pic2, pic6]，
处理逻辑应是：
pic1, pic2 保留；
删除 pic3, pic4；
下载 pic6；
最终 phoLH 顺序为 [pic1_hash, pic2_hash, pic6_hash]。

如果 prop 中的某一张图下载失败，应该 整体不写入 phoLH，并保留清晰的错误与重试机制，否则会导致：
merged 表中 phoLH 不完整，数据不一致；
顺序错乱，影响前端展示；
调试困难。

1. 需要在并发前先进行差异分析，将任务分为：

保留的图片（无需重新下载）；
新增的图片（需要下载）；
被删除的旧图（需要从硬盘上删除）。

🧠 建议处理步骤（完整方案）

✅ Step 1. 旧图 vs 新图：差异分析 （图片和非图片共用逻辑）
```
type Media struct {
    Key  string // 用来识别唯一图片，比如 MediaKey
    URL  string
}

oldList := []Media{pic1, pic2, pic3, pic4}
newList := []Media{pic1, pic2, pic6}

// 构建 map 用于对比
oldMap := make(map[string]Media)
for _, m := range oldList {
    oldMap[m.Key] = m
}

newMap := make(map[string]Media)
for _, m := range newList {
    newMap[m.Key] = m
}

var (
    keepList    []Media
    toDownload  []Media
    toDelete    []Media
)

for _, m := range newList {
    if _, exists := oldMap[m.Key]; exists {
        keepList = append(keepList, m) // 保留
    } else {
        toDownload = append(toDownload, m) // 新增
    }
}

for _, m := range oldList {
    if _, exists := newMap[m.Key]; !exists {
        toDelete = append(toDelete, m) // 已被移除
    }
}

```

✅ Step 2. 删除旧图
```
for _, m := range toDelete {
    deleteLocalFile(m.Key) // 删除本地旧图片
}

```

✅ Step 3. 并发下载新图 + 构建完整顺序列表（保持顺序）
```
// 最终结果列表（有序，对应新图）
results := make([]string, len(newList))

// 下载任务只处理 toDownload 中的 Key，结果放入对应 idx
taskCh := make(chan int)
var wg sync.WaitGroup

// 启动 workers
for i := 0; i < workerCount; i++ {
    go func() {
        for idx := range taskCh {
            m := newList[idx]
            hash := ""
            err := error(nil)

            if _, isNew := newMap[m.Key]; isNew && !existsIn(keepList, m.Key) {
                hash, err = downloadAndHash(m.URL)
            } else {
                hash = getLocalHash(m.Key)
            }

            results[idx] = PhotoDownloadResult{Hash: hash, Err: err}

            if err != nil {
                failed = true
            }

            wg.Done()
        }
    }()
}

// 发送任务
for i := range newList {
    wg.Add(1)
    taskCh <- i
}
wg.Wait()
close(taskCh)
```
✅ 因为我们用了 idx 对应 newList[i]，results 顺序就是 newList 顺序，也就是最终要写入的 phoLH。

✅ 最终 Step 4. 写入 merged 表
```
if failed {
    log.Errorf("Prop %s: download failed, skipping phoLH write", propID)
    // 可选：保存失败记录到 DB、发送通知等
    return
}

var finalPhoLH []string
for _, r := range results {
    finalPhoLH = append(finalPhoLH, r.Hash)
}

writeToMerged(propID, finalPhoLH)
```
对 doc 文件也一样处理，只是写入 docLH 字段，保存路径不同。

同时需要增加：
重试机制	每张失败的图片可尝试下载 2-3 次，仍失败再放弃
失败记录表	存入 DB：prop_id、image_url、err_msg、retry_count 等
告警通知	如果图片连续失败，可通知开发者/发出告警


## treb 的代码流程小结（伪码）
```
// 1. 解压 phoUrls
newPhotoList := decompressPhoUrls(event.UpdatedFields["phoUrls"])

// 2. 从 media 表查找 RRK 相关 media
mediaList := queryMediaByRRK(event.FullDocument["RRK"])

// 3. 删除 MS == Deleted 的文件
deleteAllDeletedFiles(mediaList)
##
// 4. 比较 & 下载新的照片
参考【比较 & 下载新的照片 逻辑流程】

// 5. 下载新的文档
docHashes := downloadDocs(mediaList)

// 6. 写入 merged 表
if success {
    writeMerged(propID, photoHashes, docHashes)
}
```


比较 & 下载新的照片 逻辑流程
```
// 拿到当前应存在图的 hash 列表（newHashes）
// 来自 phourls + media 表（不含 deleted）
newHashes := getCurrentPhotoHashesFromPhourlsAndMedia(propID, mlsID)

// 扫描本地目录，获取实际存在的图 hash ??
localHashes := getLocalPhotoHashes(localDir)

// 做差集运算
toDownload := newHashes - localHashes
toDelete := localHashes - newHashes

// 下载缺的
for _, h := range toDownload {
    downloadPhoto(mlsID, h)
}

// 删除多余的
for _, h := range toDelete {
    deletePhotoFile(mlsID, h)
}
```


直接使用goconfig package 使用配置
直接使用golog package 进行log