本文件是 #20241101_file_server_redesign.md 整体需求实现的第三步： 
  新图片保存，prop关联：添加 goaap/src/batch/watchAndUpdateLocalPhoto.go实现watch不同SRC表完成以下内容：
  （1）mls_car_raw_records/reso_crea_raw/bridge_bcre_raw/mls_rae_raw_records表的media字段，如果updatedFields中有'Media'，比较更新前【从对应logs表.mls_car_logs/reso_crea_logs/bridge_bcre_logs/mls_rae_logs中根据sid获得list，找到最新的一个，并获取到其‘data.Media’字段值】和更新后【watch结果的updatedFields中'Media'】图片list中每一张图的变化，进行图片的添加和删除，当处理好后，在对应的merged表中添加结果字段
  （2）reso_treb_evow_merged表的phoUrls字段，如果有变化，解压phoUrls字段获取最新的图片，然后在reso_treb_evow_mediamedia表中找到这个RRK的所有media，删除掉状态【MS：deleted】的，同时下载其他格式文件 MC不是: 'Photo'
  （3）merged更新：所有下载完成后再对应的merged表添加字段： phoLH【hash0,hash2,hash3】  docLH [hash0.pdf, hash1.img,]

实现时候需要：
1. 内存中添加1个map， 记录sid-》L1/L2，长度不超过5000
2. 每个board 运行一个watchAndUpdateLocalPhoto.go文件，输入board src，在文件中实例化一个dirKeyStore，每次下载图片后，更新stat：dirKeyStore.AddDirStats("1200", "abc12", 1, 45) // 1 entities, 45 files。 dirKeyStore会定时（十分钟）记录一次到数据库。
3. 注意DRY
4. dependency injection
5. 图片名称hash计算硬盘上是4位hex，先按照4位hex 需要添加是否一致check