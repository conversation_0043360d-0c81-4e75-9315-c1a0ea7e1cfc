# 📘 文件服务器媒体处理模块 API 设计文档

> 本文档基于《图片存储系统架构设计文档（第三步）》的架构描述，定义各核心模块之间的接口设计，便于后续模块开发、测试与集成。

---

## 🎯 总体目标

模块化设计所有涉及媒体变更监听、下载处理、路径管理与状态同步逻辑，并通过接口定义（Interface）实现解耦，便于扩展和单元测试。

---

## 📦 接口模块划分

### 1. Watcher 模块

监听源表的变更，识别媒体字段变化，生成下载任务。

```go
// Watcher 监听器接口
interface Watcher {
    Start(ctx context.Context) error
    Stop(ctx context.Context) error
}
```

```go
// MediaChangeEvent 表示一次媒体字段的变更信息
struct MediaChangeEvent {
    Sid       string
    OldMedia  []MediaInfo
    NewMedia  []MediaInfo
    PropTs    time.Time
}
```

---

### 2. MediaDiffAnalyzer 差异分析器

根据 Old/New 列表对比生成下载和删除任务。

```go
interface MediaDiffAnalyzer {
    Analyze(event MediaChangeEvent) ([]MediaTask, []DeleteTask)
}
```

---

### 3. Downloader 下载器

负责下载媒体文件。

```go
interface Downloader {
    Download(task MediaTask) error
}

struct MediaTask {
    TaskID    string
    Sid       string
    Order     int
    MediaKey  string
    URL       string
    Type      string // photo/pdf/zip/img
    DestPath  string
    PropTs    time.Time
}
```

---

### 4. FileStorageManager 文件存储与路径生成

负责媒体文件路径生成、本地写入、缓存映射。

```go
interface FileStorageManager {
    GeneratePath(task MediaTask) (string, error) // 返回 DestPath
    Save(task MediaTask, content []byte) error
    Delete(task DeleteTask) error
    GetDirKey(sid string) (L1 string, L2 string, err error)
}
```

---

### 5. Hasher 哈希生成与一致性校验

```go
interface Hasher {
    HashMediaKey(mediaKey string) string  // 返回前 4 位 hex
    Verify(task MediaTask, path string) (bool, error)
}
```

---

### 6. MergedWriter 合并表写入器

```go
interface MergedWriter {
    UpdatePhotoLH(sid string, hashes []int) error
    UpdateDocLH(sid string, hashes []int) error
}
```

---

### 7. DirKeyStore 目录统计器

```go
interface DirKeyStore {
    AddDirStats(l1, l2 string, entityCount, fileCount int)
    FlushToDB(ctx context.Context) error
}
```

---

### 8. ErrorLogger 错误记录器

```go
interface ErrorLogger {
    LogDownloadError(task MediaTask, err error)
    LogDeleteError(task DeleteTask, err error)
}
```

---

### 9. Scheduler / RetryQueue

可选模块：用于后续支持失败任务重试逻辑。

```go
interface RetryScheduler {
    AddFailedTask(task MediaTask)
    RunRetryLoop(ctx context.Context)
}
```

---

## 🔧 支持结构体定义

```go
struct MediaInfo {
    MediaKey string
    Order    int
    URL      string
    Type     string // photo/pdf/zip
}

struct DeleteTask {
    Sid      string
    MediaKey string
    Path     string
}
```

---

## 🔁 依赖注入与配置建议

* 所有组件通过 `NewXXX(config)` 函数构造，并注入依赖模块。
* 启动参数：board 名、数据源 URI、本地路径前缀、并发限制、LRU 缓存大小等。

---

## ✅ 设计原则

- **模块清晰、职责分离**：每个模块只负责单一逻辑，接口可 Mock 化
- **易于测试与调试**：通过接口定义可以单独测试每个模块
- **支持未来扩展**：如新增视频、音频、AI 模型处理模块，只需实现对应接口

---

## 📌 示例组合入口

```go
type MediaProcessor struct {
    watcher     Watcher
    analyzer    MediaDiffAnalyzer
    downloader  Downloader
    storage     FileStorageManager
    writer      MergedWriter
    dirStats    DirKeyStore
    hasher      Hasher
    logger      ErrorLogger
}
```
✅ 调用流程图（简要）
```mermaid

sequenceDiagram
    participant User as 调用方（主服务）
    participant FSAPI as File Server API
    participant Watcher as Watcher/Listener
    participant Downloader as Downloader
    participant Merger as Merged Writer

    User->>FSAPI: 请求 /trigger-board-sync?board=car
    FSAPI->>Watcher: 启动监听 car 表 Media 字段
    Watcher->>Downloader: 分析差异并生成下载任务
    Downloader->>Downloader: 并发下载文件 + 校验 Hash
    Downloader->>Merger: 下载完成，写入 merged 表 phoLH/docLH
    Merger-->>User: 返回处理成功状态（可选）



直接使用goconfig package 使用配置
直接使用golog package 进行log