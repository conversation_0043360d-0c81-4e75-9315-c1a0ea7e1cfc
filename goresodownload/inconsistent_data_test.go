package goresodownload

import (
	"context"
	"os"
	"path/filepath"
	"testing"

	"github.com/real-rm/gohelper"
	"github.com/real-rm/gomongo"
	"github.com/stretchr/testify/assert"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func setupInconsistentDataTest(t *testing.T) (*gomongo.MongoCollection, func()) {
	// Setup test environment similar to downloader_test.go
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	configPath, err := filepath.Abs(filepath.Join(currentDir, "local.test.ini"))
	if err != nil {
		t.Fatalf("Failed to get config path: %v", err)
	}
	gohelper.SetRmbaseFileCfg(configPath)

	// Initialize test environment
	if err := gohelper.SetupTestEnv(gohelper.TestOptions{
		UseEnvConfig: true,
	}); err != nil {
		t.Fatalf("Failed to setup test environment: %v", err)
	}

	// Initialize MongoDB
	if err := gomongo.InitMongoDB(); err != nil {
		t.Fatalf("Failed to initialize MongoDB: %v", err)
	}

	// Create a test collection
	testCollName := "test_reso_photo_inconsistent_" + primitive.NewObjectID().Hex()
	collection := gomongo.Coll("rni", testCollName)

	cleanup := func() {
		// Clean up test collection
		_ = collection.Drop(context.Background())
	}

	return collection, cleanup
}

func TestNewInconsistentDataManager(t *testing.T) {
	manager := NewInconsistentDataManager()
	assert.NotNil(t, manager)
	assert.NotNil(t, manager.collection)
}

func TestInconsistentDataManager_BasicDatabaseOperations(t *testing.T) {
	collection, cleanup := setupInconsistentDataTest(t)
	defer cleanup()

	manager := &InconsistentDataManager{collection: collection}

	// Test basic save and retrieve
	testData := &InconsistentData{
		Board:         "CAR",
		PropID:        "test123",
		Disk:          "ca6",
		ExpectedPhoLH: []int32{1234, 5678},
		ExpectedDocLH: []string{"abc.pdf"},
		ExpectedTnLH:  9999,
		ActualPhoLH:   []int32{1234, 5679},
		ActualDocLH:   []string{"def.pdf"},
		ActualTnLH:    9998,
		PhoP:          "/test/path",
	}

	// Save data
	err := manager.SaveInconsistentData(testData)
	assert.NoError(t, err)

	// Retrieve data
	retrieved, err := manager.GetInconsistentData("test123", "ca6")
	assert.NoError(t, err)
	assert.NotNil(t, retrieved)

	// Check if data was saved correctly
	t.Logf("Original data: %+v", testData)
	t.Logf("Retrieved data: %+v", retrieved)

	// Check raw data in MongoDB
	var rawData bson.M
	err = collection.FindOne(context.Background(), bson.M{"_id": "test123_ca6"}).Decode(&rawData)
	assert.NoError(t, err)
	t.Logf("Raw MongoDB data: %+v", rawData)

	// Basic checks
	assert.Equal(t, testData.Board, retrieved.Board)
	assert.Equal(t, testData.PropID, retrieved.PropID)
	assert.Equal(t, testData.Disk, retrieved.Disk)
}

func TestGenerateInconsistentID(t *testing.T) {
	tests := []struct {
		name     string
		propID   string
		disk     string
		expected string
	}{
		{
			name:     "valid_ca6",
			propID:   "prop123",
			disk:     "ca6",
			expected: "prop123_ca6",
		},
		{
			name:     "valid_ca7",
			propID:   "prop456",
			disk:     "ca7",
			expected: "prop456_ca7",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GenerateInconsistentID(tt.propID, tt.disk)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestInconsistentDataManager_SaveInconsistentData(t *testing.T) {
	collection, cleanup := setupInconsistentDataTest(t)
	defer cleanup()

	manager := &InconsistentDataManager{collection: collection}

	tests := []struct {
		name    string
		data    *InconsistentData
		wantErr bool
	}{
		{
			name: "valid_data",
			data: &InconsistentData{
				Board:         "CAR",
				PropID:        "prop123",
				Disk:          "ca6",
				ExpectedPhoLH: []int32{1234, 5678},
				ExpectedDocLH: []string{"abc.pdf", "def.doc"},
				ExpectedTnLH:  9999,
				ActualPhoLH:   []int32{1234, 5679},
				ActualDocLH:   []string{"abc.pdf"},
				ActualTnLH:    9998,
				PhoP:          "/test/path",
			},
			wantErr: false,
		},
		{
			name:    "nil_data",
			data:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := manager.SaveInconsistentData(tt.data)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)

				// Verify data was saved
				if tt.data != nil {
					saved, err := manager.GetInconsistentData(tt.data.PropID, tt.data.Disk)
					assert.NoError(t, err)
					assert.NotNil(t, saved)
					assert.Equal(t, tt.data.Board, saved.Board)
					assert.Equal(t, tt.data.PropID, saved.PropID)
					assert.Equal(t, tt.data.Disk, saved.Disk)
					assert.Equal(t, tt.data.ExpectedPhoLH, saved.ExpectedPhoLH)
					assert.Equal(t, tt.data.ExpectedDocLH, saved.ExpectedDocLH)
					assert.Equal(t, tt.data.ExpectedTnLH, saved.ExpectedTnLH)
					assert.Equal(t, tt.data.ActualPhoLH, saved.ActualPhoLH)
					assert.Equal(t, tt.data.ActualDocLH, saved.ActualDocLH)
					assert.Equal(t, tt.data.ActualTnLH, saved.ActualTnLH)
					assert.Equal(t, tt.data.PhoP, saved.PhoP)
				}
			}
		})
	}
}

func TestInconsistentDataManager_GetInconsistentData(t *testing.T) {
	collection, cleanup := setupInconsistentDataTest(t)
	defer cleanup()

	manager := &InconsistentDataManager{collection: collection}

	// Insert test data
	testData := &InconsistentData{
		Board:         "CAR",
		PropID:        "prop123",
		Disk:          "ca6",
		ExpectedPhoLH: []int32{1234, 5678},
		ExpectedDocLH: []string{"abc.pdf"},
		ExpectedTnLH:  9999,
		ActualPhoLH:   []int32{1234, 5679},
		ActualDocLH:   []string{"abc.pdf", "def.doc"},
		ActualTnLH:    9998,
		PhoP:          "/test/path",
	}
	err := manager.SaveInconsistentData(testData)
	assert.NoError(t, err)

	tests := []struct {
		name     string
		propID   string
		disk     string
		wantData bool
		wantErr  bool
	}{
		{
			name:     "existing_data",
			propID:   "prop123",
			disk:     "ca6",
			wantData: true,
			wantErr:  false,
		},
		{
			name:     "non_existing_data",
			propID:   "prop999",
			disk:     "ca6",
			wantData: false,
			wantErr:  false,
		},
		{
			name:     "empty_propID",
			propID:   "",
			disk:     "ca6",
			wantData: false,
			wantErr:  true,
		},
		{
			name:     "empty_disk",
			propID:   "prop123",
			disk:     "",
			wantData: false,
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			data, err := manager.GetInconsistentData(tt.propID, tt.disk)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, data)
			} else {
				assert.NoError(t, err)
				if tt.wantData {
					assert.NotNil(t, data)
					assert.Equal(t, tt.propID, data.PropID)
					assert.Equal(t, tt.disk, data.Disk)
				} else {
					assert.Nil(t, data)
				}
			}
		})
	}
}

func TestInconsistentDataManager_DeleteInconsistentData(t *testing.T) {
	collection, cleanup := setupInconsistentDataTest(t)
	defer cleanup()

	manager := &InconsistentDataManager{collection: collection}

	// Insert test data
	testData := &InconsistentData{
		Board:         "CAR",
		PropID:        "prop123",
		Disk:          "ca6",
		ExpectedPhoLH: []int32{1234},
		ExpectedDocLH: []string{"abc.pdf"},
		ExpectedTnLH:  9999,
		ActualPhoLH:   []int32{5678},
		ActualDocLH:   []string{"def.doc"},
		ActualTnLH:    8888,
		PhoP:          "/test/path",
	}
	err := manager.SaveInconsistentData(testData)
	assert.NoError(t, err)

	tests := []struct {
		name    string
		propID  string
		disk    string
		wantErr bool
	}{
		{
			name:    "existing_data",
			propID:  "prop123",
			disk:    "ca6",
			wantErr: false,
		},
		{
			name:    "non_existing_data",
			propID:  "prop999",
			disk:    "ca6",
			wantErr: false, // Delete operation should not error for non-existing data
		},
		{
			name:    "empty_propID",
			propID:  "",
			disk:    "ca6",
			wantErr: true,
		},
		{
			name:    "empty_disk",
			propID:  "prop123",
			disk:    "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := manager.DeleteInconsistentData(tt.propID, tt.disk)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)

				// Verify data was deleted (only for the first test case)
				if tt.name == "existing_data" {
					data, err := manager.GetInconsistentData(tt.propID, tt.disk)
					assert.NoError(t, err)
					assert.Nil(t, data)
				}
			}
		})
	}
}

func TestInconsistentDataManager_ListInconsistentDataByProp(t *testing.T) {
	collection, cleanup := setupInconsistentDataTest(t)
	defer cleanup()

	manager := &InconsistentDataManager{collection: collection}

	// Insert test data for multiple disks
	testData1 := &InconsistentData{
		Board:         "CAR",
		PropID:        "prop123",
		Disk:          "ca6",
		ExpectedPhoLH: []int32{1234},
		ExpectedDocLH: []string{"abc.pdf"},
		ExpectedTnLH:  9999,
		ActualPhoLH:   []int32{5678},
		ActualDocLH:   []string{"def.doc"},
		ActualTnLH:    8888,
		PhoP:          "/test/path1",
	}
	testData2 := &InconsistentData{
		Board:         "CAR",
		PropID:        "prop123",
		Disk:          "ca7",
		ExpectedPhoLH: []int32{2345},
		ExpectedDocLH: []string{"ghi.pdf"},
		ExpectedTnLH:  7777,
		ActualPhoLH:   []int32{6789},
		ActualDocLH:   []string{"jkl.doc"},
		ActualTnLH:    6666,
		PhoP:          "/test/path2",
	}

	err := manager.SaveInconsistentData(testData1)
	assert.NoError(t, err)
	err = manager.SaveInconsistentData(testData2)
	assert.NoError(t, err)

	tests := []struct {
		name      string
		propID    string
		wantCount int
		wantErr   bool
	}{
		{
			name:      "existing_prop_with_multiple_disks",
			propID:    "prop123",
			wantCount: 2,
			wantErr:   false,
		},
		{
			name:      "non_existing_prop",
			propID:    "prop999",
			wantCount: 0,
			wantErr:   false,
		},
		{
			name:      "empty_propID",
			propID:    "",
			wantCount: 0,
			wantErr:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			results, err := manager.ListInconsistentDataByProp(tt.propID)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, results)
			} else {
				assert.NoError(t, err)
				assert.Len(t, results, tt.wantCount)

				// Verify all results have the correct propID
				for _, result := range results {
					assert.Equal(t, tt.propID, result.PropID)
				}
			}
		})
	}
}

func TestCompareMediaData(t *testing.T) {
	tests := []struct {
		name     string
		expected []int32
		actual   []int32
		want     bool
	}{
		{
			name:     "identical_data",
			expected: []int32{1234, 5678, 9999},
			actual:   []int32{1234, 5678, 9999},
			want:     true,
		},
		{
			name:     "different_data",
			expected: []int32{1234, 5678},
			actual:   []int32{1234, 5679},
			want:     false,
		},
		{
			name:     "different_length",
			expected: []int32{1234, 5678},
			actual:   []int32{1234},
			want:     false,
		},
		{
			name:     "empty_arrays",
			expected: []int32{},
			actual:   []int32{},
			want:     true,
		},
		{
			name:     "nil_vs_empty",
			expected: nil,
			actual:   []int32{},
			want:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CompareMediaData(tt.expected, tt.actual)
			assert.Equal(t, tt.want, result)
		})
	}
}

func TestCompareDocData(t *testing.T) {
	tests := []struct {
		name     string
		expected []string
		actual   []string
		want     bool
	}{
		{
			name:     "identical_data",
			expected: []string{"abc.pdf", "def.doc", "ghi.txt"},
			actual:   []string{"abc.pdf", "def.doc", "ghi.txt"},
			want:     true,
		},
		{
			name:     "different_data",
			expected: []string{"abc.pdf", "def.doc"},
			actual:   []string{"abc.pdf", "xyz.doc"},
			want:     false,
		},
		{
			name:     "different_length",
			expected: []string{"abc.pdf", "def.doc"},
			actual:   []string{"abc.pdf"},
			want:     false,
		},
		{
			name:     "empty_arrays",
			expected: []string{},
			actual:   []string{},
			want:     true,
		},
		{
			name:     "nil_vs_empty",
			expected: nil,
			actual:   []string{},
			want:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := CompareDocData(tt.expected, tt.actual)
			assert.Equal(t, tt.want, result)
		})
	}
}

func TestIsDataConsistent(t *testing.T) {
	tests := []struct {
		name     string
		expected *InconsistentData
		actual   *InconsistentData
		want     bool
	}{
		{
			name: "consistent_data",
			expected: &InconsistentData{
				ExpectedPhoLH: []int32{1234, 5678},
				ExpectedDocLH: []string{"abc.pdf"},
				ExpectedTnLH:  9999,
			},
			actual: &InconsistentData{
				ActualPhoLH: []int32{1234, 5678},
				ActualDocLH: []string{"abc.pdf"},
				ActualTnLH:  9999,
			},
			want: true,
		},
		{
			name: "inconsistent_photo_data",
			expected: &InconsistentData{
				ExpectedPhoLH: []int32{1234, 5678},
				ExpectedDocLH: []string{"abc.pdf"},
				ExpectedTnLH:  9999,
			},
			actual: &InconsistentData{
				ActualPhoLH: []int32{1234, 5679},
				ActualDocLH: []string{"abc.pdf"},
				ActualTnLH:  9999,
			},
			want: false,
		},
		{
			name: "inconsistent_doc_data",
			expected: &InconsistentData{
				ExpectedPhoLH: []int32{1234, 5678},
				ExpectedDocLH: []string{"abc.pdf"},
				ExpectedTnLH:  9999,
			},
			actual: &InconsistentData{
				ActualPhoLH: []int32{1234, 5678},
				ActualDocLH: []string{"def.pdf"},
				ActualTnLH:  9999,
			},
			want: false,
		},
		{
			name: "inconsistent_thumbnail_data",
			expected: &InconsistentData{
				ExpectedPhoLH: []int32{1234, 5678},
				ExpectedDocLH: []string{"abc.pdf"},
				ExpectedTnLH:  9999,
			},
			actual: &InconsistentData{
				ActualPhoLH: []int32{1234, 5678},
				ActualDocLH: []string{"abc.pdf"},
				ActualTnLH:  8888,
			},
			want: false,
		},
		{
			name:     "nil_expected",
			expected: nil,
			actual: &InconsistentData{
				ActualPhoLH: []int32{1234, 5678},
				ActualDocLH: []string{"abc.pdf"},
				ActualTnLH:  9999,
			},
			want: false,
		},
		{
			name: "nil_actual",
			expected: &InconsistentData{
				ExpectedPhoLH: []int32{1234, 5678},
				ExpectedDocLH: []string{"abc.pdf"},
				ExpectedTnLH:  9999,
			},
			actual: nil,
			want:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsDataConsistent(tt.expected, tt.actual)
			assert.Equal(t, tt.want, result)
		})
	}
}
