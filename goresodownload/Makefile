# Makefile for goresodownload
# Author: Generated for goresodownload project
# Description: Build, test, and deployment automation for goresodownload

# Variables
BINARY_NAME=goresodownload.bin
UTILS_BINARY=utils.bin
L2TEST_BINARY=l2test.bin
MAIN_PATH=./cmd/goresodownload
UTILS_PATH=./cmd/utils
L2TEST_PATH=./cmd/l2test
GO_VERSION=1.24.4
COVERAGE_FILE=coverage.out
COVERAGE_HTML=coverage.html
TEST_CONFIG=local.test.ini

# Build flags
GO_LDFLAGS=-s -w
BUILD_FLAGS=-v -ldflags="$(GO_LDFLAGS)"

# Supported board types
BOARDS=CAR TRB DDF BRE EDM CLG OTW

# Colors for output
RED=\033[0;31m
GREEN=\033[0;32m
YELLOW=\033[1;33m
BLUE=\033[0;34m
NC=\033[0m # No Color

.PHONY: all help build build-all clean test test-unit test-e2e test-coverage \
        lint fmt vet deps tidy install run run-dryrun build-info \
        deploy-board dev-setup ci quick


# Default target
all: clean deps build test

# Help target
help:
	@echo "$(BLUE)goresodownload Makefile$(NC)"
	@echo ""
	@echo "$(YELLOW)Available targets:$(NC)"
	@echo "  $(GREEN)build$(NC)          - Build the main goresodownload binary (creates bin/goresodownload.bin)"
	@echo "  $(GREEN)build-all$(NC)      - Build all binaries (main, utils, l2test)"
	@echo "  $(GREEN)build-info$(NC)     - Show build information and usage examples"
	@echo "  $(GREEN)clean$(NC)          - Clean build artifacts and coverage files"
	@echo "  $(GREEN)test$(NC)           - Run all tests with coverage"
	@echo "  $(GREEN)test-unit$(NC)      - Run unit tests only"
	@echo "  $(GREEN)test-e2e$(NC)       - Run end-to-end tests"
	@echo "  $(GREEN)test-coverage$(NC)  - Generate and view test coverage report"
	@echo "  $(GREEN)lint$(NC)           - Run golangci-lint"
	@echo "  $(GREEN)fmt$(NC)            - Format Go code"
	@echo "  $(GREEN)vet$(NC)            - Run go vet"
	@echo "  $(GREEN)deps$(NC)           - Download dependencies"
	@echo "  $(GREEN)tidy$(NC)           - Tidy up go.mod"
	@echo "  $(GREEN)install$(NC)        - Install the binary to GOPATH/bin"
	@echo "  $(GREEN)run$(NC)            - Run with default parameters (CAR board)"
	@echo "  $(GREEN)run-dryrun$(NC)     - Run in dry-run mode (CAR board)"
	@echo "  $(GREEN)deploy-board$(NC)   - Deploy specific board (usage: make deploy-board BOARD=CAR)"
	@echo ""
	@echo "$(YELLOW)Board types:$(NC) $(BOARDS)"
	@echo ""
	@echo "$(YELLOW)Examples:$(NC)"
	@echo "  make build"
	@echo "  make test"
	@echo "  make deploy-board BOARD=TRB"
	@echo "  make run-dryrun BOARD=CAR FORCE=true"

# Build targets
build:
	@echo "$(BLUE)Building $(BINARY_NAME)...$(NC)"
	@mkdir -p bin
	@go build $(BUILD_FLAGS) -o bin/$(BINARY_NAME) $(MAIN_PATH)
	@echo "$(GREEN)Build completed: bin/$(BINARY_NAME)$(NC)"
	@echo "$(YELLOW)To run: ./bin/$(BINARY_NAME) -board CAR -force$(NC)"

build-all: build
	@echo "$(BLUE)Building $(UTILS_BINARY)...$(NC)"
	@go build $(BUILD_FLAGS) -o bin/$(UTILS_BINARY) $(UTILS_PATH)
	@echo "$(BLUE)Building $(L2TEST_BINARY)...$(NC)"
	@go build $(BUILD_FLAGS) -o bin/$(L2TEST_BINARY) $(L2TEST_PATH)
	@echo "$(GREEN)All builds completed$(NC)"
	@echo "$(YELLOW)Binaries available in bin/ directory:$(NC)"
	@ls -la bin/

# Clean target
clean:
	@echo "$(BLUE)Cleaning...$(NC)"
	@rm -rf bin/
	@rm -f $(COVERAGE_FILE) $(COVERAGE_HTML)
	@go clean -cache
	@echo "$(GREEN)Clean completed$(NC)"

# Test targets
test: test-unit test-e2e

test-unit:
	@echo "$(BLUE)Running unit tests...$(NC)"
	@go test -v -coverprofile=$(COVERAGE_FILE) $$(go list ./... | grep -v '/test/e2e')
	@echo "$(GREEN)Unit tests completed$(NC)"

test-e2e:
	@echo "$(BLUE)Running end-to-end tests...$(NC)"
	@go test -v ./test/e2e/...
	@echo "$(GREEN)E2E tests completed$(NC)"

test-coverage: test-unit
	@echo "$(BLUE)Generating coverage report...$(NC)"
	@go tool cover -html=$(COVERAGE_FILE) -o $(COVERAGE_HTML)
	@go tool cover -func=$(COVERAGE_FILE)
	@echo "$(GREEN)Coverage report generated: $(COVERAGE_HTML)$(NC)"

# Code quality targets
lint:
	@echo "$(BLUE)Running golangci-lint...$(NC)"
	@if command -v golangci-lint >/dev/null 2>&1; then \
		golangci-lint run --timeout=5m; \
	else \
		echo "$(RED)golangci-lint not found. Install it with: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest$(NC)"; \
		exit 1; \
	fi

fmt:
	@echo "$(BLUE)Formatting code...$(NC)"
	@go fmt ./...
	@echo "$(GREEN)Code formatted$(NC)"

vet:
	@echo "$(BLUE)Running go vet...$(NC)"
	@go vet ./...
	@echo "$(GREEN)Vet completed$(NC)"

# Dependency management
deps:
	@echo "$(BLUE)Downloading dependencies...$(NC)"
	@go mod download
	@echo "$(GREEN)Dependencies downloaded$(NC)"

tidy:
	@echo "$(BLUE)Tidying go.mod...$(NC)"
	@go mod tidy
	@echo "$(GREEN)go.mod tidied$(NC)"

# Installation
install: build
	@echo "$(BLUE)Installing $(BINARY_NAME)...$(NC)"
	@go install $(MAIN_PATH)
	@echo "$(GREEN)$(BINARY_NAME) installed$(NC)"

# Run targets
BOARD ?= CAR
FORCE ?= false
BATCH_SIZE ?= 3
DRYRUN ?= false

run: build
	@echo "$(BLUE)Running goresodownload (board: $(BOARD), force: $(FORCE), batchSize: $(BATCH_SIZE))...$(NC)"
	@./bin/$(BINARY_NAME) -board $(BOARD) -force=$(FORCE) -batchSize $(BATCH_SIZE)

run-dryrun: build
	@echo "$(BLUE)Running goresodownload in dry-run mode (board: $(BOARD))...$(NC)"
	@./bin/$(BINARY_NAME) -board $(BOARD) -force=$(FORCE) -dryrun=true -batchSize $(BATCH_SIZE)

# Show build info
build-info:
	@echo "$(BLUE)Build Information:$(NC)"
	@echo "$(YELLOW)Binary files will be created in: bin/$(NC)"
	@echo "$(YELLOW)Main binary: bin/$(BINARY_NAME)$(NC)"
	@echo "$(YELLOW)Utils binary: bin/$(UTILS_BINARY)$(NC)"
	@echo "$(YELLOW)L2Test binary: bin/$(L2TEST_BINARY)$(NC)"
	@echo ""
	@echo "$(BLUE)How to run after build:$(NC)"
	@echo "$(GREEN)./bin/$(BINARY_NAME) -board CAR -force$(NC)"
	@echo "$(GREEN)./bin/$(BINARY_NAME) -board TRB -force -dryrun$(NC)"
	@echo "$(GREEN)./bin/$(BINARY_NAME) -board DDF -force -batchSize 3$(NC)"



deploy-board:
ifndef BOARD
	@echo "$(RED)Error: BOARD parameter is required. Usage: make deploy-board BOARD=CAR$(NC)"
	@exit 1
endif
	@echo "$(BLUE)Deploying board: $(BOARD)...$(NC)"
	@if [ -f ../rmconfig/start.sh ]; then \
		cd ../rmconfig && ./start.sh -t batch -n goresodownload$(BOARD) -d "goresodownload" -cmd "bin/goresodownload.bin -board $(BOARD) -force"; \
	else \
		echo "$(RED)Error: ../rmconfig/start.sh not found$(NC)"; \
		exit 1; \
	fi
	@echo "$(GREEN)Deployment completed for $(BOARD)$(NC)"

# Development helpers
dev-setup: deps tidy fmt vet lint
	@echo "$(GREEN)Development environment setup completed$(NC)"

# CI/CD targets
ci: deps tidy build lint test
	@echo "$(GREEN)CI pipeline completed$(NC)"

# Quick development cycle
quick: fmt vet build test-unit
	@echo "$(GREEN)Quick development cycle completed$(NC)"
