# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
*.bin

# Build output directory
bin/
dist/
build/

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out
*.prof

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work
go.work.sum

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Log files
*.log
logs/

# Configuration files with sensitive data
*.ini.local
*.toml.local
config.local.*

# Coverage reports
coverage.out
coverage.html
coverage.xml

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Local development files
.env
.env.local
.env.*.local

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup
