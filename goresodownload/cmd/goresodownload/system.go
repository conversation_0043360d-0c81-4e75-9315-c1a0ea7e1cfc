package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"runtime"
	"strconv"
	"strings"
	"time"

	golog "github.com/real-rm/golog"
)

// System monitoring constants
const (
	// Memory monitoring settings
	MemoryCheckInterval = 10 * time.Second   // Check memory usage every 10 seconds
	MaxMemoryBytes      = 1024 * 1024 * 1024 // 1GB absolute memory limit
	MaxMemoryPercent    = 0.5                // 50% of system memory threshold
	MaxGoroutines       = 500                // Alert threshold for goroutine count

	// HTTP connection cleanup settings
	HTTPCleanupInterval = 30 * time.Second // Clean up HTTP connections every 30 seconds

	// Goroutine monitoring thresholds
	ExtremeGoroutineThreshold = 1000 // Threshold for extremely high goroutine count

	// Memory conversion constants
	BytesPerKB = 1024
	BytesPerMB = 1024 * 1024
	BytesPerGB = 1024 * 1024 * 1024
)

// monitorMemoryUsage monitors memory usage and logs warnings
func monitorMemoryUsage(ctx context.Context) {
	ticker := time.NewTicker(MemoryCheckInterval)
	defer ticker.Stop()

	totalSystemMemoryBytes := getSystemMemory()
	golog.Info("Memory monitoring started", "systemMemory", formatBytes(totalSystemMemoryBytes))

	for {
		select {
		case <-ticker.C:
			var m runtime.MemStats
			runtime.ReadMemStats(&m)

			currentMemoryBytes := m.Alloc // Bytes allocated and still in use
			numGoroutines := runtime.NumGoroutine()
			golog.Info("numGoroutines", "numGoroutines", numGoroutines)

			if currentMemoryBytes > MaxMemoryBytes || float64(currentMemoryBytes)/float64(totalSystemMemoryBytes) > MaxMemoryPercent || numGoroutines > MaxGoroutines {
				golog.Warn("High resource usage detected",
					"currentMemory", formatBytes(currentMemoryBytes),
					"systemMemory", formatBytes(totalSystemMemoryBytes),
					"percentage", fmt.Sprintf("%.2f%%", float64(currentMemoryBytes)/float64(totalSystemMemoryBytes)*100),
					"heapAlloc", formatBytes(m.HeapAlloc),
					"heapSys", formatBytes(m.HeapSys),
					"numGoroutines", numGoroutines,
					"maxGoroutines", MaxGoroutines)

				// Force garbage collection
				runtime.GC()

				// If goroutines are extremely high, log stack traces for debugging
				if numGoroutines > ExtremeGoroutineThreshold {
					golog.Error("Extremely high goroutine count detected - possible leak",
						"numGoroutines", numGoroutines)
				}
			} else {
				golog.Debug("Resource usage normal",
					"currentMemory", formatBytes(currentMemoryBytes),
					"heapAlloc", formatBytes(m.HeapAlloc),
					"numGoroutines", numGoroutines)
			}
		case <-ctx.Done():
			golog.Debug("Memory monitor stopping")
			return
		}
	}
}

// getSystemMemory attempts to get the total system memory in bytes
func getSystemMemory() uint64 {
	// Try to read from /proc/meminfo on Linux
	if data, err := os.ReadFile("/proc/meminfo"); err == nil {
		lines := strings.Split(string(data), "\n")
		for _, line := range lines {
			if strings.HasPrefix(line, "MemTotal:") {
				fields := strings.Fields(line)
				if len(fields) >= 2 {
					if kb, err := strconv.ParseUint(fields[1], 10, 64); err == nil {
						return kb * BytesPerKB // Convert KB to bytes
					}
				}
				break
			}
		}
	}

	// Fallback: assume 4GB if we can't detect
	return 4 * BytesPerGB
}

// cleanupHTTPConnections periodically forces cleanup of HTTP connections to prevent leaks
func cleanupHTTPConnections(ctx context.Context) {
	ticker := time.NewTicker(HTTPCleanupInterval)
	defer ticker.Stop()

	golog.Info("Aggressive HTTP connection cleanup started")

	for {
		select {
		case <-ticker.C:
			// Force cleanup of default HTTP transport
			if transport, ok := http.DefaultTransport.(*http.Transport); ok {
				transport.CloseIdleConnections()
			}

			// Force garbage collection to clean up any leaked connections
			runtime.GC()

			numGoroutines := runtime.NumGoroutine()
			golog.Debug("numGoroutines", numGoroutines) // Debug output

			if numGoroutines > 200 { // Lower threshold
				golog.Warn("numGoroutines more than 200",
					"numGoroutines", numGoroutines)

				runtime.GC()
			}

		case <-ctx.Done():
			golog.Debug("HTTP connection cleanup stopping")
			return
		}
	}
}

// formatBytes formats bytes into human readable format
func formatBytes(bytes uint64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}
