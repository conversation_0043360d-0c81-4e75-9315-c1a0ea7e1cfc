package main

import (
	"sync"
	"testing"
)

// TestTaskStageTracking tests the new task stage tracking mechanism
func TestTaskStageTracking(t *testing.T) {
	// Reset task status
	taskStatus = sync.Map{}

	propID := "TEST_PROP_123"

	// Test starting processing
	startProcessingProp(propID)

	// Check that task was added
	count, _ := getProcessingStatus()
	if count != 1 {
		t.Errorf("Expected processing count 1, got %d", count)
	}

	// Check initial stage
	if statusInterface, exists := taskStatus.Load(propID); exists {
		status := statusInterface.(*TaskStatus)
		if status.Stage != StageAnalyzing {
			t.Errorf("Expected stage %s, got %s", StageAnalyzing, status.Stage)
		}
	} else {
		t.Error("Task status not found")
	}

	// Test stage updates
	stages := []string{StageDownloading, StageUpdatingDB, StageRemovingQueue}
	for _, stage := range stages {
		updateTaskStage(propID, stage)
		
		if statusInterface, exists := taskStatus.Load(propID); exists {
			status := statusInterface.(*TaskStatus)
			if status.Stage != stage {
				t.Errorf("Expected stage %s, got %s", stage, status.Stage)
			}
		} else {
			t.Error("Task status not found after update")
		}
	}

	// Test completion check - should not be complete yet
	if isAllTasksFullyComplete() {
		t.Error("Tasks should not be fully complete yet")
	}

	// Test finishing processing
	finishProcessingProp(propID)

	// Check that task was removed
	count, _ = getProcessingStatus()
	if count != 0 {
		t.Errorf("Expected processing count 0, got %d", count)
	}

	// Task should be deleted from taskStatus after finishing
	if _, exists := taskStatus.Load(propID); exists {
		t.Error("Task status should be deleted after completion")
	}

	// Test completion check - should be complete now
	if !isAllTasksFullyComplete() {
		t.Error("Tasks should be fully complete now")
	}

	t.Log("Task stage tracking test passed")
}

// TestGracefulShutdownWithStages tests graceful shutdown with stage tracking
func TestGracefulShutdownWithStages(t *testing.T) {
	// Reset task status
	taskStatus = sync.Map{}

	// Simulate multiple tasks in different stages
	tasks := []struct {
		propID string
		stage  string
	}{
		{"PROP_1", StageDownloading},
		{"PROP_2", StageUpdatingDB},
		{"PROP_3", StageRemovingQueue},
	}

	// Start all tasks
	for _, task := range tasks {
		startProcessingProp(task.propID)
		updateTaskStage(task.propID, task.stage)
	}

	// Should not be complete
	if isAllTasksFullyComplete() {
		t.Error("Tasks should not be complete with active stages")
	}

	// Complete tasks one by one
	for _, task := range tasks {
		finishProcessingProp(task.propID)
		
		// Check status
		count, props := getProcessingStatus()
		t.Logf("After completing %s: count=%d, props=%v", task.propID, count, props)
	}

	// Should be complete now
	if !isAllTasksFullyComplete() {
		t.Error("All tasks should be complete now")
	}

	t.Log("Graceful shutdown with stages test passed")
}

// TestTaskStatusConsistency tests that task status remains consistent
func TestTaskStatusConsistency(t *testing.T) {
	// Reset task status
	taskStatus = sync.Map{}

	// Start multiple tasks
	taskIDs := []string{"TASK_1", "TASK_2", "TASK_3"}

	for _, propID := range taskIDs {
		startProcessingProp(propID)
	}

	// Verify all tasks are tracked
	count, props := getProcessingStatus()
	if count != 3 {
		t.Errorf("Expected processing count 3, got %d", count)
	}
	if len(props) != 3 {
		t.Errorf("Expected 3 task props, got %d", len(props))
	}

	// Verify completion check
	if isAllTasksFullyComplete() {
		t.Error("Tasks should not be complete yet")
	}

	// Finish all tasks
	for _, propID := range taskIDs {
		finishProcessingProp(propID)
	}

	// Verify all tasks are cleaned up
	count, props = getProcessingStatus()
	if count != 0 {
		t.Errorf("Expected processing count 0, got %d", count)
	}
	if len(props) != 0 {
		t.Errorf("Expected 0 task props, got %d", len(props))
	}

	// Verify completion check
	if !isAllTasksFullyComplete() {
		t.Error("All tasks should be complete now")
	}

	t.Log("Task status consistency test passed")
}


