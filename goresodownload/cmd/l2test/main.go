package main

import (
	"context"
	"fmt"
	"os"
	"sort"
	"sync"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"

	goconfig "github.com/real-rm/goconfig"
	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"github.com/real-rm/gostreaming"
)

// L2 test constants
const (
	// Time range settings
	QueryTimeRangeDays = 7 // Query data from last 7 days

	// Progress reporting
	ProgressReportInterval = 1000 // Report progress every 1000 processed items
	MaxDebugLogCount       = 3    // Maximum number of debug logs for missing SID

	// Streaming settings
	ConcurrentProcessingLimit = 10 // Process 10 records concurrently
)

func init() {
	// Load configuration first
	if err := goconfig.LoadConfig(); err != nil {
		golog.Error("Failed to load config", "error", err)
	}

	// Initialize logging last
	if err := golog.InitLog(); err != nil {
		golog.Error("Failed to initialize logging", "error", err)
	}

	// Initialize MongoDB second (before any package that needs it)
	if err := gomongo.InitMongoDB(); err != nil {
		golog.Error("Failed to initialize MongoDB", "error", err)
	}
}

// SourceTable represents a table to be monitored
type SourceTable struct {
	Name  string
	Board string // Board short name (TRB, DDF, etc.)
	Key   string // Key field name
}

// L2Distribution stores the distribution data
type L2Distribution struct {
	Board     string
	TotalSIDs int
	Counts    map[int]int
}

// MongoStream implements gostreaming.Stream interface
type MongoStream struct {
	cursor *mongo.Cursor
}

func (s *MongoStream) Next(ctx context.Context) bool {
	return s.cursor.Next(ctx)
}

func (s *MongoStream) Decode(v interface{}) error {
	return s.cursor.Decode(v)
}

func (s *MongoStream) Err() error {
	return s.cursor.Err()
}

func main() {
	// Define source tables based on architecture design
	sourceTables := []SourceTable{
		// {Name: "mls_car_raw_records", Board: "CAR", Key: "ListingId"},
		// {Name: "reso_crea_raw", Board: "DDF", Key: "ListingKey"},
		// {Name: "bridge_bcre_raw", Board: "BRE", Key: "ListingId"},
		// {Name: "mls_rae_raw_records", Board: "EDM", Key: "ListingId"},
		{Name: "reso_treb_evow_merged", Board: "TRB", Key: "ListingKey"},
	}

	// Calculate time range (last week)
	endTime := time.Now().UTC()
	startTime := endTime.AddDate(0, 0, -QueryTimeRangeDays)

	golog.Info("Query time range",
		"start", startTime.Format(time.RFC3339),
		"end", endTime.Format(time.RFC3339))

	// Process each source table
	distributions := make(map[string]*L2Distribution)

	// Process tables sequentially
	for _, table := range sourceTables {
		golog.Info("Processing table", "table", table.Name, "board", table.Board)

		// Get L2Size from config
		l2Size, exists := levelStore.SOURCE_L2_SIZE[table.Board]
		if !exists {
			golog.Error("L2 size not found for board", "board", table.Board)
			continue
		}
		golog.Debug("Found L2 size", "board", table.Board, "size", l2Size)

		dist := &L2Distribution{
			Board:  table.Name,
			Counts: make(map[int]int),
		}

		// Create a mutex to protect map access
		var mu sync.Mutex

		// Query records from last week
		coll := gomongo.Coll("rni", table.Name)
		if coll == nil {
			golog.Error("failed to get collection", "table", table.Name)
			continue
		}

		options := gomongo.QueryOptions{
			Projection: bson.D{
				{Key: table.Key, Value: 1},
			},
		}

		// Then check documents in time range
		query := bson.M{
			"ts": bson.M{
				"$gte": startTime,
				"$lte": endTime,
			},
		}

		// Debug: Print the exact query
		golog.Info("Debug: Time range - start", "start", startTime, "end", endTime)

		cursor, err := coll.Find(context.Background(), query, options)
		if err != nil {
			golog.Error("failed to query collection", "table", table.Name, "error", err)
			continue
		}

		// Count documents in time range
		count, err := coll.CountDocuments(context.Background(), query)
		if err != nil {
			golog.Error("failed to count documents in time range", "table", table.Name, "error", err)
		} else {
			golog.Info("Found documents in time range", "table", table.Name, "count", count)
		}

		// Create a channel to signal completion
		done := make(chan struct{})

		// Create streaming options
		opts := gostreaming.StreamingOptions{
			Stream: cursor,
			Process: func(item interface{}) error {
				// Handle both bson.D and bson.M types
				var doc bson.M
				switch v := item.(type) {
				case bson.M:
					doc = v
				case bson.D:
					// Convert bson.D to bson.M
					doc = make(bson.M)
					for _, elem := range v {
						doc[elem.Key] = elem.Value
					}
				default:
					golog.Error("Invalid document type", "table", table.Name, "type", fmt.Sprintf("%T", item))
					return fmt.Errorf("invalid document type: %T", item)
				}

				var sid string
				if s, ok := doc[table.Key].(string); ok && s != "" {
					sid = s
				}

				if sid == "" {
					if dist.TotalSIDs < MaxDebugLogCount {
						golog.Debug("Skipping document without sid", "table", table.Name, "doc", doc)
					}
					return nil
				}

				// Calculate L2 index
				index, err := levelStore.CalculateL2Index(ReverseString(sid), l2Size)
				if err != nil {
					golog.Error("failed to calculate L2 index", "sid", sid, "error", err)
					return nil
				}

				// Update distribution with mutex protection
				mu.Lock()
				dist.Counts[index]++
				dist.TotalSIDs++
				mu.Unlock()

				if dist.TotalSIDs%ProgressReportInterval == 0 {
					golog.Info("Processing progress", "table", table.Name, "processed", dist.TotalSIDs)
				}
				return nil
			},
			High: ConcurrentProcessingLimit, // Process records concurrently
			End: func(err error) {
				if err != nil {
					golog.Error("Stream ended with error", "table", table.Name, "error", err)
				} else {
					golog.Info("Stream completed successfully", "table", table.Name, "totalSIDs", dist.TotalSIDs)
				}
				// Signal completion
				close(done)
			},
			Error: func(err error) {
				golog.Error("Stream processing error", "table", table.Name, "error", err)
			},
		}

		// Start streaming
		if err := gostreaming.Streaming(context.Background(), &opts); err != nil {
			golog.Error("streaming error", "table", table.Name, "error", err)
		}

		// Wait for this table to complete processing
		<-done

		golog.Debug("Adding distribution for table", "table", table.Name, "totalSIDs", dist.TotalSIDs)
		distributions[table.Name] = dist
	}

	golog.Debug("Final distributions map", "distributions", distributions)
	// Print detailed debug info for each distribution
	for board, dist := range distributions {
		golog.Debug("Distribution for", "board", board)
		golog.Debug("  TotalSIDs", "totalSIDs", dist.TotalSIDs)
	}

	// Generate report
	if err := generateDistributionReport(distributions); err != nil {
		golog.Error("Failed to generate distribution report", "error", err)
		os.Exit(1)
	}
}

func generateDistributionReport(distributions map[string]*L2Distribution) error {
	// Create report file
	file, err := os.Create("l2_distribution_report.txt")
	if err != nil {
		return fmt.Errorf("failed to create report file: %v", err)
	}
	defer func() {
		if err := file.Close(); err != nil {
			golog.Error("Failed to close file", "error", err)
		}
	}()

	golog.Info("Generating distribution report", "boards", len(distributions))
	golog.Debug("Number of boards in distributions", "boards", len(distributions))

	// Write header
	_, err = fmt.Fprintf(file, "L2 Distribution Report\n")
	if err != nil {
		return fmt.Errorf("failed to write header: %v", err)
	}
	_, err = fmt.Fprintf(file, "Generated at: %s\n\n", time.Now().Format(time.RFC3339))
	if err != nil {
		return fmt.Errorf("failed to write timestamp: %v", err)
	}

	// Write distribution for each board
	for board, dist := range distributions {
		golog.Info("Writing board distribution", "board", board, "totalSIDs", dist.TotalSIDs)

		if dist.TotalSIDs == 0 {
			golog.Warn("No data for board", "board", board)
			continue
		}

		// Write board header
		_, err = fmt.Fprintf(file, "Board: %s\n", board)
		if err != nil {
			return fmt.Errorf("failed to write board header: %v", err)
		}
		_, err = fmt.Fprintf(file, "Total SIDs: %d\n", dist.TotalSIDs)
		if err != nil {
			return fmt.Errorf("failed to write total SIDs: %v", err)
		}
		_, err = fmt.Fprintf(file, "L2 Size: %d\n", len(dist.Counts))
		if err != nil {
			return fmt.Errorf("failed to write L2 size: %v", err)
		}

		// Write raw Counts map
		_, err = fmt.Fprintf(file, "Raw Counts Map:\n")
		if err != nil {
			return fmt.Errorf("failed to write raw counts header: %v", err)
		}

		// Get sorted indices
		indices := make([]int, 0, len(dist.Counts))
		for index := range dist.Counts {
			indices = append(indices, index)
		}
		sort.Ints(indices)

		// Write counts in sorted order
		for _, index := range indices {
			_, err = fmt.Fprintf(file, "%d\t%d\n", index, dist.Counts[index])
			if err != nil {
				return fmt.Errorf("failed to write raw count: %v", err)
			}
		}
		if _, err := fmt.Fprintf(file, "\n"); err != nil {
			return fmt.Errorf("failed to write newline: %v", err)
		}

		// Calculate statistics
		var min, max, sum int
		if len(dist.Counts) > 0 {
			min = dist.Counts[indices[0]]
			for _, count := range dist.Counts {
				if count < min {
					min = count
				}
				if count > max {
					max = count
				}
				sum += count
			}
		}
		avg := float64(sum) / float64(len(dist.Counts))

		// Write statistics
		_, err = fmt.Fprintf(file, "Statistics:\n")
		if err != nil {
			return fmt.Errorf("failed to write statistics header: %v", err)
		}
		_, err = fmt.Fprintf(file, "Min count: %d\n", min)
		if err != nil {
			return fmt.Errorf("failed to write min count: %v", err)
		}
		_, err = fmt.Fprintf(file, "Max count: %d\n", max)
		if err != nil {
			return fmt.Errorf("failed to write max count: %v", err)
		}
		_, err = fmt.Fprintf(file, "Average count: %.2f\n", avg)
		if err != nil {
			return fmt.Errorf("failed to write average count: %v", err)
		}
		_, err = fmt.Fprintf(file, "Standard deviation: %.2f\n\n", calculateStdDev(dist.Counts, avg))
		if err != nil {
			return fmt.Errorf("failed to write standard deviation: %v", err)
		}
	}

	// Force flush the file
	if err := file.Sync(); err != nil {
		golog.Error("Failed to sync file", "error", err)
	}

	golog.Info("Distribution report generated successfully")
	return nil
}

func calculateStdDev(counts map[int]int, mean float64) float64 {
	var sumSquares float64
	for _, count := range counts {
		diff := float64(count) - mean
		sumSquares += diff * diff
	}
	return sumSquares / float64(len(counts))
}

func ReverseString(s string) string {
	runes := []rune(s)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}
