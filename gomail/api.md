<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# gomail

```go
import "github.com/real-rm/gomail"
```

## Index

- [Constants](<#constants>)
- [Variables](<#variables>)
- [func GetValidatedEmails\(ctx context.Context, emails \[\]string\) \(\[\]string, error\)](<#GetValidatedEmails>)
- [func LogEmail\(msg \*EmailMessage\) \(string, error\)](<#LogEmail>)
- [type BaseMailEngine](<#BaseMailEngine>)
  - [func NewBaseMailEngine\(engineName string\) \*BaseMailEngine](<#NewBaseMailEngine>)
  - [func \(e \*BaseMailEngine\) GetFromEmail\(\) string](<#BaseMailEngine.GetFromEmail>)
  - [func \(e \*BaseMailEngine\) GetName\(\) string](<#BaseMailEngine.GetName>)
  - [func \(e \*BaseMailEngine\) Init\(config map\[string\]interface\{\}\) error](<#BaseMailEngine.Init>)
  - [func \(e \*BaseMailEngine\) Send\(msg \*EmailMessage\) \(string, error\)](<#BaseMailEngine.Send>)
- [type EmailMessage](<#EmailMessage>)
- [type GomailEngine](<#GomailEngine>)
  - [func NewGomailEngine\(smtpConfig \*ServiceConfig\) \(\*GomailEngine, error\)](<#NewGomailEngine>)
  - [func \(e \*GomailEngine\) Init\(config map\[string\]interface\{\}\) error](<#GomailEngine.Init>)
  - [func \(e \*GomailEngine\) Send\(msg \*EmailMessage\) \(string, error\)](<#GomailEngine.Send>)
- [type MailEngine](<#MailEngine>)
- [type Mailer](<#Mailer>)
  - [func GetSendMailObj\(\) \(\*Mailer, error\)](<#GetSendMailObj>)
  - [func \(m \*Mailer\) CheckAndFixFrom\(engineName string, mail \*EmailMessage\) string](<#Mailer.CheckAndFixFrom>)
  - [func \(m \*Mailer\) GetEngine\(engineName string, msg \*EmailMessage\) MailEngine](<#Mailer.GetEngine>)
  - [func \(m \*Mailer\) GetName\(engineName string, msg \*EmailMessage\) string](<#Mailer.GetName>)
  - [func \(m \*Mailer\) NotifyAdmin\(msg string\) error](<#Mailer.NotifyAdmin>)
  - [func \(m \*Mailer\) SendMail\(engineName string, msg \*EmailMessage\) error](<#Mailer.SendMail>)
- [type MockMailEngine](<#MockMailEngine>)
  - [func NewMockMailEngine\(\) \*MockMailEngine](<#NewMockMailEngine>)
  - [func \(engine \*MockMailEngine\) GetMails\(subject string, email string\) \(EmailMessage, error\)](<#MockMailEngine.GetMails>)
  - [func \(e \*MockMailEngine\) Init\(config map\[string\]interface\{\}\) error](<#MockMailEngine.Init>)
  - [func \(engine \*MockMailEngine\) Reset\(\)](<#MockMailEngine.Reset>)
  - [func \(engine \*MockMailEngine\) Send\(msg \*EmailMessage\) \(string, error\)](<#MockMailEngine.Send>)
- [type RMMailEngine](<#RMMailEngine>)
  - [func NewRMMailEngine\(\) \*RMMailEngine](<#NewRMMailEngine>)
  - [func \(e \*RMMailEngine\) Init\(config map\[string\]interface\{\}\) error](<#RMMailEngine.Init>)
  - [func \(engine \*RMMailEngine\) Send\(msg \*EmailMessage\) \(string, error\)](<#RMMailEngine.Send>)
- [type SESEngine](<#SESEngine>)
  - [func NewSESEngine\(engineName string\) \*SESEngine](<#NewSESEngine>)
  - [func \(e \*SESEngine\) Init\(config map\[string\]interface\{\}\) error](<#SESEngine.Init>)
  - [func \(engine \*SESEngine\) Send\(msg \*EmailMessage\) \(string, error\)](<#SESEngine.Send>)
- [type SendmailEngine](<#SendmailEngine>)
  - [func NewSendmailEngine\(path string\) \*SendmailEngine](<#NewSendmailEngine>)
  - [func \(e \*SendmailEngine\) Init\(config map\[string\]interface\{\}\) error](<#SendmailEngine.Init>)
  - [func \(e \*SendmailEngine\) Send\(msg \*EmailMessage\) \(string, error\)](<#SendmailEngine.Send>)
- [type ServiceConfig](<#ServiceConfig>)


## Constants

<a name="DefaultFromEmail"></a>

```go
const (
    // Default values
    DefaultFromEmail = "<EMAIL>"
    DefaultFromName  = "RealMaster"
)
```

<a name="RM_MAIL"></a>

```go
const (
    RM_MAIL               = "rmMail"
    GMAIL                 = "gmail"
    SES_ENGINE            = "SES"
    SEND_MAIL             = "sendMail"
    MOCK_MAIL             = "mockmail"
    DEFAULT_FROM_NAME     = "RealMaster"
    GMAIL_HOST            = "smtp.gmail.com"
    GMAIL_PORT            = 587
    DEFAULT_SENDMAIL_PATH = "/usr/sbin/sendmail"
)
```

## Variables

<a name="ENGINES"></a>

```go
var (
    ENGINES            []string
    HOST_NAME          string
    EMAIL_ENGINE_MAP   map[string]map[string]interface{}
    VALID_EMAIL_REGEXP = regexp.MustCompile(`^[a-zA-Z0-9._%+\-]+@[a-zA-Z0-9.\-]+\.[a-zA-Z]{2,}$`)
)
```

<a name="GetValidatedEmails"></a>
## func [GetValidatedEmails](<https://github.com/real-rm/gomail/blob/main/sendMailValidate.go#L153>)

```go
func GetValidatedEmails(ctx context.Context, emails []string) ([]string, error)
```



<a name="LogEmail"></a>
## func [LogEmail](<https://github.com/real-rm/gomail/blob/main/mail.go#L274>)

```go
func LogEmail(msg *EmailMessage) (string, error)
```

LogEmail logs the email to the database

<a name="BaseMailEngine"></a>
## type [BaseMailEngine](<https://github.com/real-rm/gomail/blob/main/baseMailEngine.go#L22-L25>)

BaseMailEngine provides common functionality for all mail engines

```go
type BaseMailEngine struct {
    // contains filtered or unexported fields
}
```

<a name="NewBaseMailEngine"></a>
### func [NewBaseMailEngine](<https://github.com/real-rm/gomail/blob/main/baseMailEngine.go#L27>)

```go
func NewBaseMailEngine(engineName string) *BaseMailEngine
```



<a name="BaseMailEngine.GetFromEmail"></a>
### func \(\*BaseMailEngine\) [GetFromEmail](<https://github.com/real-rm/gomail/blob/main/baseMailEngine.go#L56>)

```go
func (e *BaseMailEngine) GetFromEmail() string
```

GetFromEmail returns the from email address

<a name="BaseMailEngine.GetName"></a>
### func \(\*BaseMailEngine\) [GetName](<https://github.com/real-rm/gomail/blob/main/baseMailEngine.go#L51>)

```go
func (e *BaseMailEngine) GetName() string
```

GetName returns the engine name

<a name="BaseMailEngine.Init"></a>
### func \(\*BaseMailEngine\) [Init](<https://github.com/real-rm/gomail/blob/main/baseMailEngine.go#L34>)

```go
func (e *BaseMailEngine) Init(config map[string]interface{}) error
```

Init initializes the base mail engine

<a name="BaseMailEngine.Send"></a>
### func \(\*BaseMailEngine\) [Send](<https://github.com/real-rm/gomail/blob/main/baseMailEngine.go#L45>)

```go
func (e *BaseMailEngine) Send(msg *EmailMessage) (string, error)
```

Send implements the MailEngine interface for BaseMailEngine

<a name="EmailMessage"></a>
## type [EmailMessage](<https://github.com/real-rm/gomail/blob/main/mail.go#L32-L50>)

EmailMessage represents an email to be sent

```go
type EmailMessage struct {
    From     string            `json:"from"`
    To       []string          `json:"to"`
    Subject  string            `json:"subject"`
    Text     string            `json:"text,omitempty"`
    HTML     string            `json:"html,omitempty"`
    ReplyTo  string            `json:"replyTo,omitempty"`
    Headers  map[string]string `json:"headers"`
    Priority bool              `json:"priority,omitempty"`
    Engine   string            `json:"-"`

    SetName        string    `json:"setName,omitempty"`
    EventType      string    `json:"eventType,omitempty"`
    LogID          string    `json:"logID,omitempty"`
    ListID         string    `json:"listID,omitempty"`
    Ts             time.Time `json:"ts,omitempty"`
    IsNeedValidate bool      `json:"-"`
    // contains filtered or unexported fields
}
```

<a name="GomailEngine"></a>
## type [GomailEngine](<https://github.com/real-rm/gomail/blob/main/gmailEngine.go#L9-L12>)



```go
type GomailEngine struct {
    BaseMailEngine
    // contains filtered or unexported fields
}
```

<a name="NewGomailEngine"></a>
### func [NewGomailEngine](<https://github.com/real-rm/gomail/blob/main/gmailEngine.go#L14>)

```go
func NewGomailEngine(smtpConfig *ServiceConfig) (*GomailEngine, error)
```



<a name="GomailEngine.Init"></a>
### func \(\*GomailEngine\) [Init](<https://github.com/real-rm/gomail/blob/main/gmailEngine.go#L28>)

```go
func (e *GomailEngine) Init(config map[string]interface{}) error
```



<a name="GomailEngine.Send"></a>
### func \(\*GomailEngine\) [Send](<https://github.com/real-rm/gomail/blob/main/gmailEngine.go#L32>)

```go
func (e *GomailEngine) Send(msg *EmailMessage) (string, error)
```



<a name="MailEngine"></a>
## type [MailEngine](<https://github.com/real-rm/gomail/blob/main/baseMailEngine.go#L16-L19>)

MailEngine defines the interface that all mail engines must implement

```go
type MailEngine interface {
    Init(config map[string]interface{}) error
    Send(msg *EmailMessage) (string, error)
}
```

<a name="Mailer"></a>
## type [Mailer](<https://github.com/real-rm/gomail/blob/main/mail.go#L53-L81>)

Mailer is the main struct for sending emails

```go
type Mailer struct {
    // Engine configurations
    Sendmail     MailEngine
    SendmailFrom string
    SendmailItvl int

    Gmail     MailEngine
    GmailFrom string
    GmailItvl int

    RMMail     MailEngine
    RMMailFrom string

    SES     MailEngine
    SESH    MailEngine
    SESFrom string
    SESItvl int

    Dummy     MailEngine
    DummyFrom string
    DummyItvl int

    // Helper functions
    ReplaceParam         func(mail *EmailMessage, data map[string]string) *EmailMessage
    HTMLToText           func(string) string
    GetEngines           func() []string
    GetFromEmailByEngine func(engineName string) string
    MockMail             MailEngine
}
```

<a name="GetSendMailObj"></a>
### func [GetSendMailObj](<https://github.com/real-rm/gomail/blob/main/mail.go#L147>)

```go
func GetSendMailObj() (*Mailer, error)
```

GetSendMailObj returns the sendMail object with all configured engines

<a name="Mailer.CheckAndFixFrom"></a>
### func \(\*Mailer\) [CheckAndFixFrom](<https://github.com/real-rm/gomail/blob/main/mail.go#L222>)

```go
func (m *Mailer) CheckAndFixFrom(engineName string, mail *EmailMessage) string
```

CheckAndFixFrom checks and fixes the from email address for the specified engine

<a name="Mailer.GetEngine"></a>
### func \(\*Mailer\) [GetEngine](<https://github.com/real-rm/gomail/blob/main/mail.go#L403>)

```go
func (m *Mailer) GetEngine(engineName string, msg *EmailMessage) MailEngine
```

GetEngine returns the engine for the specified engine name

<a name="Mailer.GetName"></a>
### func \(\*Mailer\) [GetName](<https://github.com/real-rm/gomail/blob/main/mail.go#L238>)

```go
func (m *Mailer) GetName(engineName string, msg *EmailMessage) string
```

GetName returns the name of the engine

<a name="Mailer.NotifyAdmin"></a>
### func \(\*Mailer\) [NotifyAdmin](<https://github.com/real-rm/gomail/blob/main/mail.go#L254>)

```go
func (m *Mailer) NotifyAdmin(msg string) error
```

NotifyAdmin sends a notification email to the admin

<a name="Mailer.SendMail"></a>
### func \(\*Mailer\) [SendMail](<https://github.com/real-rm/gomail/blob/main/mail.go#L296>)

```go
func (m *Mailer) SendMail(engineName string, msg *EmailMessage) error
```

SendMail sends an email using the specified engine

<a name="MockMailEngine"></a>
## type [MockMailEngine](<https://github.com/real-rm/gomail/blob/main/mockMailEngine.go#L13-L17>)

MockMailEngine is a mock mail engine for testing

```go
type MockMailEngine struct {
    BaseMailEngine
    Verbose    int
    SEND_MAILS []EmailMessage
}
```

<a name="NewMockMailEngine"></a>
### func [NewMockMailEngine](<https://github.com/real-rm/gomail/blob/main/mockMailEngine.go#L20>)

```go
func NewMockMailEngine() *MockMailEngine
```

NewMockMailEngine creates a new mock mail engine

<a name="MockMailEngine.GetMails"></a>
### func \(\*MockMailEngine\) [GetMails](<https://github.com/real-rm/gomail/blob/main/mockMailEngine.go#L61>)

```go
func (engine *MockMailEngine) GetMails(subject string, email string) (EmailMessage, error)
```

GetMails gets emails from the mock engine

<a name="MockMailEngine.Init"></a>
### func \(\*MockMailEngine\) [Init](<https://github.com/real-rm/gomail/blob/main/mockMailEngine.go#L26>)

```go
func (e *MockMailEngine) Init(config map[string]interface{}) error
```



<a name="MockMailEngine.Reset"></a>
### func \(\*MockMailEngine\) [Reset](<https://github.com/real-rm/gomail/blob/main/mockMailEngine.go#L85>)

```go
func (engine *MockMailEngine) Reset()
```



<a name="MockMailEngine.Send"></a>
### func \(\*MockMailEngine\) [Send](<https://github.com/real-rm/gomail/blob/main/mockMailEngine.go#L36>)

```go
func (engine *MockMailEngine) Send(msg *EmailMessage) (string, error)
```

Send implements the MailEngine interface for MockMailEngine

<a name="RMMailEngine"></a>
## type [RMMailEngine](<https://github.com/real-rm/gomail/blob/main/rmMailEngine.go#L15-L19>)

RMMailEngine implements the RealMaster mail service

```go
type RMMailEngine struct {
    BaseMailEngine
    // contains filtered or unexported fields
}
```

<a name="NewRMMailEngine"></a>
### func [NewRMMailEngine](<https://github.com/real-rm/gomail/blob/main/rmMailEngine.go#L22>)

```go
func NewRMMailEngine() *RMMailEngine
```

NewRMMailEngine creates a new RealMaster mail engine

<a name="RMMailEngine.Init"></a>
### func \(\*RMMailEngine\) [Init](<https://github.com/real-rm/gomail/blob/main/rmMailEngine.go#L30>)

```go
func (e *RMMailEngine) Init(config map[string]interface{}) error
```

Init implements the MailEngine interface for RMMailEngine

<a name="RMMailEngine.Send"></a>
### func \(\*RMMailEngine\) [Send](<https://github.com/real-rm/gomail/blob/main/rmMailEngine.go#L44>)

```go
func (engine *RMMailEngine) Send(msg *EmailMessage) (string, error)
```

Send implements the MailEngine interface for RMMailEngine

<a name="SESEngine"></a>
## type [SESEngine](<https://github.com/real-rm/gomail/blob/main/sesEngine.go#L14-L19>)

SESEngine implements the AWS SES mail service

```go
type SESEngine struct {
    BaseMailEngine
    // contains filtered or unexported fields
}
```

<a name="NewSESEngine"></a>
### func [NewSESEngine](<https://github.com/real-rm/gomail/blob/main/sesEngine.go#L22>)

```go
func NewSESEngine(engineName string) *SESEngine
```

NewSESEngine creates a new AWS SES mail engine

<a name="SESEngine.Init"></a>
### func \(\*SESEngine\) [Init](<https://github.com/real-rm/gomail/blob/main/sesEngine.go#L29>)

```go
func (e *SESEngine) Init(config map[string]interface{}) error
```

Init implements the MailEngine interface for SESEngine

<a name="SESEngine.Send"></a>
### func \(\*SESEngine\) [Send](<https://github.com/real-rm/gomail/blob/main/sesEngine.go#L65>)

```go
func (engine *SESEngine) Send(msg *EmailMessage) (string, error)
```

Send implements the MailEngine interface for SESEngine

<a name="SendmailEngine"></a>
## type [SendmailEngine](<https://github.com/real-rm/gomail/blob/main/sendmailEngine.go#L12-L14>)



```go
type SendmailEngine struct {
    Path string
}
```

<a name="NewSendmailEngine"></a>
### func [NewSendmailEngine](<https://github.com/real-rm/gomail/blob/main/sendmailEngine.go#L16>)

```go
func NewSendmailEngine(path string) *SendmailEngine
```



<a name="SendmailEngine.Init"></a>
### func \(\*SendmailEngine\) [Init](<https://github.com/real-rm/gomail/blob/main/sendmailEngine.go#L25>)

```go
func (e *SendmailEngine) Init(config map[string]interface{}) error
```



<a name="SendmailEngine.Send"></a>
### func \(\*SendmailEngine\) [Send](<https://github.com/real-rm/gomail/blob/main/sendmailEngine.go#L29>)

```go
func (e *SendmailEngine) Send(msg *EmailMessage) (string, error)
```



<a name="ServiceConfig"></a>
## type [ServiceConfig](<https://github.com/real-rm/gomail/blob/main/mail_utils.go#L208-L214>)

ServiceConfig holds the extracted service config

```go
type ServiceConfig struct {
    Service string
    User    string
    Pass    string
    Host    string
    Port    int
}
```

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)
