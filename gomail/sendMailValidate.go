package gomail

import (
	"context"
	"log"
	"net"
	"strings"
	"sync"
	"time"

	gohelper "github.com/real-rm/gohelper"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	emailMXFailedCollection = "emailMXFailed"
	expireDuration          = 30 * 24 * time.Hour // 30 days
)

var (
	emailWhitelistCol  *gomongo.MongoCollection
	emailMXFailedCol   *gomongo.MongoCollection
	whiteListDomainMap = make(map[string]bson.M)
	whiteListMutex     sync.RWMutex
	whiteListOnce      sync.Once
)

func ensureWhitelistLoaded(ctx context.Context) {
	whiteListOnce.Do(func() { loadEmailWhitelist(ctx) })
}

func loadEmailWhitelist(ctx context.Context) {
	emailWhitelistCol = gomongo.PreDefColl("emailWhitelist")
	emailMXFailedCol = gomongo.PreDefColl(emailMXFailedCollection)
	if emailWhitelistCol != nil {
		if emailWhiteLists, err := emailWhitelistCol.FindToArray(ctx, bson.M{}); err == nil {
			whiteListMutex.Lock()
			for _, emailWhiteList := range emailWhiteLists {
				whiteListDomainMap[emailWhiteList["_id"].(string)] = emailWhiteList
			}
			whiteListMutex.Unlock()
		} else {
			golog.Error("Failed to load email whitelist", "error", err)
		}
	}
}

func isDomainInWhitelist(domain string) bool {
	whiteListMutex.RLock()
	ret := whiteListDomainMap[domain]
	whiteListMutex.RUnlock()

	if ret == nil {
		return false
	}
	if ret["_expTime"] != nil {
		var expTime time.Time
		switch v := ret["_expTime"].(type) {
		case primitive.DateTime:
			expTime = v.Time()
		case time.Time:
			expTime = v
		default:
			return false
		}
		if time.Now().Before(expTime) {
			return true
		}
		whiteListMutex.Lock()
		delete(whiteListDomainMap, domain)
		whiteListMutex.Unlock()
		return false
	}
	return true
}

func addDomainToWhitelist(ctx context.Context, domain string) error {
	expTime := time.Now().Add(expireDuration)
	doc := bson.M{"_id": domain, "_expTime": expTime}
	if emailWhitelistCol == nil {
		loadEmailWhitelist(ctx)
	}

	_, err := emailWhitelistCol.UpdateOne(ctx, bson.M{"_id": domain}, bson.M{"$set": doc}, options.Update().SetUpsert(true))
	if err == nil {
		whiteListMutex.Lock()
		whiteListDomainMap[domain] = doc
		whiteListMutex.Unlock()
	} else {
		golog.Error("Failed to add domain to whitelist", "error", err)
	}
	return err
}

func resolveMX(domain string) bool {
	mxRecords, err := net.LookupMX(domain)
	if err != nil {
		golog.Error("MX lookup failed for domain", "domain", domain, "error", err)
		return false
	}

	for _, mx := range mxRecords {
		if mx.Host != "localhost" {
			return true
		}
	}
	return false
}

func validateEmail(ctx context.Context, email string) bool {
	ensureWhitelistLoaded(ctx)
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return false
	}
	domain := parts[1]

	if isDomainInWhitelist(domain) {
		return true
	}
	// check email format
	validFormat := gohelper.IsEmail(email)
	if !validFormat {
		return false
	}
	// check mx
	validMX := resolveMX(domain)

	if validFormat && validMX {
		if err := addDomainToWhitelist(ctx, domain); err == nil {
			return true
		}
	}

	// Insert to failed collection
	if emailMXFailedCol != nil {
		_, err := emailMXFailedCol.InsertOne(ctx, bson.M{
			"email":    email,
			"_expTime": time.Now().Add(expireDuration),
		})
		if err != nil {
			log.Printf("Failed to insert invalid email: %s, err: %v", email, err)
		}
	}

	return false
}

func GetValidatedEmails(ctx context.Context, emails []string) ([]string, error) {
	if len(emails) == 0 {
		return []string{}, nil
	}
	ensureWhitelistLoaded(ctx)
	validatedEmails := []string{}
	for _, email := range emails {
		if validateEmail(ctx, email) {
			validatedEmails = append(validatedEmails, email)
		}
	}
	return validatedEmails, nil
}
