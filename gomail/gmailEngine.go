package gomail

import (
	"fmt"

	"gopkg.in/gomail.v2"
)

type GomailEngine struct {
	BaseMailEngine
	dialer *gomail.Dialer
}

func NewGomailEngine(smtpConfig *ServiceConfig) (*GomailEngine, error) {
	if smtpConfig == nil {
		return nil, fmt.Errorf("SMTP config is nil")
	}
	if smtpConfig.Host == "" || smtpConfig.Port == 0 {
		return nil, fmt.<PERSON>("SMTP host and port must be set")
	}
	dialer := gomail.NewDialer(smtpConfig.Host, smtpConfig.Port, smtpConfig.User, smtpConfig.Pass)
	return &GomailEngine{
		BaseMailEngine: BaseMailEngine{engineName: "gmail"},
		dialer:         dialer,
	}, nil
}

func (e *GomailEngine) Init(config map[string]interface{}) error {
	return e.BaseMailEngine.Init(config)
}

func (e *GomailEngine) Send(msg *EmailMessage) (string, error) {
	m := gomail.NewMessage()
	m.SetHeader("From", msg.From)
	m.SetHeader("To", msg.To...)
	m.SetHeader("Subject", msg.Subject)
	if msg.ReplyTo != "" {
		m.SetHeader("Reply-To", msg.ReplyTo)
	}
	for k, v := range msg.Headers {
		m.SetHeader(k, v)
	}
	m.SetBody("text/plain", msg.Text)
	if msg.HTML != "" {
		m.AddAlternative("text/html", msg.HTML)
	}

	// 添加附件支持
	if msg.AttachmentList != nil {
		for _, attachment := range msg.AttachmentList {
			if attachment.FilePath != "" {
				if attachment.DisplayName != "" {
					m.Attach(attachment.FilePath, gomail.Rename(attachment.DisplayName))
				} else {
					m.Attach(attachment.FilePath)
				}
			}
		}
	}

	err := e.dialer.DialAndSend(m)
	if err != nil {
		return "", err
	}
	return "success", nil
}
