package gomail

import (
	"fmt"
	"os/exec"
	"strings"

	"github.com/jaytaylor/html2text"
	golog "github.com/real-rm/golog"
)

type SendmailEngine struct {
	Path string
}

func NewSendmailEngine(path string) *SendmailEngine {
	if path == "" {
		golog.Fatal("path is required")
	}
	return &SendmailEngine{
		Path: path,
	}
}

func (e *SendmailEngine) Init(config map[string]interface{}) error {
	return nil
}

func (e *SendmailEngine) Send(msg *EmailMessage) (string, error) {
	var textBody string
	if msg.HTML != "" {
		var err error
		textBody, err = html2text.FromString(msg.HTML, html2text.Options{PrettyTables: true})
		if err != nil {
			return "", fmt.Errorf("failed to convert HTML to text: %v", err)
		}
	} else {
		textBody = msg.Text
	}

	// Construct email message content (MIME multipart alternative)
	message := fmt.Sprintf(
		"From: %s\nTo: %s\nSubject: %s\nMIME-Version: 1.0\nContent-Type: multipart/alternative; boundary=boundary123\n\n"+
			"--boundary123\nContent-Type: text/plain; charset=UTF-8\n\n%s\n\n"+
			"--boundary123\nContent-Type: text/html; charset=UTF-8\n\n%s\n\n--boundary123--",
		msg.From, strings.Join(msg.To, ", "), msg.Subject, textBody, msg.HTML,
	)

	cmd := exec.Command(e.Path, "-t")
	stdin, err := cmd.StdinPipe()
	if err != nil {
		return "", fmt.Errorf("failed to create stdin pipe: %v", err)
	}

	if err := cmd.Start(); err != nil {
		return "", fmt.Errorf("failed to start command: %v", err)
	}

	_, err = stdin.Write([]byte(message))
	if err != nil {
		return "", fmt.Errorf("failed to write message to stdin: %v", err)
	}
	if err := stdin.Close(); err != nil {
		return "", fmt.Errorf("failed to close stdin: %v", err)
	}

	if err := cmd.Wait(); err != nil {
		return "", fmt.Errorf("failed to wait for command: %v", err)
	}

	return "", nil
}
