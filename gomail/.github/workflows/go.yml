name: Go CI

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]
  workflow_dispatch:

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        token: ${{ secrets.WORKFLOW_TOKEN_GITHUB }}  # 使用 GitHub Token 进行 HTTPS 克隆

    - name: Configure Git for private repos
      run: |
        git config --global url."https://${{ secrets.WORKFLOW_TOKEN_GITHUB }}@github.com/".insteadOf "https://github.com/"
  
    - name: Configure GOPRIVATE
      run: echo "GOPRIVATE=github.com/real-rm/*" >> $GITHUB_ENV

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: 'stable'

    - name: Run golangci-lint
      uses: golangci/golangci-lint-action@v6
      with:
        version: latest
        args: --timeout=5m

  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        token: ${{ secrets.WORKFLOW_TOKEN_GITHUB }}

    - name: Configure Git for private repos
      run: |
        git config --global url."https://${{ secrets.WORKFLOW_TOKEN_GITHUB }}@github.com/".insteadOf "https://github.com/"

    - name: Configure GOPRIVATE
      run: echo "GOPRIVATE=github.com/real-rm/*" >> $GITHUB_ENV

    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: 'stable'

    - name: Start MongoDB (Replica Set)
      run: |
        docker run --rm -d --name mongo \
          -p 27017:27017 \
          mongo:latest \
          --replSet rs0 --bind_ip_all

    - name: Wait for MongoDB to start
      run: |
        echo "Waiting for MongoDB to become available..."
        for i in {1..20}; do
          nc -z localhost 27017 && echo "MongoDB is up!" && break
          echo "Waiting..."
          sleep 1
        done

    - name: Initialize Mongo Replica Set
      run: |
        docker run --rm --network host mongo:latest \
          mongosh --host localhost --eval 'rs.initiate({_id: "rs0", members: [{_id: 0, host: "localhost:27017"}]})'

    - name: Test MongoDB
      run: |
        docker run --rm --network host mongo:latest \
          mongosh --host localhost --eval 'db.testDocs.insertOne({ok: true})'
   
    - name: Tidy up Go modules
      run: go mod tidy

    - name: Build
      run: go build -v ./...

    - name: revise local.test.ini for test
      run: |
        sed -i 's|***********************************************************************************************' local.test.ini
        cat local.test.ini

    - name: Run Tests
      run: go test -v ./...

    - name: Stop MongoDB
      run: |
        docker rm -f mongo
