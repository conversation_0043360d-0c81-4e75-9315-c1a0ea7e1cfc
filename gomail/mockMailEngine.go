package gomail

import (
	"fmt"
	"sort"
	"time"

	gohelper "github.com/real-rm/gohelper"
	golog "github.com/real-rm/golog"
)

// MockMailEngine is a mock mail engine for testing
type MockMailEngine struct {
	BaseMailEngine
	Verbose    int
	SEND_MAILS []EmailMessage
}

// NewMockMailEngine creates a new mock mail engine
func NewMockMailEngine() *MockMailEngine {
	return &MockMailEngine{
		BaseMailEngine: BaseMailEngine{engineName: "mockmail"},
	}
}

func (e *MockMailEngine) Init(config map[string]interface{}) error {
	e.Verbose = 0
	if verbose, ok := config["verbose"].(int); ok && verbose != 0 {
		e.Verbose = verbose
	}
	e.SEND_MAILS = []EmailMessage{}
	return e.BaseMailEngine.Init(config)
}

// Send implements the MailEngine interface for MockMailEngine
func (engine *MockMailEngine) Send(msg *EmailMessage) (string, error) {
	if engine.Verbose > 0 {
		golog.Info("Mock email sent", "to", msg.To, "subject", msg.Subject)
	}

	to := msg.To
	if len(to) == 0 {
		golog.Error("Invalid format of mail.to", "to", msg.To)
		return "", fmt.Errorf("invalid to address format")
	}

	newMail := EmailMessage{
		Subject: msg.Subject,
		HTML:    msg.HTML,
		From:    msg.From,
		Text:    msg.Text,
		To:      to,
		Ts:      time.Now(),
	}

	engine.SEND_MAILS = append(engine.SEND_MAILS, newMail)
	return "", nil
}

// GetMails gets emails from the mock engine
func (engine *MockMailEngine) GetMails(subject string, email string) (EmailMessage, error) {
	if engine.Verbose > 0 {
		golog.Info("mock get mails,subject:", subject, "email:", email)
	}
	var result []EmailMessage
	for _, mail := range engine.SEND_MAILS {
		subjectMatch := subject == "" || mail.Subject == subject
		emailMatch := email == "" || gohelper.Contains(mail.To, email)
		if subjectMatch && emailMatch {
			result = append(result, mail)
		}
	}

	// Sort by timestamp
	sort.Slice(result, func(i, j int) bool {
		return result[i].Ts.Before(result[j].Ts)
	})

	if len(result) > 0 {
		return result[0], nil
	}
	return EmailMessage{}, fmt.Errorf("no mail found")
}

func (engine *MockMailEngine) Reset() {
	engine.SEND_MAILS = []EmailMessage{}
}
