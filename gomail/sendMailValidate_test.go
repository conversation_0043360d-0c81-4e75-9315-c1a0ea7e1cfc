package gomail

import (
	"context"
	"os"
	"path/filepath"
	"testing"
	"time"

	gohelper "github.com/real-rm/gohelper"
	gomongo "github.com/real-rm/gomongo"
	"github.com/stretchr/testify/assert"
	"go.mongodb.org/mongo-driver/bson"
)

func setupTestSendMailValidate(t *testing.T) func() {
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	configPath, err := filepath.Abs(filepath.Join(currentDir, "local.test.ini"))
	if err != nil {
		t.Fatalf("Failed to get absolute path: %v", err)
	}
	if _, err := os.Stat(configPath); err != nil {
		t.Fatalf("Config file not found at %s: %v", configPath, err)
	}
	gohelper.SetRmbaseFileCfg(configPath)
	// Initialize test environment
	if err := gohelper.SetupTestEnv(gohelper.TestOptions{
		UseEnvConfig: true,
	}); err != nil {
		t.Fatalf("Failed to setup test environment: %v", err)
	}
	cleanup := gohelper.CleanupTestEnv
	return func() {
		cleanup()
		emailWhitelistCol = gomongo.PreDefColl("emailWhitelist")
		if _, err := emailWhitelistCol.DeleteMany(context.Background(), bson.M{}); err != nil {
			t.Fatalf("Failed to delete email whitelist: %v", err)
		}
		emailWhitelistCol = nil
		emailMXFailedCol = nil
	}
}

func TestLoadEmailWhitelist(t *testing.T) {
	cleanup := setupTestSendMailValidate(t)
	defer cleanup()

	// Insert test data
	now := time.Now()
	emailWhitelistCol = gomongo.PreDefColl("emailWhitelist")
	_, err := emailWhitelistCol.InsertMany(context.Background(), []interface{}{
		bson.M{"_id": "example.com", "_expTime": now.Add(24 * time.Hour)},
		bson.M{"_id": "example2.com"},
		bson.M{"_id": "test.com", "_expTime": now.Add(-24 * time.Hour)}, // Expired
	})
	assert.NoError(t, err)

	// Test
	loadEmailWhitelist(context.Background())
	// Verify
	assert.True(t, isDomainInWhitelist("example.com"))
	assert.True(t, isDomainInWhitelist("example2.com"))
	assert.False(t, isDomainInWhitelist("test.com"))
}

func TestIsDomainInWhitelist(t *testing.T) {
	cleanup := setupTestSendMailValidate(t)
	defer cleanup()

	// Setup test data
	now := time.Now()
	whiteListDomainMap = make(map[string]bson.M)
	whiteListDomainMap["example.com"] = bson.M{"_expTime": now.Add(24 * time.Hour)}
	whiteListDomainMap["expired.com"] = bson.M{"_expTime": now.Add(-24 * time.Hour)}

	// Test cases
	tests := []struct {
		name     string
		domain   string
		expected bool
	}{
		{"Valid domain", "example.com", true},
		{"Expired domain", "expired.com", false},
		{"Non-existent domain", "nonexistent.com", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isDomainInWhitelist(tt.domain)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestAddDomainToWhitelist(t *testing.T) {
	cleanup := setupTestSendMailValidate(t)
	defer cleanup()

	domain := "exampleNew.com"
	err := addDomainToWhitelist(context.Background(), domain)
	assert.NoError(t, err)

	// Verify the domain was added
	var result bson.M
	err = emailWhitelistCol.FindOne(context.Background(), bson.M{"_id": domain}).Decode(&result)
	assert.NoError(t, err)
	assert.NotNil(t, result["_expTime"])
}

func TestResolveMX(t *testing.T) {
	tests := []struct {
		name     string
		domain   string
		expected bool
	}{
		{"Valid domain", "gmail.com", true},
		{"Invalid domain", "nonexistentdomain123456789.com", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := resolveMX(tt.domain)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestValidateEmail(t *testing.T) {
	cleanup := setupTestSendMailValidate(t)
	defer cleanup()
	emailWhitelistCol = gomongo.PreDefColl("emailWhitelist")
	// Insert a valid domain into whitelist
	_, err := emailWhitelistCol.InsertOne(context.Background(), bson.M{
		"_id":      "whitelisted.com",
		"_expTime": time.Now().Add(24 * time.Hour),
	})
	assert.NoError(t, err)

	tests := []struct {
		name     string
		email    string
		expected bool
	}{
		{"Valid email", "<EMAIL>", true},
		{"Whitelisted domain", "<EMAIL>", true},
		{"Invalid format", "invalid-email", false},
		{"Invalid domain", "<EMAIL>", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validateEmail(context.Background(), tt.email)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestGetValidatedEmails(t *testing.T) {
	cleanup := setupTestSendMailValidate(t)
	defer cleanup()
	emailWhitelistCol = gomongo.PreDefColl("emailWhitelist")

	// Insert a valid domain into whitelist
	_, err := emailWhitelistCol.InsertOne(context.Background(), bson.M{
		"_id":      "whitelisted.com",
		"_expTime": time.Now().Add(24 * time.Hour),
	})
	assert.NoError(t, err)

	tests := []struct {
		name          string
		emails        []string
		expectedCount int
	}{
		{"Empty list", []string{}, 0},
		{"Valid emails", []string{"<EMAIL>", "<EMAIL>"}, 2},
		{"Mixed emails", []string{"<EMAIL>", "invalid-email", "<EMAIL>"}, 1},
		{"Whitelisted domain", []string{"<EMAIL>"}, 1},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := GetValidatedEmails(context.Background(), tt.emails)
			assert.NoError(t, err)
			assert.Len(t, result, tt.expectedCount)
		})
	}
}
