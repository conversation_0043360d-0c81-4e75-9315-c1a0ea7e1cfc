package gomail

import (
	"context"
	"fmt"
	"os"
	"regexp"
	"time"

	"github.com/jaytaylor/html2text"
	goconfig "github.com/real-rm/goconfig"
	gohelper "github.com/real-rm/gohelper"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func init() {
	ENGINES = []string{RM_MAIL, GMAIL, SES_ENGINE, SEND_MAIL, MOCK_MAIL}
	hostname, err := os.Hostname()
	if err != nil {
		panic(err)
	}
	// Contain only ASCII letters (a-z, A-Z), numbers (0-9), underscores (_), or dashes (-).
	// Contain 256 characters or fewer.
	re := regexp.MustCompile(`[^-_a-zA-Z0-9]`)
	HOST_NAME = re.ReplaceAllString(hostname, "-")
}

// Attachment represents an email attachment
type Attachment struct {
	FilePath    string `json:"filePath"`    // 文件路径
	DisplayName string `json:"displayName"` // 显示名称
}

// EmailMessage represents an email to be sent
type EmailMessage struct {
	From                string            `json:"from"`
	To                  []string          `json:"to"`
	Subject             string            `json:"subject"`
	Text                string            `json:"text,omitempty"`
	HTML                string            `json:"html,omitempty"`
	ReplyTo             string            `json:"replyTo,omitempty"`
	Headers             map[string]string `json:"headers"`
	Priority            bool              `json:"priority,omitempty"`
	Engine              string            `json:"-"`
	rpd                 bool              `json:"-"` // Replaced Parameter Done
	SetName             string            `json:"setName,omitempty"`
	EventType           string            `json:"eventType,omitempty"`
	LogID               string            `json:"logID,omitempty"`
	ListID              string            `json:"listID,omitempty"`
	Ts                  time.Time         `json:"ts,omitempty"`
	IsNeedValidate      bool              `json:"-"`
	mailEngineListIndex *int              `json:"-"`
	AttachmentList      []Attachment      `json:"attachmentList,omitempty"` // 新的附件列表结构
}

// Mailer is the main struct for sending emails
type Mailer struct {
	// Engine configurations
	Sendmail     MailEngine
	SendmailFrom string
	SendmailItvl int

	Gmail     MailEngine
	GmailFrom string
	GmailItvl int

	RMMail     MailEngine
	RMMailFrom string

	SES     MailEngine
	SESH    MailEngine
	SESFrom string
	SESItvl int

	Dummy     MailEngine
	DummyFrom string
	DummyItvl int

	// Helper functions
	ReplaceParam         func(mail *EmailMessage, data map[string]string) *EmailMessage
	HTMLToText           func(string) string
	GetEngines           func() []string
	GetFromEmailByEngine func(engineName string) string
	MockMail             MailEngine
}

// initMailEngines initializes all mail engines
func initMailEngines() (gmailT, sesT, sesHighTransporter, rmMailT MailEngine, err error) {
	// Initialize Gmail Transport
	smtpCfg, err := getServiceConfig()
	if err != nil {
		golog.Fatal("Error getting service config:", err)
	}
	if smtpCfg == nil {
		smtpCfg = &ServiceConfig{
			Service: "Gmail",
			User:    "<EMAIL>",
			Pass:    "RealMasterCOM***",
			Host:    GMAIL_HOST,
			Port:    GMAIL_PORT,
		}
	}
	gmailT, err = NewGomailEngine(smtpCfg)
	if err != nil {
		golog.Error("Error creating Gmail Transport:", err)
		return
	}

	// Initialize SES Transport
	sesCfg := goconfig.Config("mailEngine.ses").(map[string]interface{})
	if sesCfg == nil {
		sesCfg = goconfig.Config("mailEngine.sesL").(map[string]interface{})
	}
	sesEngine := NewSESEngine("ses")
	if err = sesEngine.Init(sesCfg); err != nil {
		golog.Error("Error creating SES Transport:", err)
		return
	}
	sesT = sesEngine

	// Initialize SES High Transporter
	seshCfgRaw := goconfig.Config("mailEngine.sesH")
	if seshCfgRaw == nil {
		seshCfgRaw = goconfig.Config("mailEngine.ses")
	}
	seshCfg, _ := seshCfgRaw.(map[string]interface{})
	if seshCfg == nil {
		// fallback in case sesH existed but was the wrong type
		seshCfg, _ = goconfig.Config("mailEngine.ses").(map[string]interface{})
	}
	sesHighEngine := NewSESEngine("sesH")
	if err = sesHighEngine.Init(seshCfg); err != nil {
		golog.Error("Error creating SES High Transporter:", err)
		return
	}
	sesHighTransporter = sesHighEngine

	// Initialize RM Mail Transport
	rmMailCfg := goconfig.Config("mailEngine.rmMail").(map[string]interface{})
	rmMailEngine := NewRMMailEngine()
	if err = rmMailEngine.Init(rmMailCfg); err != nil {
		golog.Error("Error creating RM Mail Transport:", err)
		return
	}
	rmMailT = rmMailEngine

	return
}

// GetSendMailObj returns the sendMail object with all configured engines
func GetSendMailObj() (*Mailer, error) {
	defaultFrom, SESFrom, SESHFrom, gmailFrom, sendmailFrom, rmMailFrom := getDefaultFromEmails()
	initMailEngineList()
	initEmailEngineMap(defaultFrom, SESFrom, SESHFrom, gmailFrom, sendmailFrom, rmMailFrom)

	gmailT, sesT, sesHighTransporter, rmMailT, err := initMailEngines()
	if err != nil {
		return nil, err
	}

	// Initialize dummy mail
	dummyEngine := NewBaseMailEngine("dummy")
	sendmailEngine := NewSendmailEngine(DEFAULT_SENDMAIL_PATH)
	if err := sendmailEngine.Init(nil); err != nil {
		golog.Error("Error creating Sendmail Transport:", err)
		return nil, err
	}

	// Initialize mock mail
	mockMailCfgRaw := goconfig.Config("mailEngine.mockMail")
	mockMailCfg, ok := mockMailCfgRaw.(map[string]interface{})
	if !ok {
		golog.Warn("mailEngine.mockMail not configured correctly, fallback to empty map")
		mockMailCfg = make(map[string]interface{})
	}
	mockEngine := NewMockMailEngine()
	if err := mockEngine.Init(mockMailCfg); err != nil {
		golog.Error("Error creating mock mail Transport:", err)
		return nil, err
	}

	mailer := &Mailer{
		Sendmail:     sendmailEngine,
		SendmailFrom: sendmailFrom,
		SendmailItvl: 300,
		Gmail:        gmailT,
		GmailFrom:    gmailFrom,
		GmailItvl:    250,
		RMMail:       rmMailT,
		RMMailFrom:   rmMailFrom,
		SES:          sesT,
		SESH:         sesHighTransporter,
		SESFrom:      SESFrom,
		SESItvl:      100,
		Dummy:        dummyEngine,
		DummyFrom:    "<EMAIL>",
		DummyItvl:    0,
		ReplaceParam: replaceParam,
		HTMLToText: func(html string) string {
			textBody, err := html2text.FromString(html, html2text.Options{PrettyTables: true})
			if err != nil {
				golog.Error("Error converting HTML to text:", err)
				return ""
			}
			return textBody
		},
		GetEngines: func() []string {
			return ENGINES
		},
		MockMail: mockEngine,
		GetFromEmailByEngine: func(engineName string) string {
			if engineName == "" {
				engineName = "SES"
			}
			fromEmail, ok := EMAIL_ENGINE_MAP[engineName]["email"].(string)
			if !ok {
				return DefaultFromEmail
			}
			return fromEmail
		},
	}
	return mailer, nil
}

// CheckAndFixFrom checks and fixes the from email address for the specified engine
func (m *Mailer) CheckAndFixFrom(engineName string, mail *EmailMessage) string {
	mailFrom := mail.From
	engineCfg, ok := EMAIL_ENGINE_MAP[engineName]

	if !ok {
		return mailFrom
	}
	if re, ok := engineCfg["regex"].(*regexp.Regexp); ok && !re.MatchString(mailFrom) {
		if email, ok := engineCfg["email"].(string); ok && email != "" {
			return email
		}
	}
	return mailFrom
}

// GetName returns the name of the engine
func (m *Mailer) GetName(engineName string, msg *EmailMessage) string {
	var engineNameReturned string
	if engineName == "SES" {
		engineNameReturned = "SES"
		if msg.Priority {
			engineNameReturned = "SESH"
		}
		return engineNameReturned
	}
	if msg.Engine != "" {
		return msg.Engine
	}
	return ""
}

// NotifyAdmin sends a notification email to the admin
func (m *Mailer) NotifyAdmin(msg string) error {
	engineName := getEngineName()
	adminEmail := getAdminEmail()
	replyTo := getReplyToEmail()

	email := &EmailMessage{
		Engine:   engineName,
		Priority: true,
		From:     m.GetFromEmailByEngine(string(engineName)),
		To:       []string{adminEmail},
		ReplyTo:  "RealMaster Technology Inc.<" + replyTo + ">",
		Subject:  "RealMaster APP System Notify",
		Text:     msg,
		HTML:     msg,
	}

	return m.SendMail(engineName, email)
}

// LogEmail logs the email to the database
func LogEmail(msg *EmailMessage) (string, error) {
	sendMailLogCol := gomongo.PreDefColl("mailLog")
	if sendMailLogCol == nil {
		return "", fmt.Errorf("not found mailLog collection")
	}
	set := bson.M{
		"timestamp": time.Now(),
		"metadata":  msg,
	}
	insertResult, err := sendMailLogCol.InsertOne(context.Background(), set)
	if err != nil {
		golog.Error("Error logging email:", err)
		return "", fmt.Errorf("error logging email: %v", err)
	}
	id, ok := insertResult.InsertedID.(primitive.ObjectID)
	if !ok {
		return "", fmt.Errorf("inserted ID is not an ObjectID")
	}
	return id.Hex(), nil
}

// SendMail sends an email using the specified engine
func (m *Mailer) SendMail(engineName string, msg *EmailMessage) error {
	if goconfig.Config("mailEngine.mockMail.mock") == true {
		if goconfig.Config("mockMail.verbose") == 1 {
			golog.Info("Mock mail enabled, sending", "email", msg)
		}
		engineName = MOCK_MAIL
	}
	// Validate message
	if msg == nil {
		return fmt.Errorf("no mail")
	}
	if len(msg.To) == 0 {
		return fmt.Errorf("no to %v", msg)
	}
	if msg.Subject == "" {
		return fmt.Errorf("no title %v", msg)
	}
	if !gohelper.Contains(ENGINES, engineName) {
		return fmt.Errorf("not supported engine %s", engineName)
	}

	// Check email format and validate
	var newTo []string
	for _, to := range msg.To {
		if isIgnoredToAddr(to) {
			golog.Error("Ignored Email:", to)
			return nil
		}
		newTo = append(newTo, to)
	}
	msg.To = newTo
	if msg.IsNeedValidate {
		validatedEmails, err := GetValidatedEmails(context.Background(), msg.To)
		if err != nil {
			return fmt.Errorf("failed to get validated emails: %v", err)
		}
		if len(validatedEmails) == 0 {
			golog.Warn("no valid email: %v", msg.To)
			return fmt.Errorf("no valid email: %v", msg.To)
		}
		msg.To = validatedEmails
	}
	if len(msg.From) == 0 {
		msg.From = m.GetFromEmailByEngine(msg.Engine)
	} else {
		msg.From = m.CheckAndFixFrom(msg.Engine, msg)
	}
	var transport MailEngine
	if msg.mailEngineListIndex != nil && engineName != MOCK_MAIL {
		if len(mailEngineList) == 0 {
			return fmt.Errorf("mailEngineList is empty")
		}
		mailEngineConfig := mailEngineList[*msg.mailEngineListIndex]
		if mailEngineConfig == nil {
			return fmt.Errorf("error: not found mailEngine %d", *msg.mailEngineListIndex)
		}
		re := regexp.MustCompile(`<.*?>`)
		from := DefaultFromEmail
		if fromEmail, ok := mailEngineConfig["email"].(string); ok && fromEmail != "" {
			from = fromEmail
		}

		if re.MatchString(msg.From) {
			msg.From = re.ReplaceAllString(msg.From, "<"+from+">")
		} else {
			msg.From = from
		}
		engineName = mailEngineConfig["engine"].(string)
		switch engineName {
		case SES_ENGINE:
			transport = m.SES
		case RM_MAIL:
			transport = m.RMMail
		default:
			return fmt.Errorf("error: not supported engine %s", engineName)
		}
	} else {
		transport = m.GetEngine(engineName, msg)
		if transport == nil {
			golog.Error("error: not supported engine %s", engineName)
			return fmt.Errorf("not supported engine %s", engineName)
		}
	}

	// set display name
	re := regexp.MustCompile(`^.+<`)
	if !re.MatchString(msg.From) {
		re := regexp.MustCompile(`[<>]`)
		cleanedEmail := re.ReplaceAllString(msg.From, "")
		displayName, err := goconfig.ConfigString("contact.defaultEmailFromName")
		if err != nil || displayName == "" {
			displayName = DEFAULT_FROM_NAME
		}
		msg.From = displayName + " <" + cleanedEmail + ">"
	}
	golog.Info("Use mail engine", "engine", engineName, "from", msg.From)
	logID, err := LogEmail(msg)
	if err != nil {
		golog.Error("error logging email:", "error", err)
	}
	msg.LogID = logID
	// Send email
	_, err = transport.Send(msg)
	return err
}

// GetEngine returns the engine for the specified engine name
func (m *Mailer) GetEngine(engineName string, msg *EmailMessage) MailEngine {
	var engine MailEngine
	switch engineName {
	case RM_MAIL:
		engine = m.RMMail
	case GMAIL:
		engine = m.Gmail
	case SES_ENGINE:
		engine = m.SES
		if msg.Priority {
			engine = m.SESH
		}
	case MOCK_MAIL:
		engine = m.MockMail
	default:
		return nil
	}
	return engine
}
