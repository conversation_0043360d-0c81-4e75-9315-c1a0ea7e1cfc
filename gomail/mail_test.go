package gomail

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"testing"
	"time"

	goconfig "github.com/real-rm/goconfig"
	gohelper "github.com/real-rm/gohelper"
	gomongo "github.com/real-rm/gomongo"
	"github.com/stretchr/testify/assert"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	EDM_TEST_EMAIL = "<EMAIL>"
)

var (
	SOFT_BOUNCE_BODY_MOCK = bson.M{
		"eventType": "Bounce",
		"bounce": bson.M{
			"feedbackId":    "0100018d4b4069d9-e021dfde-dfd2-4a63-94eb-4c3aed594e46-000000",
			"bounceType":    "Transient",
			"bounceSubType": "General",
			"bouncedRecipients": []bson.M{
				{
					"emailAddress":   "<EMAIL>",
					"action":         "failed",
					"status":         "5.7.1",
					"diagnosticCode": "smtp; 550 5.7.1 Service unavailable, MailFrom domain is listed in Spamhaus. To request removal from this list see https://www.spamhaus.org/query/lookup/ (S8002) [VI1EUR06FT056.eop-eur06.prod.protection.outlook.com 2024-01-27T14:08:48.927Z 08DC1F199CAB0CD2]",
				},
			},
			"timestamp":    "2024-01-27T14:08:49.248Z",
			"reportingMTA": "dns; a43-186.smtp-out.amazonses.com",
		},
		"mail": bson.M{
			"timestamp":        "2024-01-27T14:08:47.589Z",
			"source":           "RealMaster<<EMAIL>>",
			"sourceArn":        "arn:aws:ses:us-east-1:************:identity/realmaster.ca",
			"sendingAccountId": "************",
			"messageId":        "0100018d4b4063e5-c6ba0790-0ad6-4837-b694-09f292bbffe4-000000",
			"destination":      []string{"<EMAIL>"},
			"headersTruncated": false,
			"headers": []bson.M{
				{
					"name":  "From",
					"value": "RealMaster <<EMAIL>>",
				},
				{
					"name":  "Reply-To",
					"value": "<EMAIL>",
				},
				{
					"name":  "To",
					"value": "<EMAIL>",
				},
				{
					"name":  "Subject",
					"value": "房大师注册确认",
				},
				{
					"name":  "MIME-Version",
					"value": "1.0",
				},
				{
					"name":  "Content-Type",
					"value": "multipart/alternative;  boundary=\"----=_Part_1224931_1807568700.*************\"",
				},
			},
			"commonHeaders": bson.M{
				"from":      []string{"\"RealMaster\" <<EMAIL>>"},
				"replyTo":   []string{"<EMAIL>"},
				"to":        []string{"<EMAIL>"},
				"messageId": "0100018d4b4063e5-c6ba0790-0ad6-4837-b694-09f292bbffe4-000000",
				"subject":   "房大师注册确认",
			},
			"tags": bson.M{
				"ses:source-tls-version": []string{"TLSv1.3"},
				"ses:operation":          []string{"SendEmail"},
				"ses:configuration-set":  []string{"RM_SES"},
				"ses:recipient-isp":      []string{"Hotmail"},
				"ses:source-ip":          []string{"***************"},
				"ses:from-domain":        []string{"realmaster.ca"},
				"Event":                  []string{"Registration"},
				"ses:sender-identity":    []string{"realmaster.ca"},
				"ses:caller-identity":    []string{"root"},
				"HostName":               []string{"shf3"},
			},
		},
	}

	META_DATA = bson.M{
		"_trans":              "smtp",
		"engine":              "SES",
		"eventType":           "Registration",
		"from":                "RealMaster<<EMAIL>>",
		"html":                "<head><meta name=\"X-RM-Host\" content=\"MacdeMac-Pro-local\"></head><body> <table width=\"100%\" class=\"single default invert\" bgcolor=\"#f5f5f5\" cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr> <td align=\"center\" style=\"padding: 0 20px\"> <table cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr> <td width=\"520\" style=\"padding: 32px 0 0px\" class=\"singleCell\"> <table width=\"100%\" class=\"center\" style=\"text-align: center\" cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr> <td align=\"center\" class=\"icon\"> <table width=\"inherit\" style=\"text-align: center\" cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr> <td style=\"padding: 0px 0px 5px\"><img src=\"https://www.realmaster.cn/img/download/RealMaster-web-logo.png\" width=\"162\" height=\"46\" style=\"display: inline-block;-webkit-border-radius: 1px;-moz-border-radius: 11px;border-radius: 1px;border: 0px solid #ffffff\"> </td> </tr> </tbody> </table> </td> </tr> <tr> <td align=\"center\"> <div style=\"width: 0;height: 0;border-left-width: 11px;border-left-color: transparent;border-left-style: solid;border-right-width: 11px;border-right-color: transparent;border-right-style: solid;border-bottom-width: 11px;border-bottom-color: #ffffff;border-bottom-style: solid\"> </div> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> <table width=\"100%\" class=\"single default\" bgcolor=\"#ffffff\" cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr> <td align=\"center\" style=\"padding: 0 20px\"> <table cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr> <td width=\"400\" style=\"padding: 29px 0\" class=\"singleCell\"> <table width=\"100%\" class=\"left\" style=\"text-align: left\" cellpadding=\"0\" cellspacing=\"0\"> <tbody>  <tr> <td style=\"border-top-style: solid;padding: 19px 0 8px;border-top-width: 1px;border-top-color: #e0e0e0;color: #2d394e;font-family: sqmarket, Helvetica, sans-serif;font-weight: 300;font-size: 28px;line-height: 32px;\" class=\"h1\" align=\"center\">验证码</td> </tr> <tr> <td style=\"color: #5f6a7c;font-size: 16px;line-height: 24px;font-family: sqmarket, Helvetica, sans-serif;font-weight: normal\" class=\"p\" align=\"center\">将在30分钟后过期</td> </tr> <tr> <td style=\"padding-right:0px;padding-top:20px;padding-bottom:20px;padding-left:0px;width:5px;background-color:#ffffff;font-family:Arial, Helvetica, sans-serif;font-size:14px;font-style:normal;font-weight:normal;line-height:24px;text-align:center;\"> <div style=\"color: blue;font-family: sqmarket, Helvetica, sans-serif;font-weight: 300;font-size: 28px;line-height: 10px;padding: 0px 0 8px;text-align:center;\"> 2077156</div> </td> </tr> <tr> <td style=\"padding-right:0px;padding-top:20px;padding-bottom:20px;padding-left:0px;font-family:Arial, Helvetica, sans-serif;line-height:24px;color:#5f6a7c;font-size:16px;font-family: sqmarket, Helvetica, sans-serif; text-align:center\"> 或者点击下面按钮认证                         </td> </tr> <tr> <td style=\"text-align: center;\"> <a href=\"http://www.test/zh-cn/verifyEmail/0aa5a61d579a951d5dd39dcb59723b4318d43c8c\" style=\"text-decoration: none;font-size:14px; padding: 10px 30px; color: #fff;background-color: #5cb85c;\" rel=\"noopener\" target=\"_blank\">确认电子邮件地址</a><br><br> </td> </tr>  <tr> <td style=\"border-top-color: #e0e0e0;padding: 18px 0;border-top-style: solid;border-top-width: 1px;font-size: 14px;line-height: 22px;font-family: sqmarket, Helvetica, sans-serif;font-weight: normal;color: #5f6a7d\" class=\"p\">如果您有任何疑问，请联系我们。<br> </td> </tr> <tr> <td style=\"border-top-width: 0px;border-top-color: #e0e0e0;border-top-style: solid;padding: 6px 0 12px\"> <table class=\"tabDat\" style=\"text-align: left\" cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr style=\"vertical-align: top\"> <td colspan=\"2\" style=\"padding-bottom: 12px\" valign=\"top\"></td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> <table width=\"100%\" class=\"single default invert\" bgcolor=\"#f5f5f5\" cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr> <td align=\"center\" style=\"padding: 0 20px\"> <table cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr> <td width=\"520\" style=\"padding: 32px 0 0px\" class=\"singleCell\"> <table width=\"100%\" class=\"center\" style=\"text-align: center\" cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr> <td align=\"center\" class=\"icon\"> <table width=\"100%\" class=\"center\" style=\"text-align: center\" cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr> <td style=\"font-size: 14px;line-height: 22px;color: #2d394e;font-family: sqmarket-medium, Helvetica, sans-serif;font-weight: bold\" class=\"h3\">联系我们</td> </tr> <tr> <td style=\"font-size: 14px;line-height: 22px;font-family: sqmarket, Helvetica, sans-serif;font-weight: normal;color: #5f6a7d;padding: 0px 0 16px\" class=\"p\"> <br><a href=\"mailto:<EMAIL>\" style=\"color: #5f6a7d;text-decoration: none;font-family: sqmarket-medium, Helvetica, sans-serif;font-weight: bold\" target=\"_blank\"><EMAIL></a><br><span style=\"border-bottom:1px dashed #ccc;z-index:1\" t=\"7\" onclick=\"return false;\" data=\"************\">************</span><br> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table></body>",
		"isNeedValidate":      true,
		"mailEngineListIndex": 0,
		"priority":            true,
		"replyTo":             "<<EMAIL>>",
		"sbj":                 "RealMaster Registration Confirmation",
		"subject":             fmt.Sprintf("房大师注册确认%d", time.Now().UnixNano()),
		"text":                "<head><meta name=\"X-RM-Host\" content=\"MacdeMac-Pro-local\"></head>请点击下面链接验证您的电子邮件：\nhttp://www.test/zh-cn/verifyEmail/0aa5a61d579a951d5dd39dcb59723b4318d43c8c",
		"to":                  []string{"<EMAIL>"},
	}
)

func setupTestMail(t *testing.T) func() {
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	configPath, err := filepath.Abs(filepath.Join(currentDir, "local.test.ini"))
	if err != nil {
		t.Fatalf("Failed to get absolute path: %v", err)
	}
	if _, err := os.Stat(configPath); err != nil {
		t.Fatalf("Config file not found at %s: %v", configPath, err)
	}
	gohelper.SetRmbaseFileCfg(configPath)
	// Initialize test environment
	if err := gohelper.SetupTestEnv(gohelper.TestOptions{
		UseEnvConfig: true,
	}); err != nil {
		t.Fatalf("Failed to setup test environment: %v", err)
	}

	// Initialize mailEngineList
	mailEngineList = []map[string]interface{}{
		{
			"engine": "SES",
			"email":  "<EMAIL>",
		},
	}

	cleanup := gohelper.CleanupTestEnv
	return func() {
		cleanup()
	}
}

// getEmails retrieves emails for the specified address
func getEmails(email string) ([]bson.M, error) {
	if goconfig.Config("mailEngine.mockMail.mock") == true {
		// Mock mode: get emails from mock engine
		mailer, err := GetSendMailObj()
		if err != nil {
			return nil, err
		}
		if mockEngine, ok := mailer.MockMail.(interface {
			GetMails(string, string) (EmailMessage, error)
		}); ok {
			mail, err := mockEngine.GetMails("", email)
			if err != nil {
				return nil, err
			}
			// Convert EmailMessage to bson.M
			emailDoc := bson.M{
				"from": []bson.M{
					{
						"address": mail.From,
						"name":    "RealMaster",
					},
				},
				"subject":   mail.Subject,
				"text":      mail.Text,
				"html":      mail.HTML,
				"to":        mail.To,
				"timestamp": primitive.NewDateTimeFromTime(mail.Ts),
			}
			return []bson.M{emailDoc}, nil
		}
		return nil, fmt.Errorf("mock engine does not support GetMails")
	}

	// Real mode: get emails from mail server
	url := fmt.Sprintf("https://ml1.realmaster.cc/list/%s", email)
	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to get emails: %v", err)
	}
	defer func() {
		if err := resp.Body.Close(); err != nil {
			fmt.Printf("failed to close response body: %v", err)
		}
	}()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	var result struct {
		R []bson.M `json:"r"`
	}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("failed to parse response: %v", err)
	}

	return result.R, nil
}

func TestMailer(t *testing.T) {
	// Note: To run this test with a longer timeout, use:
	// go test -v -timeout 2m -run TestMailer
	cleanup := setupTestMail(t)
	defer cleanup()

	mailer, err := GetSendMailObj()
	assert.NoError(t, err)
	assert.NotNil(t, mailer)

	// Test domain name configurations
	tests := []struct {
		name       string
		engineName string
		from       string
		priority   bool
		expected   struct {
			address  string
			name     string
			fullFrom string
		}
	}{
		{
			name:       "SES engine <NAME_EMAIL>",
			engineName: SES_ENGINE,
			from:       "<EMAIL>",
			expected: struct {
				address  string
				name     string
				fullFrom string
			}{
				address:  "<EMAIL>",
				name:     "RealMaster",
				fullFrom: "RealMaster<<EMAIL>>",
			},
		},
		{
			name:       "SES engine with priority <NAME_EMAIL>",
			engineName: SES_ENGINE,
			from:       "<EMAIL>",
			priority:   true,
			expected: struct {
				address  string
				name     string
				fullFrom string
			}{
				address:  "<EMAIL>",
				name:     "RealMaster",
				fullFrom: "RealMaster<<EMAIL>>",
			},
		},
		{
			name:       "RMMail engine <NAME_EMAIL>",
			engineName: RM_MAIL,
			from:       "<EMAIL>",
			expected: struct {
				address  string
				name     string
				fullFrom string
			}{
				address:  "<EMAIL>",
				name:     "RealMaster",
				fullFrom: "RealMaster<<EMAIL>>",
			},
		},
		{
			name:       "SES engine with mailEngineListIndex <NAME_EMAIL>",
			engineName: SES_ENGINE,
			from:       "<EMAIL>",
			priority:   true,
			expected: struct {
				address  string
				name     string
				fullFrom string
			}{
				address:  "<EMAIL>",
				name:     "RealMaster",
				fullFrom: "RealMaster<<EMAIL>>",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			msg := &EmailMessage{
				From:      tt.from,
				To:        []string{EDM_TEST_EMAIL},
				Subject:   "Test Subject",
				Text:      "Test Body",
				Engine:    tt.engineName,
				Priority:  tt.priority,
				EventType: "Registration",
			}

			// Set mailEngineListIndex for the specific test case
			if tt.name == "SES engine with mailEngineListIndex <NAME_EMAIL>" {
				index := 0
				msg.mailEngineListIndex = &index
			}

			err := mailer.SendMail(tt.engineName, msg)
			assert.NoError(t, err)

			// Wait for 5 seconds
			time.Sleep(5 * time.Second)
			if tt.name == "RMMail engine <NAME_EMAIL>" {
				time.Sleep(5 * time.Second)
			}

			// Get and verify emails
			emails, err := getEmails(EDM_TEST_EMAIL)
			assert.NoError(t, err)
			assert.NotEmpty(t, emails)

			// Get the latest email
			var mailIndex int
			if goconfig.Config("mailEngine.mockMail.mock") == true {
				// Mock mode: emails are sorted in ascending order
				mailIndex = len(emails) - 1
				assert.Equal(t, tt.expected.fullFrom, emails[mailIndex]["from"])
			} else {
				// Real mode: emails are sorted in descending order
				mailIndex = 0
				fromList, ok := emails[mailIndex]["from"].([]interface{})
				assert.True(t, ok, "from should be a slice")
				assert.NotEmpty(t, fromList, "from list should not be empty")
				from, ok := fromList[0].(map[string]interface{})
				assert.True(t, ok, "from item should be a map")
				assert.Equal(t, tt.expected.address, from["address"])
				assert.Equal(t, tt.expected.name, from["name"])
			}
			assert.Equal(t, "Test Subject", emails[mailIndex]["subject"])
		})
	}

	// Test admin notification
	t.Run("Admin notification", func(t *testing.T) {
		err := mailer.NotifyAdmin("Test notification")
		assert.NoError(t, err)

		// Verify admin notification was logged
		mailLogCol := gomongo.PreDefColl("mailLog")
		var result bson.M
		err = mailLogCol.FindOne(context.Background(), bson.M{"metadata.subject": "RealMaster APP System Notify"}).Decode(&result)
		assert.NoError(t, err)
		assert.NotNil(t, result)
	})
}

// testSoftBounce is a helper function for testing soft bounce scenarios
// func testSoftBounce(t *testing.T, eventType string, shouldSend bool) {
// 	// Create a copy of META_DATA with dynamic subject
// 	metaData := bson.M{}
// 	for k, v := range META_DATA {
// 		metaData[k] = v
// 	}

// 	// Set different subject based on event type
// 	var newSubject string
// 	switch eventType {
// 	case "Registration":
// 		newSubject = fmt.Sprintf("房大师注册确认%d", time.Now().UnixNano())
// 	case "EdmNotify":
// 		newSubject = fmt.Sprintf("TEST_EDM_COLL_NAME%d", time.Now().UnixNano())
// 	default:
// 		newSubject = fmt.Sprintf("TEST_NO_COLL_NAME%d", time.Now().UnixNano())
// 	}
// 	metaData["subject"] = newSubject
// 	metaData["sbj"] = newSubject

// 	// Create a copy of SOFT_BOUNCE_BODY_MOCK
// 	bounceBody := bson.M{}
// 	for k, v := range SOFT_BOUNCE_BODY_MOCK {
// 		bounceBody[k] = v
// 	}
// 	bounceBody["mail"] = bson.M{}
// 	for k, v := range SOFT_BOUNCE_BODY_MOCK["mail"].(bson.M) {
// 		bounceBody["mail"].(bson.M)[k] = v
// 	}
// 	bounceBody["mail"].(bson.M)["tags"] = bson.M{}
// 	for k, v := range SOFT_BOUNCE_BODY_MOCK["mail"].(bson.M)["tags"].(bson.M) {
// 		bounceBody["mail"].(bson.M)["tags"].(bson.M)[k] = v
// 	}

// 	// Insert test email log
// 	mailLogCol := gomongo.PreDefColl("mailLog")
// 	insertData := bson.M{
// 		"timestamp": time.Now(),
// 		"metadata":  metaData,
// 	}
// 	result, err := mailLogCol.InsertOne(context.Background(), insertData)
// 	assert.NoError(t, err)
// 	assert.NotNil(t, result.InsertedID)

// 	// Set LogId and Event in bounceBody
// 	bounceBody["mail"].(bson.M)["tags"].(bson.M)["LogId"] = []string{result.InsertedID.(primitive.ObjectID).Hex()}
// 	if eventType != "" {
// 		bounceBody["mail"].(bson.M)["tags"].(bson.M)["eventType"] = []string{eventType}
// 	}

// 	// Create HTTP request with bounce notification
// 	jsonBody, err := json.Marshal(bounceBody)
// 	assert.NoError(t, err)

// 	// Create request for bounce notification
// 	req, err := http.NewRequest("POST", "http://localhost/aws/sns", bytes.NewReader(jsonBody))
// 	assert.NoError(t, err)
// 	req.Header.Set("Accept", "application/json")
// 	req.Header.Set("x-amz-sns-message-type", "Notification")
// 	req.Header.Set("x-amz-sns-message-id", "5784e873-d797-5e85-a9d7-b65cc7262cae")
// 	req.Header.Set("x-amz-sns-topic-arn", "arn:aws:sns:us-east-1:************:SES-Monitor")
// 	req.Header.Set("x-amz-sns-rawdelivery", "true")

// 	// Get mailer instance
// 	mailer, err := GetSendMailObj()
// 	assert.NoError(t, err)
// 	assert.NotNil(t, mailer)

// 	// Wait for 8 seconds for email delivery
// 	time.Sleep(8 * time.Second)

// 	// Verify email was logged with RMMail engine
// 	emails, err := getEmails(EDM_TEST_EMAIL)
// 	assert.NoError(t, err)

// 	if shouldSend {
// 		assert.NotEmpty(t, emails)

// 		// Find the email with matching subject
// 		var found bool
// 		for _, email := range emails {
// 			if email["subject"] == metaData["subject"] {
// 				found = true
// 				fromList, ok := email["from"].([]interface{})
// 				assert.True(t, ok, "from should be a slice")
// 				assert.NotEmpty(t, fromList, "from list should not be empty")
// 				from, ok := fromList[0].(map[string]interface{})
// 				assert.True(t, ok, "from item should be a map")
// 				assert.Equal(t, "<EMAIL>", from["address"])
// 				assert.Equal(t, "RealMaster", from["name"])
// 				break
// 			}
// 		}
// 		assert.True(t, found, "email with matching subject should be found")
// 	} else {
// 		isExists := false
// 		if len(emails) > 0 {
// 			for _, email := range emails {
// 				if email["subject"] == metaData["subject"] {
// 					isExists = true
// 					break
// 				}
// 			}
// 		}
// 		assert.False(t, isExists, "email with matching subject should not be found")
// 	}
// }

// TODO: this test need sns_receiver to run
// func TestSoftBounce(t *testing.T) {
// 	// Note: To run this test with a longer timeout, use:
// 	// go test -v -timeout 1m -run TestSoftBounce
// 	cleanup := setupTestMail(t)
// 	defer cleanup()

// 	t.Run("should use rmMail engine sendMail when eventType is Registration", func(t *testing.T) {
// 		testSoftBounce(t, "Registration", true)
// 	})

// 	t.Run("should not use rmMail engine sendMail when no eventType", func(t *testing.T) {
// 		testSoftBounce(t, "", false)
// 	})

// 	t.Run("should not use rmMail engine sendMail when eventType is EdmNotify", func(t *testing.T) {
// 		testSoftBounce(t, "EdmNotify", false)
// 	})
// }
