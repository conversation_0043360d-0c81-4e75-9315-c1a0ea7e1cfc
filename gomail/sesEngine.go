package gomail

import (
	"fmt"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/awserr"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/ses"
)

// SESEngine implements the AWS SES mail service
type SESEngine struct {
	BaseMailEngine
	url     string
	setName string
	client  *ses.SES
}

// NewSESEngine creates a new AWS SES mail engine
func NewSESEngine(engineName string) *SESEngine {
	return &SESEngine{
		BaseMailEngine: BaseMailEngine{engineName: engineName},
	}
}

// Init implements the MailEngine interface for SESEngine
func (e *SESEngine) Init(config map[string]interface{}) error {
	if err := e.BaseMailEngine.Init(config); err != nil {
		return err
	}
	if url, ok := config["url"].(string); ok {
		e.url = url
	}

	accessKeyID, ok1 := config["accessKeyId"].(string)
	secretAccessKey, ok2 := config["secretAccessKey"].(string)
	region, ok3 := config["region"].(string)
	if !ok1 || !ok2 || !ok3 {
		return fmt.Errorf("missing or invalid AWS credentials/region in config")
	}
	e.setName = "RM_SES"
	if setName, ok := config["setName"].(string); ok && setName != "" {
		e.setName = setName
	}

	if accessKeyID == "" || secretAccessKey == "" || region == "" {
		return fmt.Errorf("missing required parameters")
	}

	sess, err := session.NewSession(&aws.Config{
		Region:      aws.String(region),
		Credentials: credentials.NewStaticCredentials(accessKeyID, secretAccessKey, ""),
	})
	if err != nil {
		return fmt.Errorf("error creating AWS session: %v", err)
	}

	e.client = ses.New(sess)
	return nil
}

// Send implements the MailEngine interface for SESEngine
func (engine *SESEngine) Send(msg *EmailMessage) (string, error) {
	if len(msg.To) == 0 {
		return "", fmt.Errorf("no recipients specified")
	}
	// Convert recipients to AWS string pointers
	toAddresses := make([]*string, len(msg.To))
	for i, addr := range msg.To {
		toAddresses[i] = aws.String(addr)
	}

	var setName string
	if msg.SetName != "" {
		setName = msg.SetName
	} else {
		setName = engine.setName
	}
	var html string
	if msg.HTML == "" && msg.Text == "" {
		return "", fmt.Errorf("email content cannot be empty")
	}
	html = msg.HTML
	if html == "" {
		html = msg.Text
	}

	text := msg.Text

	// Create email input
	input := &ses.SendEmailInput{
		ConfigurationSetName: aws.String(setName),
		Destination: &ses.Destination{
			ToAddresses: toAddresses,
		},
		Message: &ses.Message{
			Body: &ses.Body{
				Html: &ses.Content{
					Charset: aws.String("UTF-8"),
					Data:    aws.String(html),
				},
				Text: &ses.Content{
					Charset: aws.String("UTF-8"),
					Data:    aws.String(text),
				},
			},
			Subject: &ses.Content{
				Charset: aws.String("UTF-8"),
				Data:    aws.String(msg.Subject),
			},
		},
		Source: aws.String(msg.From),
		Tags: []*ses.MessageTag{
			{
				Name:  aws.String("X-RM-Host"),
				Value: aws.String(HOST_NAME),
			},
		},
	}

	if msg.EventType != "" {
		input.Tags = append(input.Tags, &ses.MessageTag{
			Name:  aws.String("Event"),
			Value: aws.String(msg.EventType),
		})
	}

	if msg.LogID != "" {
		input.Tags = append(input.Tags, &ses.MessageTag{
			Name:  aws.String("LogId"),
			Value: aws.String(msg.LogID),
		})
	}

	// Add reply-to if specified
	if msg.ReplyTo != "" {
		input.ReplyToAddresses = []*string{aws.String(msg.ReplyTo)}
	}

	// Send the email
	data, err := engine.client.SendEmail(input)
	if err != nil {
		if aerr, ok := err.(awserr.Error); ok {
			return "", fmt.Errorf("AWS SES error: %v", aerr.Error())
		}
		return "", fmt.Errorf("error sending email via SES: %v", err)
	}

	return fmt.Sprintf("%v", data), nil
}
