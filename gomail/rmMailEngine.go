package gomail

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	golog "github.com/real-rm/golog"
)

// RMMailEngine implements the RealMaster mail service
type RMMailEngine struct {
	BaseMailEngine
	client *http.Client
	url    string
}

// NewRMMailEngine creates a new RealMaster mail engine
func NewRMMailEngine() *RMMailEngine {
	return &RMMailEngine{
		BaseMailEngine: BaseMailEngine{engineName: "rmMail"},
		client:         &http.Client{Timeout: 10 * time.Second},
	}
}

// Init implements the MailEngine interface for RMMailEngine
func (e *RMMailEngine) Init(config map[string]interface{}) error {
	if err := e.BaseMailEngine.Init(config); err != nil {
		return err
	}
	if url, ok := config["url"].(string); ok {
		e.url = url
	} else {
		golog.Error("Missing required parameter: url")
		return fmt.Errorf("missing required parameter: url")
	}
	return nil
}

// Send implements the MailEngine interface for RMMailEngine
func (engine *RMMailEngine) Send(msg *EmailMessage) (string, error) {
	to := msg.To
	if len(to) == 0 {
		return "", fmt.Errorf("no recipients specified")
	}

	defaultDomain := "realmaster.cc"
	listID := fmt.Sprintf("<%s>", defaultDomain)
	if msg.ListID != "" {
		listID = fmt.Sprintf("<%s.%s>", msg.ListID, defaultDomain)
	} else {
		golog.Warn("rmMail send email has no list-id,content:", "msg", msg)
	}
	// Marshal the message to JSON
	jsonData, err := json.Marshal(msg)
	if err != nil {
		return "", fmt.Errorf("error marshaling email message: %v", err)
	}

	// Create the request
	req, err := http.NewRequest("POST", engine.url, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("error creating request: %v", err)
	}

	// Set required headers for the HTTP request
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-RM-Auth", "RM")
	req.Header.Set("X-RM-Host", HOST_NAME)
	req.Header.Set("List-ID", listID)

	// Send the request
	resp, err := engine.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("error sending email: %v", err)
	}
	defer func() {
		if err := resp.Body.Close(); err != nil {
			golog.Error("failed to close response body", "error", err)
		}
	}()

	// Check response status
	if resp.StatusCode != http.StatusOK {
		var respBody []byte
		if resp.Body != nil {
			respBody, err = io.ReadAll(resp.Body)
			if err != nil {
				return "", fmt.Errorf("email service returned non-200 status code: %d, failed to read response body: %v", resp.StatusCode, err)
			}
		}
		return "", fmt.Errorf("email service returned non-200 status code: %d, body: %s", resp.StatusCode, string(respBody))
	}

	return fmt.Sprintf("%v", resp), nil
}
