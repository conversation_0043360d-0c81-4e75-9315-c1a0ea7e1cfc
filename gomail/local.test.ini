[contact]
defaultEmail = "<EMAIL>"
defaultEmailFromName = "RealMaster"
defaultRTEmail = "<EMAIL>"
defaultReciveEmail = "<EMAIL>"
devEmails = "<EMAIL>"


[dbs]
verbose = 3

[dbs.tmp]
uri = "***************************************************************"
#uri = "mongodb://localhost:27017/test1"
[golog]
dir = "/tmp/test_logs"
level = "info"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "text"

[preDefColls.emailMXFailed]
collName = "email_mx_failed"
dbName = "tmp"

[preDefColls.emailWhitelist]
collName = "email_whitelist"
dbName = "tmp"

[preDefColls.mailLog]
collName = "mail_log"
dbName = "tmp"

  [preDefColls.mailLog.options]
  expireAfterSeconds = 2_592_000

    [preDefColls.mailLog.options.timeseries]
    metaField = "metadata"
    timeField = "timestamp"

[mailEngine]
mailEngine = "mockmail"
mailEngine2 = "rmMail"

  [mailEngine.gmail]
  defaultEmail = ""

    [mailEngine.gmail.auth]
    pass = "dummy"
    user = "<EMAIL>"

  [mailEngine.mockMail]
  mock = false
  verbose = 3

  [mailEngine.rmMail]
  defaultEmail = "<EMAIL>"
  url = "https://ml1.realmaster.cc/send"

  [mailEngine.sendGrid]
  apiKey = "*********************************************************************"
  fromEmail = "<EMAIL>"

  [mailEngine.sendmail]
  defaultEmail = ""

  [mailEngine.ses]
  accessKeyId = "********************"
  defaultEmail = "<EMAIL>"
  region = "us-east-1"
  secretAccessKey = "Uogp6eMqmbIhxB43mqCYPJYBT9x3vnQZwrawvz6W"
  url = "http://934.230.229.12:8088/ses.php1"

  [mailEngine.smtp]
  service = "Gmail"

[[mailEngineList]]
accessKeyId = "********************"
email = "<EMAIL>"
engine = "SES"
region = "us-east-2"
secretAccessKey = "NqYJDdVhIMdvVqlJnmBSIBsbHKCEQLlkw91/v4rA"
url = "http://934.230.229.12:8088/ses.php1"

[[mailEngineList]]
email = "<EMAIL>"
engine = "rmMail"
url = "https://ml1.realmaster.cc/send"
