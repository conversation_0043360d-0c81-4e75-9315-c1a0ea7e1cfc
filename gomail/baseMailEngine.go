package gomail

import (
	"fmt"

	golog "github.com/real-rm/golog"
)

const (
	// Default values
	DefaultFromEmail = "<EMAIL>"
	DefaultFromName  = "RealMaster"
)

// MailEngine defines the interface that all mail engines must implement
type MailEngine interface {
	Init(config map[string]interface{}) error
	Send(msg *EmailMessage) (string, error)
}

// BaseMailEngine provides common functionality for all mail engines
type BaseMailEngine struct {
	fromEmail  string
	engineName string
}

func NewBaseMailEngine(engineName string) *BaseMailEngine {
	return &BaseMailEngine{
		engineName: engineName,
	}
}

// Init initializes the base mail engine
func (e *BaseMailEngine) Init(config map[string]interface{}) error {
	if defaultEmail, ok := config["defaultEmail"].(string); ok {
		e.fromEmail = defaultEmail
	} else {
		e.fromEmail = DefaultFromEmail
		golog.Warn("defaultEmail is not set for engine %s, use %s", e.engineName, DefaultFromEmail)
	}
	return nil
}

// Send implements the MailEngine interface for BaseMailEngine
func (e *BaseMailEngine) Send(msg *EmailMessage) (string, error) {
	golog.Info("BaseMailEngine send email", "msg", msg)
	return "", fmt.Errorf("BaseMailEngine.Send is a stub method, actual engine must implement this method")
}

// GetName returns the engine name
func (e *BaseMailEngine) GetName() string {
	return e.engineName
}

// GetFromEmail returns the from email address
func (e *BaseMailEngine) GetFromEmail() string {
	return e.fromEmail
}
