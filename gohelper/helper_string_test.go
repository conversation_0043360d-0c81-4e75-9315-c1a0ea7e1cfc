package gohelper

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func TestIsEmail(t *testing.T) {
	tests := []struct {
		name     string
		email    string
		expected bool
	}{
		{
			name:     "Valid email with letters",
			email:    "<EMAIL>",
			expected: true,
		},
		{
			name:     "Valid email with numbers",
			email:    "<EMAIL>",
			expected: true,
		},
		{
			name:     "Valid email with dots",
			email:    "<EMAIL>",
			expected: true,
		},
		{
			name:     "Valid email with hyphens",
			email:    "<EMAIL>",
			expected: true,
		},
		{
			name:     "Valid email with plus sign",
			email:    "<EMAIL>",
			expected: true,
		},
		{
			name:     "Invalid email - missing @",
			email:    "testexample.com",
			expected: false,
		},
		{
			name:     "Invalid email - missing domain",
			email:    "test@",
			expected: false,
		},
		{
			name:     "Invalid email - missing local part",
			email:    "@example.com",
			expected: false,
		},
		{
			name:     "Invalid email - empty string",
			email:    "",
			expected: false,
		},
		{
			name:     "Invalid email - special characters",
			email:    "test!@example.com",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsEmail(tt.email)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Helper function to create compressed test data
func createCompressedTestData(data interface{}) (primitive.Binary, error) {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return primitive.Binary{}, err
	}

	var buf bytes.Buffer
	gzipWriter := gzip.NewWriter(&buf)
	if _, err := gzipWriter.Write(jsonData); err != nil {
		return primitive.Binary{}, err
	}
	if err := gzipWriter.Close(); err != nil {
		return primitive.Binary{}, err
	}

	return primitive.Binary{
		Subtype: 2,
		Data:    buf.Bytes(),
	}, nil
}

func TestDecompressPhoUrls(t *testing.T) {
	// Create test data - a JSON array with 3 media items
	testData := []map[string]interface{}{
		{"id": 1, "url": "http://example.com/1.jpg", "type": "photo"},
		{"id": 2, "url": "http://example.com/2.jpg", "type": "photo"},
		{"id": 3, "url": "http://example.com/3.pdf", "type": "document"},
	}

	// Compress to BinData
	binData, err := createCompressedTestData(testData)
	require.NoError(t, err)

	// Test the function
	mediaArray, err := DecompressPhoUrls(binData)
	require.NoError(t, err)
	assert.Equal(t, 3, len(mediaArray))

	// Verify the content (JSON unmarshaling converts numbers to float64)
	assert.Equal(t, float64(1), mediaArray[0].(map[string]interface{})["id"])
	assert.Equal(t, testData[0]["url"], mediaArray[0].(map[string]interface{})["url"])
	assert.Equal(t, testData[0]["type"], mediaArray[0].(map[string]interface{})["type"])
}

func TestDecompressPhoUrls_EmptyArray(t *testing.T) {
	// Test with empty array
	testData := []interface{}{}
	binData, err := createCompressedTestData(testData)
	require.NoError(t, err)

	mediaArray, err := DecompressPhoUrls(binData)
	require.NoError(t, err)
	assert.Equal(t, 0, len(mediaArray))
	assert.Empty(t, mediaArray)
}

func TestDecompressPhoUrls_InvalidType(t *testing.T) {
	// Test with invalid type
	_, err := DecompressPhoUrls("not a binary")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "phoUrls is not a primitive.Binary type")
}

func TestDecompressPhoUrls_InvalidGzip(t *testing.T) {
	// Test with invalid gzip data
	binData := primitive.Binary{
		Subtype: 2,
		Data:    []byte("invalid gzip data"),
	}

	_, err := DecompressPhoUrls(binData)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to create gzip reader")
}

func TestDecompressPhoUrls_InvalidJSON(t *testing.T) {
	// Create invalid JSON data and compress it
	invalidJSON := "invalid json"
	binData, err := createCompressedTestData(invalidJSON)
	require.NoError(t, err)

	_, err = DecompressPhoUrls(binData)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to parse decompressed JSON")
}
