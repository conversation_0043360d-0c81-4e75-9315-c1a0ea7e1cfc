package gohelper

import (
	"time"
)

const TimeFormat = "2006-01-02 15:04:05"

// TimeToStr converts a time.Time object to a string.
// Uses local timezone to match the local filesystem operations.
func TimeToStr(t time.Time) string {
	return t.Format(TimeFormat)
}

// StrToTime converts a string to a time.Time object.
// Uses local timezone to match the local filesystem operations.
func StrToTime(s string) (time.Time, error) {
	return time.ParseInLocation(TimeFormat, s, time.Local)
}

// DateToTime converts a date integer in the format YYYYMMDD to a time.Time object.
// For example, 20150101 becomes January 1, 2015 00:00:00.
// Uses local timezone to match the local filesystem operations.
func DateToTime(dateInt int) time.Time {
	if dateInt <= 0 {
		return time.Time{}
	}
	year := dateInt / 10000
	month := (dateInt % 10000) / 100
	day := dateInt % 100

	if month < 1 || month > 12 || day < 1 || day > 31 {
		return time.Time{}
	}
	return time.Date(year, time.Month(month), day, 0, 0, 0, 0, time.Local)
}

// TimeToDateInt converts a time.Time object to a date integer in the format YYYYMMDD.
// For example, January 1, 2015 becomes 20150101.
func TimeToDateInt(timestamp time.Time) int {
	return timestamp.Year()*10000 + int(timestamp.Month())*100 + timestamp.Day()
}
