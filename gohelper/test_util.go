package gohelper

import (
	"fmt"
	"os"
	"testing"

	goconfig "github.com/real-rm/goconfig"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
)

var (
	tmpDir     string
	configFile string
)

// TestOptions defines options for test environment setup
type TestOptions struct {
	// UseEnvConfig indicates whether to use existing environment config file
	// If true, will use RMBASE_FILE_CFG environment variable
	// If false, will create temporary config file
	UseEnvConfig bool
}

// DefaultTestOptions returns default test options
func DefaultTestOptions() TestOptions {
	return TestOptions{
		UseEnvConfig: true,
	}
}

// SetupTestEnv initializes the test environment including config and logging
func SetupTestEnv(opts TestOptions) error {
	if opts.UseEnvConfig {
		// Use existing environment config
		if os.Getenv("RMBASE_FILE_CFG") == "" {
			return fmt.Errorf("RMBASE_FILE_CFG environment variable is not set")
		}
	} else {
		// Create temporary directory for logs
		var err error
		tmpDir, err = os.MkdirTemp("", "logs-*")
		if err != nil {
			return fmt.Errorf("failed to create temp dir: %v", err)
		}

		// Create a temporary config file
		content := `
[golog]
dir = "` + tmpDir + `"
level = "debug"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "text"

[dbs.tmp]
uri = "***************************************************************"
#uri = "mongodb://localhost:27017/tmp"
`
		tmpfile, err := os.CreateTemp(tmpDir, "config-*.toml")
		if err != nil {
			if err := os.RemoveAll(tmpDir); err != nil {
				golog.Error("failed to remove temp dir: %v", err)
			}
			return fmt.Errorf("failed to create temp file: %v", err)
		}
		configFile = tmpfile.Name()

		if _, err := tmpfile.Write([]byte(content)); err != nil {
			if err := os.Remove(configFile); err != nil {
				golog.Error("failed to remove config file: %v", err)
			}
			if err := os.RemoveAll(tmpDir); err != nil {
				golog.Error("failed to remove tmpDie: %v", err)
			}
			return fmt.Errorf("failed to write config: %v", err)
		}
		if err := tmpfile.Close(); err != nil {
			if err := os.Remove(configFile); err != nil {
				golog.Error("failed to remove config file: %v", err)
			}
			if err := os.RemoveAll(tmpDir); err != nil {
				golog.Error("failed to remove tmpDie: %v", err)
			}
			return fmt.Errorf("failed to close config file: %v", err)
		}

		// Set config file path
		if err := os.Setenv("RMBASE_FILE_CFG", configFile); err != nil {
			golog.Error("failed to set RMBASE_FILE_CFG: %v", err)
		}
	}

	// Reset config state before loading
	goconfig.ResetConfig()

	// Load config immediately after setting environment variable
	if err := goconfig.LoadConfig(); err != nil {
		if !opts.UseEnvConfig {
			CleanupTestEnv()
		}
		return fmt.Errorf("failed to load config: %v", err)
	}

	// Initialize logging
	// fmt.Println("Initializing logging", )
	if err := golog.InitLog(); err != nil {
		if !opts.UseEnvConfig {
			CleanupTestEnv()
		}
		return fmt.Errorf("failed to initialize logging: %v", err)
	}

	if err := gomongo.InitMongoDB(); err != nil {
		if !opts.UseEnvConfig {
			CleanupTestEnv()
		}
		return fmt.Errorf("failed to initialize MongoDB: %v", err)
	}

	return nil
}

// CleanupTestEnv cleans up the test environment
func CleanupTestEnv() {
	if configFile != "" {
		if err := os.Remove(configFile); err != nil {
			golog.Error("failed to remove config file: %v", err)
		}
	}
	if tmpDir != "" {
		if err := os.RemoveAll(tmpDir); err != nil {
			golog.Error("failed to remove temp dir: %v", err)
		}
	}
	if err := os.Unsetenv("RMBASE_FILE_CFG"); err != nil {
		golog.Error("failed to unset RMBASE_FILE_CFG: %v", err)
	}
}

// SetRmbaseFileCfg sets the RMBASE_FILE_CFG environment variable to the given config path
func SetRmbaseFileCfg(configPath string) {
	// fmt.Println("Using config file:", configPath)

	// Verify config file exists
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		fmt.Printf("Config file not found at %s: %v\n", configPath, err)
		return
	}

	// Read and log config file content
	content, err := os.ReadFile(configPath)
	if err != nil {
		fmt.Printf("Failed to read config file: %v\n", err)
		return
	}

	if content != nil {
		fmt.Print("")
		// fmt.Println("Config file content exists")
		// fmt.Printf("###Config file content: %s\n", string(content))
	}

	// Set environment variable to point to test config
	if err := os.Setenv("RMBASE_FILE_CFG", configPath); err != nil {
		fmt.Printf("Failed to set RMBASE_FILE_CFG: %v\n", err)
		return
	}
}

// TestUtilMain is a common test main function that can be used across different test files
func TestUtilMain(m *testing.M, opts ...TestOptions) {
	var testOpts TestOptions
	if len(opts) > 0 {
		testOpts = opts[0]
	} else {
		testOpts = DefaultTestOptions()
	}

	if err := SetupTestEnv(testOpts); err != nil {
		fmt.Printf("Failed to setup test environment: %v\n", err)
		os.Exit(1)
	}

	// Run tests
	code := m.Run()

	// Cleanup only if we created temporary files
	if !testOpts.UseEnvConfig {
		CleanupTestEnv()
	}
	os.Exit(code)
}
