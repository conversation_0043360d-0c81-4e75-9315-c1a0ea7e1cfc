package gohelper

import (
	"sync/atomic"
	"testing"
)

func TestCallOnce(t *testing.T) {
	var counter int32 = 0

	// increment function
	increment := func() {
		atomic.AddInt32(&counter, 1)
	}

	// create wrapped function
	onlyOnce := CallOnce(increment)

	// call multiple times
	for i := 0; i < 10; i++ {
		onlyOnce()
	}

	// assert that it was called once
	if counter != 1 {
		t.<PERSON><PERSON><PERSON>("Expected function to be called once, but was called %d times", counter)
	}
}
