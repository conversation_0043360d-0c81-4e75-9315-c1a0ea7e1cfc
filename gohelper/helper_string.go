package gohelper

import (
	"bytes"
	"compress/gzip"
	"encoding/json"
	"fmt"
	"io"
	"regexp"

	golog "github.com/real-rm/golog"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var emailRegex = regexp.MustCompile(`^\w+([\-+\.]\w+)*@\w+([\-\.]\w+)*\.\w+([\-\.]\w+)*$`)

func IsEmail(eml string) bool {
	return emailRegex.MatchString(eml)
}

// DecompressPhoUrls decompresses gzip-compressed JSON array data and returns the media array.
// This function is designed to handle compressed JSON array data stored as MongoDB BinData,
// specifically for photo URL arrays, but can be used for any JSON array data.
//
// Parameters:
//   - phoUrls: MongoDB primitive.Binary containing gzip-compressed JSON array
//
// Returns:
//   - []interface{}: The decompressed JSON array
//   - error: Any error that occurred during decompression or parsing
//
// Example usage:
//
//	mediaArray, err := gohelper.DecompressPhoUrls(doc["phoUrls"])
//	if err != nil {
//	    log.Printf("Failed to decompress data: %v", err)
//	    return
//	}
//	fmt.Printf("Array contains %d items\n", len(mediaArray))
func DecompressPhoUrls(phoUrls interface{}) ([]interface{}, error) {
	// Handle primitive.Binary type from MongoDB
	var binData primitive.Binary
	var ok bool

	if binData, ok = phoUrls.(primitive.Binary); !ok {
		return nil, fmt.Errorf("phoUrls is not a primitive.Binary type, got %T", phoUrls)
	}

	// Create a gzip reader from the binary data
	reader, err := gzip.NewReader(bytes.NewReader(binData.Data))
	if err != nil {
		return nil, fmt.Errorf("failed to create gzip reader: %w", err)
	}
	defer func() {
		if err := reader.Close(); err != nil {
			golog.Error("Failed to close gzip reader", "error", err)
		}
	}()

	// Read all decompressed data
	decompressed, err := io.ReadAll(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to read decompressed data: %w", err)
	}

	// Parse JSON to get the media array
	var mediaArray []interface{}
	if err := json.Unmarshal(decompressed, &mediaArray); err != nil {
		return nil, fmt.Errorf("failed to parse decompressed JSON: %w", err)
	}

	return mediaArray, nil
}
