package gohelper

import (
	"sync"
	"time"

	golog "github.com/real-rm/golog"
)

var (
	stopMutex sync.Mutex
)

// define Interval struct
type Interval struct {
	ticker *time.Ticker
	stop   chan struct{}
}

// StartInterval - start a timer, execute `task` function every `intervalSec` seconds (supports fractional seconds)
func StartInterval(intervalSec float64, task func()) *Interval {
	interval := &Interval{
		ticker: time.NewTicker(time.Duration(intervalSec) * time.Second),
		stop:   make(chan struct{}),
	}

	go func() {
		for {
			select {
			case <-interval.ticker.C:
				task()
			case <-interval.stop:
				golog.Debug("Stopping interval...")
				return
			}
		}
	}()

	return interval
}

// Stop - stop the timer
func (i *Interval) Stop() {
	stopMutex.Lock()
	defer stopMutex.Unlock()

	select {
	case <-i.stop:
	default:
		close(i.stop)
	}
}

// func main() {
// 	fmt.Println("Starting interval...")

// 	// start a timer, execute `task` function every `intervalSec` seconds
// 	interval := StartInterval(2, func() {
// 		fmt.Println("Task executed at", time.Now())
// 	})

// 	// run 10 seconds, then stop the timer
// 	time.Sleep(10 * time.Second)
// 	interval.Stop()

// 	fmt.Println("Main function exiting...")
// }
