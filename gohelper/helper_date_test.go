package gohelper

import (
	"testing"
	"time"
)

func TestTimeToStr(t *testing.T) {
	tests := []struct {
		name string
		time time.Time
		want string
	}{
		{
			name: "Zero time",
			time: time.Time{},
			want: "0001-01-01 00:00:00",
		},
		{
			name: "Specific time",
			time: time.Date(2024, 3, 14, 15, 30, 45, 0, time.UTC),
			want: "2024-03-14 15:30:45",
		},
		{
			name: "Local timezone",
			time: time.Date(2024, 3, 14, 15, 30, 45, 0, time.Local),
			want: "2024-03-14 15:30:45",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := TimeToStr(tt.time); got != tt.want {
				t.Errorf("TimeToStr() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestStrToTime(t *testing.T) {
	tests := []struct {
		name    string
		str     string
		want    time.Time
		wantErr bool
	}{
		{
			name:    "Valid time string",
			str:     "2024-03-14 15:30:45",
			want:    time.Date(2024, 3, 14, 15, 30, 45, 0, time.Local),
			wantErr: false,
		},
		{
			name:    "Invalid time string",
			str:     "invalid",
			want:    time.Time{},
			wantErr: true,
		},
		{
			name:    "Leap year time string",
			str:     "2024-02-29 12:00:00",
			want:    time.Date(2024, 2, 29, 12, 0, 0, 0, time.Local),
			wantErr: false,
		},
		{
			name:    "Malformed but partially valid time string",
			str:     "2024-03-14 25:70:99",
			want:    time.Time{},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := StrToTime(tt.str)
			if (err != nil) != tt.wantErr {
				t.Errorf("StrToTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && !got.Equal(tt.want) {
				t.Errorf("StrToTime() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestDateToTime(t *testing.T) {
	tests := []struct {
		name     string
		dateInt  int
		expected time.Time
	}{
		{
			name:     "Valid date",
			dateInt:  20240314,
			expected: time.Date(2024, 3, 14, 0, 0, 0, 0, time.Local),
		},
		{
			name:     "Edge case - year boundary",
			dateInt:  20240101,
			expected: time.Date(2024, 1, 1, 0, 0, 0, 0, time.Local),
		},
		{
			name:     "Invalid date - negative",
			dateInt:  -1,
			expected: time.Time{},
		},
		{
			name:     "Invalid date - invalid month",
			dateInt:  20241301, // Month 13
			expected: time.Time{},
		},
		{
			name:     "Invalid date - invalid day",
			dateInt:  20240132, // Day 32
			expected: time.Time{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := DateToTime(tt.dateInt)
			if !got.Equal(tt.expected) {
				t.Errorf("DateToTime() = %v, want %v", got, tt.expected)
			}
		})
	}
}

func TestTimeToDateInt(t *testing.T) {
	tests := []struct {
		name      string
		timestamp time.Time
		expected  int
	}{
		{
			name:      "Valid time",
			timestamp: time.Date(2024, 3, 14, 15, 30, 45, 0, time.Local),
			expected:  20240314,
		},
		{
			name:      "Edge case - year boundary",
			timestamp: time.Date(2024, 1, 1, 0, 0, 0, 0, time.Local),
			expected:  20240101,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := TimeToDateInt(tt.timestamp); got != tt.expected {
				t.Errorf("TimeToDateInt() = %v, want %v", got, tt.expected)
			}
		})
	}
}
