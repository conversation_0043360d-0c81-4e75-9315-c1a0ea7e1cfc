package gohelper

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestContains(t *testing.T) {
	// Test with string slice
	t.Run("String slice", func(t *testing.T) {
		slice := []string{"apple", "banana", "orange"}
		assert.True(t, Contains(slice, "apple"))
		assert.True(t, Contains(slice, "banana"))
		assert.True(t, Contains(slice, "orange"))
		assert.False(t, Contains(slice, "grape"))
		assert.False(t, Contains(slice, ""))
	})

	// Test with int slice
	t.Run("Int slice", func(t *testing.T) {
		slice := []int{1, 2, 3, 4, 5}
		assert.True(t, Contains(slice, 1))
		assert.True(t, Contains(slice, 3))
		assert.True(t, Contains(slice, 5))
		assert.False(t, Contains(slice, 0))
		assert.False(t, Contains(slice, 6))
	})

	// Test with empty slice
	t.Run("Empty slice", func(t *testing.T) {
		slice := []string{}
		assert.False(t, Contains(slice, "test"))
		assert.False(t, Contains(slice, ""))
	})

	// Test with nil slice
	t.Run("Nil slice", func(t *testing.T) {
		var slice []string
		assert.False(t, Contains(slice, "test"))
		assert.False(t, Contains(slice, ""))
	})

	// Test with custom type
	t.Run("Custom type", func(t *testing.T) {
		type customType struct {
			id   int
			name string
		}
		slice := []customType{
			{id: 1, name: "one"},
			{id: 2, name: "two"},
		}
		assert.True(t, Contains(slice, customType{id: 1, name: "one"}))
		assert.False(t, Contains(slice, customType{id: 3, name: "three"}))
	})
}
