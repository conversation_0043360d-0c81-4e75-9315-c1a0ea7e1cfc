package gohelper

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGenUUID(t *testing.T) {
	tests := []struct {
		name          string
		length        int
		expectError   bool
		expectedLen   int
		runMultiple   bool // 用于测试多次生成的唯一性
		numIterations int  // 多次生成时的迭代次数
	}{
		{
			name:        "Standard UUID length (32)",
			length:      32,
			expectError: false,
			expectedLen: 32,
		},
		{
			name:        "Short UUID (8)",
			length:      8,
			expectError: false,
			expectedLen: 8,
		},
		{
			name:        "Zero length",
			length:      0,
			expectError: true,
			expectedLen: 0,
		},
		{
			name:        "Negative length",
			length:      -1,
			expectError: true,
			expectedLen: 0,
		},
		{
			name:          "Multiple unique UUIDs",
			length:        16,
			expectError:   false,
			expectedLen:   16,
			runMultiple:   true,
			numIterations: 100,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.runMultiple {
				// Test uniqueness of multiple generations
				uuids := make(map[string]bool)
				for i := 0; i < tt.numIterations; i++ {
					uuid, err := GenUUID(tt.length)
					assert.NoError(t, err)
					assert.Len(t, uuid, tt.expectedLen)
					// Verify uniqueness
					assert.False(t, uuids[uuid], "UUID should be unique")
					uuids[uuid] = true
				}
			} else {
				// Single generation test
				uuid, err := GenUUID(tt.length)
				if tt.expectError {
					assert.Error(t, err)
					assert.Empty(t, uuid)
				} else {
					assert.NoError(t, err)
					assert.NotEmpty(t, uuid)
					assert.Len(t, uuid, tt.expectedLen)
					// Verify it's a valid hex string
					for _, c := range uuid {
						assert.Contains(t, "0123456789abcdef", string(c), "UUID should only contain hex characters")
					}
				}
			}
		})
	}
}

func TestGenUUID_Concurrent(t *testing.T) {
	// Test concurrent UUID generation
	const (
		numGoroutines = 10
		numUUIDs      = 100
		uuidLength    = 32
	)

	// Channel to collect UUIDs from goroutines
	uuidChan := make(chan string, numGoroutines*numUUIDs)

	// Launch multiple goroutines to generate UUIDs
	for i := 0; i < numGoroutines; i++ {
		go func() {
			for j := 0; j < numUUIDs; j++ {
				uuid, err := GenUUID(uuidLength)
				assert.NoError(t, err)
				uuidChan <- uuid
			}
		}()
	}

	// Collect and verify UUIDs
	uuids := make(map[string]bool)
	for i := 0; i < numGoroutines*numUUIDs; i++ {
		uuid := <-uuidChan
		assert.Len(t, uuid, uuidLength)
		assert.False(t, uuids[uuid], "UUID should be unique even in concurrent generation")
		uuids[uuid] = true
	}
}

func BenchmarkGenUUID(b *testing.B) {
	lengths := []int{8, 16, 32, 64}

	for _, length := range lengths {
		b.Run(fmt.Sprintf("length_%d", length), func(b *testing.B) {
			for i := 0; i < b.N; i++ {
				_, err := GenUUID(length)
				assert.NoError(b, err)
			}
		})
	}
}
