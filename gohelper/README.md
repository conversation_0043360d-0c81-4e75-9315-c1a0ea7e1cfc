# gohelper

A collection of utility functions and helpers for Go applications that provide common programming tasks and operations.

## Features

- UUID Generation
  - Secure random UUID generation using crypto/rand
  - Customizable length support
  - Hexadecimal format output
  - Thread-safe for concurrent use
- String Operations
  - String manipulation utilities
  - Text processing functions
- Numeric Operations
  - Number formatting
  - Mathematical utilities
- Time Operations
  - Time formatting and parsing
  - Duration calculations
- File Operations
  - File handling utilities
  - Path manipulation

## Streaming Helper

The `Streaming` helper provides a way to process streams of data with flow control and speed monitoring. It's particularly useful when you need to process large amounts of data while controlling memory usage and monitoring processing speed.

### Features

- Flow control with high/low water marks
- Speed monitoring and reporting
- Error handling with optional continuation on error
- Verbose logging options
- Customizable processing function

## Installation

```bash
go get github.com/real-rm/gohelper
```

## Usage

### UUID Generation

```go
import "github.com/real-rm/gohelper"

// Generate standard length UUID (32 characters)
uuid := gohelper.GenUUID(32)
// Example output: "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"

// Generate short UUID (8 characters)
shortUUID := gohelper.GenUUID(8)
// Example output: "1a2b3c4d"

// Handle invalid length
emptyUUID := gohelper.GenUUID(0) // Returns empty string
```

### String Operations

```go
// String manipulation examples coming soon
```

### Numeric Operations

```go
// Numeric operation examples coming soon
```

### Time Operations

```go
// Time operation examples coming soon
```

### File Operations

```go
// File operation examples coming soon
```

### Streaming Helper

```go
package main

import (
    "fmt"
    "time"
    "github.com/your-username/gohelper"
)

func main() {
    // Create a channel for data streaming
    stream := make(chan interface{}, 100)

    // Configure streaming options
    opts := &gohelper.StreamingOptions{
        Stream: stream,
        // Process function for each item
        Process: func(item interface{}) error {
            // Your processing logic here
            value, ok := item.(string)
            if !ok {
                return fmt.Errorf("expected string, got %T", item)
            }
            fmt.Printf("Processing: %s\n", value)
            return nil
        },
        // Called when streaming ends
        End: func(err error) {
            if err != nil {
                fmt.Printf("Streaming ended with error: %v\n", err)
            } else {
                fmt.Println("Streaming completed successfully")
            }
        },
        // Error handler for individual items when NoStopOnError is true
        Error: func(err error) {
            fmt.Printf("Error processing item: %v\n", err)
        },
        High: 1000,           // Pause when queue reaches 1000 items
        Low: 100,             // Resume when queue drops to 100 items
        SpeedInterval: 5000,  // Report speed every 5 seconds
        Verbose: 2,           // Logging verbosity level
        NoStopOnError: true,  // Continue processing on errors
    }

    // Start streaming
    gohelper.Streaming(opts)

    // Simulate sending data
    go func() {
        for i := 0; i < 10000; i++ {
            stream <- fmt.Sprintf("item-%d", i)
            time.Sleep(time.Millisecond * 10)
        }
        close(stream)
    }()

    // Wait for streaming to complete
    time.Sleep(time.Second * 120)
}
```

## Performance Considerations

- Uses crypto/rand for high-quality randomness in UUID generation
- Optimized for concurrent scenarios
- Memory allocation optimized to avoid unnecessary usage
- Reuse generated UUIDs for frequent-call scenarios

## Testing

The package includes comprehensive test coverage:
- Functional tests: Verify operations across various inputs
- Concurrency tests: Ensure thread safety
- Performance tests: Provide benchmark data
- Edge case tests: Handle exceptional inputs

## Dependencies

- Standard library only:
  - crypto/rand
  - encoding/hex
  - strings
  - time
  - os
  - path/filepath

## Best Practices

1. Use appropriate UUID lengths for your use case
2. Implement error handling for all operations
3. Consider performance implications in high-concurrency scenarios
4. Follow Go's standard practices for error handling
5. Use the provided utilities instead of implementing custom solutions

## Contributing

Contributions are welcome! Please feel free to submit issues and pull requests.

## License

This project is licensed under the MIT License.

## Private Repository Setup

```bash
export GOPRIVATE=*
vi ~/.gitconfig
```

Add:
```
[url "ssh://**************"]
    insteadOf = https://github.com
```
