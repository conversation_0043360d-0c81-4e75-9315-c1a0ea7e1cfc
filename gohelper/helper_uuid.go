// Package gohelper provides utility functions and helpers for the application.
package gohelper

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
)

// GenUUID generates a random UUID-like string of specified length
// If length is less than or equal to 0, returns an empty string
func GenUUID(length int) (string, error) {
	// Return empty string for invalid lengths
	if length <= 0 {
		return "", fmt.Errorf("invalid length: %d", length)
	}

	// Calculate number of bytes needed based on desired hex string length
	numBytes := (length + 1) / 2 // Round up division to ensure enough bytes

	// Create byte slice to store random bytes
	bytes := make([]byte, numBytes)

	// Read random bytes
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.E<PERSON><PERSON>("failed to generate random bytes: %w", err)
	}

	// Convert to hex string and truncate to desired length
	uuid := hex.EncodeToString(bytes)
	if len(uuid) > length {
		uuid = uuid[:length]
	}

	return uuid, nil
}
