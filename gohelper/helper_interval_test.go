package gohelper

import (
	"os"
	"path/filepath"
	"sync/atomic"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func setupTestInterval(t *testing.T) func() {
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	configPath, err := filepath.Abs(filepath.Join(currentDir, "local.test.ini"))
	if err != nil {
		t.Fatalf("Failed to get absolute path: %v", err)
	}
	SetRmbaseFileCfg(configPath)
	// Initialize test environment
	if err := SetupTestEnv(TestOptions{
		UseEnvConfig: true,
	}); err != nil {
		t.Fatalf("Failed to setup test environment: %v", err)
	}

	return CleanupTestEnv
}

func TestStartInterval(t *testing.T) {
	cleanup := setupTestInterval(t)
	defer cleanup()

	tests := []struct {
		name        string
		intervalSec float64
		duration    time.Duration
		minCount    int32
	}{
		{
			name:        "1 second interval for 3 seconds",
			intervalSec: 1,
			duration:    3 * time.Second,
			minCount:    2, // 至少执行2次
		},
		{
			name:        "2 second interval for 5 seconds",
			intervalSec: 2,
			duration:    5 * time.Second,
			minCount:    2, // 至少执行2次
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var count int32
			interval := StartInterval(tt.intervalSec, func() {
				atomic.AddInt32(&count, 1)
			})

			// 等待指定时间
			time.Sleep(tt.duration)

			// 停止定时器
			interval.Stop()

			// 验证执行次数
			actualCount := atomic.LoadInt32(&count)
			assert.GreaterOrEqual(t, actualCount, tt.minCount,
				"Task should execute at least %d times in %v with %d second interval",
				tt.minCount, tt.duration, tt.intervalSec)
		})
	}
}

func TestInterval_Stop(t *testing.T) {
	cleanup := setupTestInterval(t)
	defer cleanup()

	var count int32
	interval := StartInterval(1, func() {
		atomic.AddInt32(&count, 1)
	})

	// 立即停止
	interval.Stop()
	time.Sleep(2 * time.Second)

	// 验证停止后没有继续执行
	finalCount := atomic.LoadInt32(&count)
	time.Sleep(2 * time.Second)
	afterStopCount := atomic.LoadInt32(&count)

	assert.Equal(t, finalCount, afterStopCount,
		"Count should not increase after stopping the interval")
}
