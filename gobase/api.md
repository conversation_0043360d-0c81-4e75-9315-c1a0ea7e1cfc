<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# batch\_base

```go
import "github.com/real-rm/gobase"
```

## Index

- [func ErrorNotifyEmail\(message string, to \[\]string, batchFileName string\) error](<#ErrorNotifyEmail>)
- [func ErrorNotifySMS\(message string, batchFileName string\) error](<#ErrorNotifySMS>)
- [func Exit\(err interface\{\}, code ...int\)](<#Exit>)
- [func GetHostname\(\) string](<#GetHostname>)
- [func InitBase\(\) error](<#InitBase>)


<a name="ErrorNotifyEmail"></a>
## func [ErrorNotifyEmail](<https://github.com/real-rm/gobase/blob/main/batch_base.go#L44>)

```go
func ErrorNotifyEmail(message string, to []string, batchFileName string) error
```

ErrorNotifyEmail sends error notification via email

<a name="ErrorNotifySMS"></a>
## func [ErrorNotifySMS](<https://github.com/real-rm/gobase/blob/main/batch_base.go#L88>)

```go
func ErrorNotifySMS(message string, batchFileName string) error
```

ErrorNotifySMS sends error notification via SMS

<a name="Exit"></a>
## func [Exit](<https://github.com/real-rm/gobase/blob/main/batch_base.go#L154>)

```go
func Exit(err interface{}, code ...int)
```

Exit exits the program with an error message

<a name="GetHostname"></a>
## func [GetHostname](<https://github.com/real-rm/gobase/blob/main/batch_base.go#L144>)

```go
func GetHostname() string
```

GetHostname returns the hostname of the machine

<a name="InitBase"></a>
## func [InitBase](<https://github.com/real-rm/gobase/blob/main/batch_base.go#L29>)

```go
func InitBase() error
```

InitBase initializes common components like config and logging

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)
