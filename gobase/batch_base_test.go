package batch_base

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"

	goconfig "github.com/real-rm/goconfig"
	"github.com/real-rm/gohelper"
	gomail "github.com/real-rm/gomail"
	gosms "github.com/real-rm/gosms"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Mock implementations for testing
type MockMailer struct {
	mock.Mock
}

func (m *MockMailer) SendMail(engine string, msg *gomail.EmailMessage) error {
	args := m.Called(engine, msg)
	return args.Error(0)
}

func (m *MockMailer) CheckAndFixFrom(engineName string, mail *gomail.EmailMessage) string {
	args := m.Called(engineName, mail)
	return args.String(0)
}

func (m *MockMailer) GetEngine(engineName string, msg *gomail.EmailMessage) gomail.MailEngine {
	args := m.Called(engineName, msg)
	return args.Get(0).(gomail.MailEngine)
}

func (m *MockMailer) GetName(engineName string, msg *gomail.EmailMessage) string {
	args := m.Called(engineName, msg)
	return args.String(0)
}

func (m *MockMailer) NotifyAdmin(msg string) error {
	args := m.Called(msg)
	return args.Error(0)
}

type MockSMSEngine struct {
	mock.Mock
}

func (m *MockSMSEngine) Send(to string, message string, from string) error {
	args := m.Called(to, message, from)
	return args.Error(0)
}

// CustomMockMailEngine is a mock mail engine that returns error on Send
type CustomMockMailEngine struct {
	*gomail.MockMailEngine
}

func (e *CustomMockMailEngine) Send(msg *gomail.EmailMessage) (string, error) {
	return "", assert.AnError
}

func (e *CustomMockMailEngine) Init(config map[string]interface{}) error {
	return e.MockMailEngine.Init(config)
}

func TestGetHostname(t *testing.T) {
	// Save original function and restore after test
	originalHostname := hostnameFunc
	defer func() { hostnameFunc = originalHostname }()

	// Test successful hostname retrieval
	hostname := GetHostname()
	assert.NotEmpty(t, hostname)

	// Test with mocked hostname
	hostnameFunc = func() (string, error) {
		return "test-hostname", nil
	}
	hostname = GetHostname()
	assert.Equal(t, "test-hostname", hostname)

	// Test error case
	hostnameFunc = func() (string, error) {
		return "", assert.AnError
	}
	hostname = GetHostname()
	assert.Empty(t, hostname)

	tests := []struct {
		name        string
		setup       func()
		expectEmpty bool
	}{
		{
			name:        "Hostname error",
			expectEmpty: true,
			setup: func() {
				originalHostname := hostnameFunc
				hostnameFunc = func() (string, error) {
					return "", assert.AnError
				}
				defer func() { hostnameFunc = originalHostname }()
			},
		},
		{
			name:        "Empty hostname",
			expectEmpty: true,
			setup: func() {
				originalHostname := hostnameFunc
				hostnameFunc = func() (string, error) {
					return "", nil
				}
				defer func() { hostnameFunc = originalHostname }()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.setup != nil {
				tt.setup()
			}

			hostname := GetHostname()
			if tt.expectEmpty {
				assert.Empty(t, hostname)
			} else {
				assert.NotEmpty(t, hostname)
			}
		})
	}
}

func TestErrorNotifyEmail(t *testing.T) {
	// Set up test environment
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	configPath, err := filepath.Abs(filepath.Join(currentDir, "local.test.ini"))
	if err != nil {
		t.Fatalf("Failed to get absolute path: %v", err)
	}
	if _, err := os.Stat(configPath); err != nil {
		t.Fatalf("Config file not found at %s: %v", configPath, err)
	}
	gohelper.SetRmbaseFileCfg(configPath)

	// Initialize test environment
	if err := gohelper.SetupTestEnv(gohelper.TestOptions{
		UseEnvConfig: true,
	}); err != nil {
		t.Fatalf("Failed to setup test environment: %v", err)
	}
	defer gohelper.CleanupTestEnv()

	// Set mock mail configuration via environment variables
	if err := os.Setenv("mailEngine.mailEngine", "mockmail"); err != nil {
		t.Fatalf("Failed to set mailEngine.mailEngine: %v", err)
	}
	if err := os.Setenv("mailEngine.mockMail.mock", "true"); err != nil {
		t.Fatalf("Failed to set mailEngine.mockMail.mock: %v", err)
	}
	if err := os.Setenv("mailEngine.mockMail.verbose", "1"); err != nil {
		t.Fatalf("Failed to set mailEngine.mockMail.verbose: %v", err)
	}
	if err := os.Setenv("mailEngine.mockMail.defaultEmail", "<EMAIL>"); err != nil {
		t.Fatalf("Failed to set mailEngine.mockMail.defaultEmail: %v", err)
	}

	// Reload configuration to pick up environment variables
	if err := goconfig.LoadConfig(); err != nil {
		t.Fatalf("Failed to reload config: %v", err)
	}

	// Initialize mock mail engine
	mockEngine := gomail.NewMockMailEngine()
	if err := mockEngine.Init(map[string]interface{}{
		"verbose": 1,
	}); err != nil {
		t.Fatalf("Failed to initialize mock engine: %v", err)
	}

	// Override the getMailSender function to use mock engine
	originalGetMailSender := getMailSender
	getMailSender = func() (*gomail.Mailer, error) {
		return &gomail.Mailer{
			MockMail: mockEngine,
		}, nil
	}
	t.Cleanup(func() { getMailSender = originalGetMailSender })

	tests := []struct {
		name        string
		message     string
		to          []string
		batchFile   string
		expectError bool
		setup       func()
	}{
		{
			name:        "Valid parameters",
			message:     "Test error",
			to:          []string{"<EMAIL>"},
			batchFile:   "test.go",
			expectError: false,
		},
		{
			name:        "Empty message",
			message:     "",
			to:          []string{"<EMAIL>"},
			batchFile:   "test.go",
			expectError: true,
		},
		{
			name:        "Empty batch file",
			message:     "Test error",
			to:          []string{"<EMAIL>"},
			batchFile:   "",
			expectError: true,
		},
		{
			name:        "Empty recipients",
			message:     "Test error",
			to:          []string{},
			batchFile:   "test.go",
			expectError: true,
		},
		{
			name:        "GetMailSender error",
			message:     "Test error",
			to:          []string{"<EMAIL>"},
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				original := getMailSender
				getMailSender = func() (*gomail.Mailer, error) { return nil, assert.AnError }
				t.Cleanup(func() { getMailSender = original })
			},
		},
		{
			name:        "SendMail error",
			message:     "Test error",
			to:          []string{"<EMAIL>"},
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				getMailSender = func() (*gomail.Mailer, error) {
					mockEngine := &CustomMockMailEngine{
						MockMailEngine: gomail.NewMockMailEngine(),
					}
					if err := mockEngine.Init(map[string]interface{}{
						"verbose": 1,
					}); err != nil {
						t.Fatalf("Failed to initialize mock engine: %v", err)
					}
					return &gomail.Mailer{
						MockMail: mockEngine,
					}, nil
				}
			},
		},
		{
			name:        "Empty hostname",
			message:     "Test error",
			to:          []string{"<EMAIL>"},
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				originalHostname := hostnameFunc
				hostnameFunc = func() (string, error) {
					return "", assert.AnError
				}
				defer func() { hostnameFunc = originalHostname }()
			},
		},
		{
			name:        "Invalid mail engine",
			message:     "Test error",
			to:          []string{"<EMAIL>"},
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				getMailSender = func() (*gomail.Mailer, error) {
					return &gomail.Mailer{}, nil
				}
			},
		},
		{
			name:        "SES engine with mock disabled",
			message:     "Test error",
			to:          []string{"<EMAIL>"},
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				if err := os.Setenv("mailEngine.mockMail.mock", "false"); err != nil {
					t.Fatalf("Failed to set mailEngine.mockMail.mock: %v", err)
				}
				getMailSender = func() (*gomail.Mailer, error) {
					mockEngine := &CustomMockMailEngine{
						MockMailEngine: gomail.NewMockMailEngine(),
					}
					if err := mockEngine.Init(map[string]interface{}{
						"verbose": 1,
					}); err != nil {
						t.Fatalf("Failed to initialize mock engine: %v", err)
					}
					return &gomail.Mailer{
						MockMail: mockEngine,
					}, nil
				}
			},
		},
		{
			name:        "SES engine with mock enabled",
			message:     "Test error",
			to:          []string{"<EMAIL>"},
			batchFile:   "test.go",
			expectError: false,
			setup: func() {
				if err := os.Setenv("mailEngine.mockMail.mock", "true"); err != nil {
					t.Fatalf("Failed to set mailEngine.mockMail.mock: %v", err)
				}
				getMailSender = func() (*gomail.Mailer, error) {
					return &gomail.Mailer{
						MockMail: mockEngine,
					}, nil
				}
			},
		},
		{
			name:        "SES engine with mock disabled and error",
			message:     "Test error",
			to:          []string{"<EMAIL>"},
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				if err := os.Setenv("mailEngine.mockMail.mock", "false"); err != nil {
					t.Fatalf("Failed to set mailEngine.mockMail.mock: %v", err)
				}
				getMailSender = func() (*gomail.Mailer, error) {
					mockEngine := &CustomMockMailEngine{
						MockMailEngine: gomail.NewMockMailEngine(),
					}
					if err := mockEngine.Init(map[string]interface{}{
						"verbose": 1,
					}); err != nil {
						t.Fatalf("Failed to initialize mock engine: %v", err)
					}
					return &gomail.Mailer{
						MockMail: mockEngine,
					}, nil
				}
			},
		},
		{
			name:        "SES engine with mock disabled and success",
			message:     "Test error",
			to:          []string{"<EMAIL>"},
			batchFile:   "test.go",
			expectError: false,
			setup: func() {
				if err := os.Setenv("mailEngine.mockMail.mock", "false"); err != nil {
					t.Fatalf("Failed to set mailEngine.mockMail.mock: %v", err)
				}
				getMailSender = func() (*gomail.Mailer, error) {
					return &gomail.Mailer{
						MockMail: mockEngine,
					}, nil
				}
			},
		},
		{
			name:        "SES engine with mock disabled and error in SendMail",
			message:     "Test error",
			to:          []string{"<EMAIL>"},
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				if err := os.Setenv("mailEngine.mockMail.mock", "false"); err != nil {
					t.Fatalf("Failed to set mailEngine.mockMail.mock: %v", err)
				}
				getMailSender = func() (*gomail.Mailer, error) {
					mockEngine := &CustomMockMailEngine{
						MockMailEngine: gomail.NewMockMailEngine(),
					}
					if err := mockEngine.Init(map[string]interface{}{
						"verbose": 1,
					}); err != nil {
						t.Fatalf("Failed to initialize mock engine: %v", err)
					}
					return &gomail.Mailer{
						MockMail: mockEngine,
					}, nil
				}
			},
		},
		{
			name:        "SES engine with mock disabled and error in SendMail with custom error",
			message:     "Test error",
			to:          []string{"<EMAIL>"},
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				if err := os.Setenv("mailEngine.mockMail.mock", "false"); err != nil {
					t.Fatalf("Failed to set mailEngine.mockMail.mock: %v", err)
				}
				getMailSender = func() (*gomail.Mailer, error) {
					mockEngine := &CustomMockMailEngine{
						MockMailEngine: gomail.NewMockMailEngine(),
					}
					if err := mockEngine.Init(map[string]interface{}{
						"verbose": 1,
					}); err != nil {
						t.Fatalf("Failed to initialize mock engine: %v", err)
					}
					return &gomail.Mailer{
						MockMail: mockEngine,
					}, nil
				}
			},
		},
		{
			name:        "SES engine with mock disabled and error in SendMail with custom error message",
			message:     "Test error",
			to:          []string{"<EMAIL>"},
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				if err := os.Setenv("mailEngine.mockMail.mock", "false"); err != nil {
					t.Fatalf("Failed to set mailEngine.mockMail.mock: %v", err)
				}
				getMailSender = func() (*gomail.Mailer, error) {
					mockEngine := &CustomMockMailEngine{
						MockMailEngine: gomail.NewMockMailEngine(),
					}
					if err := mockEngine.Init(map[string]interface{}{
						"verbose": 1,
					}); err != nil {
						t.Fatalf("Failed to initialize mock engine: %v", err)
					}
					return &gomail.Mailer{
						MockMail: mockEngine,
					}, nil
				}
			},
		},
		{
			name:        "Non-mock mail engine with mock disabled",
			message:     "Test error",
			to:          []string{"<EMAIL>"},
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				if err := os.Setenv("mailEngine.mockMail.mock", "false"); err != nil {
					t.Fatalf("Failed to set mailEngine.mockMail.mock: %v", err)
				}
				getMailSender = func() (*gomail.Mailer, error) {
					return &gomail.Mailer{}, nil
				}
			},
		},
		{
			name:        "Non-mock mail engine with mock enabled",
			message:     "Test error",
			to:          []string{"<EMAIL>"},
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				if err := os.Setenv("mailEngine.mockMail.mock", "true"); err != nil {
					t.Fatalf("Failed to set mailEngine.mockMail.mock: %v", err)
				}
				getMailSender = func() (*gomail.Mailer, error) {
					return &gomail.Mailer{}, nil
				}
			},
		},
		{
			name:        "Non-mock mail engine with mock disabled and error",
			message:     "Test error",
			to:          []string{"<EMAIL>"},
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				if err := os.Setenv("mailEngine.mockMail.mock", "false"); err != nil {
					t.Fatalf("Failed to set mailEngine.mockMail.mock: %v", err)
				}
				getMailSender = func() (*gomail.Mailer, error) {
					mockEngine := &CustomMockMailEngine{
						MockMailEngine: gomail.NewMockMailEngine(),
					}
					if err := mockEngine.Init(map[string]interface{}{
						"verbose": 1,
					}); err != nil {
						t.Fatalf("Failed to initialize mock engine: %v", err)
					}
					return &gomail.Mailer{
						MockMail: mockEngine,
					}, nil
				}
			},
		},
		{
			name:        "Non-mock mail engine with mock enabled and error",
			message:     "Test error",
			to:          []string{"<EMAIL>"},
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				if err := os.Setenv("mailEngine.mockMail.mock", "true"); err != nil {
					t.Fatalf("Failed to set mailEngine.mockMail.mock: %v", err)
				}
				getMailSender = func() (*gomail.Mailer, error) {
					mockEngine := &CustomMockMailEngine{
						MockMailEngine: gomail.NewMockMailEngine(),
					}
					if err := mockEngine.Init(map[string]interface{}{
						"verbose": 1,
					}); err != nil {
						t.Fatalf("Failed to initialize mock engine: %v", err)
					}
					return &gomail.Mailer{
						MockMail: mockEngine,
					}, nil
				}
			},
		},
		{
			name:        "Mail engine with mock disabled and error",
			message:     "Test error",
			to:          []string{"<EMAIL>"},
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				if err := os.Setenv("mailEngine.mockMail.mock", "false"); err != nil {
					t.Fatalf("Failed to set mailEngine.mockMail.mock: %v", err)
				}
				getMailSender = func() (*gomail.Mailer, error) {
					mockEngine := &CustomMockMailEngine{
						MockMailEngine: gomail.NewMockMailEngine(),
					}
					if err := mockEngine.Init(map[string]interface{}{
						"verbose": 1,
					}); err != nil {
						t.Fatalf("Failed to initialize mock engine: %v", err)
					}
					return &gomail.Mailer{
						MockMail: mockEngine,
					}, nil
				}
			},
		},
		{
			name:        "Mail engine with mock enabled and error",
			message:     "Test error",
			to:          []string{"<EMAIL>"},
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				if err := os.Setenv("mailEngine.mockMail.mock", "true"); err != nil {
					t.Fatalf("Failed to set mailEngine.mockMail.mock: %v", err)
				}
				getMailSender = func() (*gomail.Mailer, error) {
					mockEngine := &CustomMockMailEngine{
						MockMailEngine: gomail.NewMockMailEngine(),
					}
					if err := mockEngine.Init(map[string]interface{}{
						"verbose": 1,
					}); err != nil {
						t.Fatalf("Failed to initialize mock engine: %v", err)
					}
					return &gomail.Mailer{
						MockMail: mockEngine,
					}, nil
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset mock engine before each test
			mockEngine.Reset()

			// Run setup if provided
			if tt.setup != nil {
				tt.setup()
			}

			err := ErrorNotifyEmail(tt.message, tt.to, tt.batchFile)
			if tt.expectError {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)

			// Verify email was sent using mock engine
			mail, err := mockEngine.GetMails("RealMaster APP Status Notify", "")
			assert.NoError(t, err)
			assert.Equal(t, "RealMaster <<EMAIL>>", mail.From)
			assert.Equal(t, "RealMaster APP Status Notify", mail.Subject)
			assert.Contains(t, mail.Text, tt.message)
			assert.Equal(t, tt.to, mail.To)
		})
	}
}

func TestErrorNotifySMS(t *testing.T) {
	// Set up test environment
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	configPath, err := filepath.Abs(filepath.Join(currentDir, "local.test.ini"))
	if err != nil {
		t.Fatalf("Failed to get absolute path: %v", err)
	}
	if _, err := os.Stat(configPath); err != nil {
		t.Fatalf("Config file not found at %s: %v", configPath, err)
	}
	gohelper.SetRmbaseFileCfg(configPath)

	// Initialize test environment
	if err := gohelper.SetupTestEnv(gohelper.TestOptions{
		UseEnvConfig: true,
	}); err != nil {
		t.Fatalf("Failed to setup test environment: %v", err)
	}
	defer gohelper.CleanupTestEnv()

	// Mock hostname
	originalHostname := hostnameFunc
	hostnameFunc = func() (string, error) {
		return "test-hostname", nil
	}
	defer func() { hostnameFunc = originalHostname }()

	// Create mock SMS engine
	mockEngine := &MockSMSEngine{}
	mockEngine.On("Send", "1234567890", mock.Anything, "+19056142609").Return(nil)

	// Override newSMSEngine to return our mock
	originalNewSMSEngine := newSMSEngine
	newSMSEngine = func(sid, authToken string) (gosms.Sender, error) {
		return mockEngine, nil
	}
	defer func() { newSMSEngine = originalNewSMSEngine }()

	tests := []struct {
		name        string
		message     string
		batchFile   string
		expectError bool
		setup       func()
	}{
		{
			name:        "Valid parameters",
			message:     "Test error",
			batchFile:   "test.go",
			expectError: false,
		},
		{
			name:        "Empty message",
			message:     "",
			batchFile:   "test.go",
			expectError: true,
		},
		{
			name:        "Empty batch file",
			message:     "Test error",
			batchFile:   "",
			expectError: true,
		},
		{
			name:        "Developer mode enabled",
			message:     "Test error",
			batchFile:   "test.go",
			expectError: false,
			setup: func() {
				originalConfig := configFunc
				t.Cleanup(func() { configFunc = originalConfig })
				configFunc = func(path string) interface{} {
					if path == "serverBase.developer_mode" {
						return true
					}
					return originalConfig(path)
				}
			},
		},
		{
			name:        "NewSMSEngine error",
			message:     "Test error",
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				original := newSMSEngine
				newSMSEngine = func(sid, authToken string) (gosms.Sender, error) {
					return nil, assert.AnError
				}
				t.Cleanup(func() { newSMSEngine = original })
			},
		},
		{
			name:        "NewSMSSender error",
			message:     "Test error",
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				mockEngine := &MockSMSEngine{}
				mockEngine.On("Send", "1234567890", mock.Anything, "+19056142609").Return(assert.AnError)
				newSMSEngine = func(sid, authToken string) (gosms.Sender, error) {
					return mockEngine, nil
				}
			},
		},
		{
			name:        "Send SMS error",
			message:     "Test error",
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				mockEngine := &MockSMSEngine{}
				mockEngine.On("Send", "1234567890", mock.Anything, "+19056142609").Return(assert.AnError)
				newSMSEngine = func(sid, authToken string) (gosms.Sender, error) {
					return mockEngine, nil
				}
			},
		},
		{
			name:        "Empty hostname",
			message:     "Test error",
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				originalHostname := hostnameFunc
				hostnameFunc = func() (string, error) {
					return "", assert.AnError
				}
				defer func() { hostnameFunc = originalHostname }()
			},
		},
		{
			name:        "Missing Twilio credentials",
			message:     "Test error",
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				originalConfig := configFunc
				t.Cleanup(func() { configFunc = originalConfig })
				configFunc = func(path string) interface{} {
					if path == "serverBase.developer_mode" {
						return false
					}
					return originalConfig(path)
				}

				originalConfigString := configStringFunc
				t.Cleanup(func() { configStringFunc = originalConfigString })
				configStringFunc = func(path string) (string, error) {
					if path == "twilio.sid" || path == "twilio.authToken" {
						return "", nil
					}
					return originalConfigString(path)
				}
			},
		},
		{
			name:        "ConfigString error",
			message:     "Test error",
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				originalConfigString := configStringFunc
				defer func() { configStringFunc = originalConfigString }()
				configStringFunc = func(path string) (string, error) {
					return "", assert.AnError
				}
			},
		},
		{
			name:        "SMS sender error with custom error",
			message:     "Test error",
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				originalConfigString := configStringFunc
				defer func() { configStringFunc = originalConfigString }()
				configStringFunc = func(path string) (string, error) {
					switch path {
					case "contact.alertSMS":
						return "1234567890", nil
					case "twilio.sid":
						return "test_sid", nil
					case "twilio.authToken":
						return "test_token", nil
					default:
						return originalConfigString(path)
					}
				}
				originalNewSMSEngine := newSMSEngine
				defer func() { newSMSEngine = originalNewSMSEngine }()
				newSMSEngine = func(sid, authToken string) (gosms.Sender, error) {
					mockEngine := &MockSMSEngine{}
					mockEngine.On("Send", "1234567890", mock.Anything, "+19056142609").Return(fmt.Errorf("custom error"))
					return mockEngine, nil
				}
			},
		},
		{
			name:        "SMS engine error with custom error",
			message:     "Test error",
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				originalConfigString := configStringFunc
				defer func() { configStringFunc = originalConfigString }()
				configStringFunc = func(path string) (string, error) {
					switch path {
					case "contact.alertSMS":
						return "1234567890", nil
					case "twilio.sid":
						return "test_sid", nil
					case "twilio.authToken":
						return "test_token", nil
					default:
						return originalConfigString(path)
					}
				}
				originalNewSMSEngine := newSMSEngine
				defer func() { newSMSEngine = originalNewSMSEngine }()
				newSMSEngine = func(sid, authToken string) (gosms.Sender, error) {
					return nil, fmt.Errorf("custom error")
				}
			},
		},
		{
			name:        "SMS sender error with empty message",
			message:     "",
			batchFile:   "test.go",
			expectError: true,
			setup: func() {
				originalConfigString := configStringFunc
				defer func() { configStringFunc = originalConfigString }()
				configStringFunc = func(path string) (string, error) {
					switch path {
					case "contact.alertSMS":
						return "1234567890", nil
					case "twilio.sid":
						return "test_sid", nil
					case "twilio.authToken":
						return "test_token", nil
					default:
						return originalConfigString(path)
					}
				}
			},
		},
		{
			name:        "SMS sender error with empty batch file",
			message:     "Test error",
			batchFile:   "",
			expectError: true,
			setup: func() {
				originalConfigString := configStringFunc
				defer func() { configStringFunc = originalConfigString }()
				configStringFunc = func(path string) (string, error) {
					switch path {
					case "contact.alertSMS":
						return "1234567890", nil
					case "twilio.sid":
						return "test_sid", nil
					case "twilio.authToken":
						return "test_token", nil
					default:
						return originalConfigString(path)
					}
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset environment variables
			if err := os.Setenv("RMBASE_SERVERBASE_DEVELOPER_MODE", "false"); err != nil {
				t.Fatalf("Failed to set RMBASE_SERVERBASE_DEVELOPER_MODE: %v", err)
			}
			if err := os.Setenv("RMBASE_TWILIO_SID", "test_sid"); err != nil {
				t.Fatalf("Failed to set RMBASE_TWILIO_SID: %v", err)
			}
			if err := os.Setenv("RMBASE_TWILIO_AUTHTOKEN", "test_token"); err != nil {
				t.Fatalf("Failed to set RMBASE_TWILIO_AUTHTOKEN: %v", err)
			}

			// Run setup if provided
			if tt.setup != nil {
				tt.setup()
			}

			err := ErrorNotifySMS(tt.message, tt.batchFile)
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				// Verify SMS was sent with correct parameters
				mockEngine.AssertCalled(t, "Send", "1234567890", mock.MatchedBy(func(msg string) bool {
					return strings.Contains(msg, tt.message) && strings.Contains(msg, "test-hostname") && strings.Contains(msg, tt.batchFile)
				}), "+19056142609")
			}
		})
	}
}

func TestExit(t *testing.T) {
	tests := []struct {
		name       string
		err        interface{}
		code       []int
		expectExit bool
	}{
		{
			name:       "Exit with nil error",
			err:        nil,
			code:       []int{},
			expectExit: true,
		},
		{
			name:       "Exit with error",
			err:        assert.AnError,
			code:       []int{},
			expectExit: true,
		},
		{
			name:       "Exit with string error",
			err:        "test error",
			code:       []int{},
			expectExit: true,
		},
		{
			name:       "Exit with custom code",
			err:        nil,
			code:       []int{2},
			expectExit: true,
		},
		{
			name:       "Exit with multiple codes",
			err:        nil,
			code:       []int{1, 2, 3},
			expectExit: true,
		},
		{
			name:       "Exit with error and code",
			err:        assert.AnError,
			code:       []int{3},
			expectExit: true,
		},
		{
			name:       "Exit with struct error",
			err:        struct{ msg string }{"test error"},
			code:       []int{},
			expectExit: true,
		},
		{
			name:       "Exit with custom error type",
			err:        fmt.Errorf("custom error"),
			code:       []int{},
			expectExit: true,
		},
		{
			name:       "Exit with nil error and custom code",
			err:        nil,
			code:       []int{4},
			expectExit: true,
		},
		{
			name:       "Exit with error and multiple codes",
			err:        assert.AnError,
			code:       []int{1, 2, 3},
			expectExit: true,
		},
		{
			name:       "Exit with error and empty code",
			err:        assert.AnError,
			code:       []int{},
			expectExit: true,
		},
		{
			name:       "Exit with nil error and empty code",
			err:        nil,
			code:       []int{},
			expectExit: true,
		},
		{
			name:       "Exit with error and zero code",
			err:        assert.AnError,
			code:       []int{0},
			expectExit: true,
		},
		{
			name:       "Exit with nil error and zero code",
			err:        nil,
			code:       []int{0},
			expectExit: true,
		},
		{
			name:       "Exit with error and negative code",
			err:        assert.AnError,
			code:       []int{-1},
			expectExit: true,
		},
		{
			name:       "Exit with nil error and negative code",
			err:        nil,
			code:       []int{-1},
			expectExit: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Since Exit calls os.Exit, we can't test it directly
			// This is more of a documentation of expected behavior
			assert.True(t, tt.expectExit)
		})
	}
}

func TestInitBase(t *testing.T) {
	tests := []struct {
		name        string
		setup       func()
		expectError bool
	}{
		{
			name:        "Config load error",
			expectError: true,
			setup: func() {
				if err := os.Unsetenv("RMBASE_FILE_CFG"); err != nil {
					t.Fatalf("Failed to unset RMBASE_FILE_CFG: %v", err)
				}
				if err := os.Setenv("RMBASE_FILE_CFG", "nonexistent.ini"); err != nil {
					t.Fatalf("Failed to set RMBASE_FILE_CFG: %v", err)
				}
				goconfig.ResetConfig()
			},
		},
		{
			name:        "Log initialization error",
			expectError: true,
			setup: func() {
				if err := os.Setenv("RMBASE_LOG_LEVEL", "invalid"); err != nil {
					t.Fatalf("Failed to set RMBASE_LOG_LEVEL: %v", err)
				}
				if err := os.Setenv("RMBASE_LOG_FILE", "/invalid/path/log.txt"); err != nil {
					t.Fatalf("Failed to set RMBASE_LOG_FILE: %v", err)
				}
				goconfig.ResetConfig()
			},
		},
		{
			name:        "Invalid config file",
			expectError: true,
			setup: func() {
				tmpFile, err := os.CreateTemp("", "invalid_config_*.ini")
				if err != nil {
					t.Fatalf("Failed to create temp file: %v", err)
				}
				defer func() {
					if err := os.Remove(tmpFile.Name()); err != nil {
						t.Errorf("Failed to remove temp file: %v", err)
					}
				}()
				if _, err := tmpFile.WriteString("invalid config content"); err != nil {
					t.Fatalf("Failed to write to temp file: %v", err)
				}
				if err := tmpFile.Close(); err != nil {
					t.Fatalf("Failed to close temp file: %v", err)
				}
				if err := os.Setenv("RMBASE_FILE_CFG", tmpFile.Name()); err != nil {
					t.Fatalf("Failed to set RMBASE_FILE_CFG: %v", err)
				}
				goconfig.ResetConfig()
			},
		},
		{
			name:        "Log initialization error with invalid config",
			expectError: true,
			setup: func() {
				if err := os.Setenv("RMBASE_LOG_LEVEL", "invalid"); err != nil {
					t.Fatalf("Failed to set RMBASE_LOG_LEVEL: %v", err)
				}
				if err := os.Setenv("RMBASE_LOG_FILE", "/invalid/path/log.txt"); err != nil {
					t.Fatalf("Failed to set RMBASE_LOG_FILE: %v", err)
				}
				goconfig.ResetConfig()
			},
		},
		{
			name:        "Log initialization error with invalid log level",
			expectError: true,
			setup: func() {
				if err := os.Setenv("RMBASE_LOG_LEVEL", "999"); err != nil {
					t.Fatalf("Failed to set RMBASE_LOG_LEVEL: %v", err)
				}
				goconfig.ResetConfig()
			},
		},
		{
			name:        "Log initialization error with invalid log file",
			expectError: true,
			setup: func() {
				if err := os.Setenv("RMBASE_LOG_FILE", "/invalid/path/log.txt"); err != nil {
					t.Fatalf("Failed to set RMBASE_LOG_FILE: %v", err)
				}
				goconfig.ResetConfig()
			},
		},
		{
			name:        "Log initialization error with invalid log config",
			expectError: true,
			setup: func() {
				if err := os.Setenv("RMBASE_LOG_LEVEL", "invalid"); err != nil {
					t.Fatalf("Failed to set RMBASE_LOG_LEVEL: %v", err)
				}
				if err := os.Setenv("RMBASE_LOG_FILE", "/invalid/path/log.txt"); err != nil {
					t.Fatalf("Failed to set RMBASE_LOG_FILE: %v", err)
				}
				if err := os.Setenv("RMBASE_LOG_FORMAT", "invalid"); err != nil {
					t.Fatalf("Failed to set RMBASE_LOG_FORMAT: %v", err)
				}
				goconfig.ResetConfig()
			},
		},
		{
			name:        "Log initialization error with invalid log level and file",
			expectError: true,
			setup: func() {
				if err := os.Setenv("RMBASE_LOG_LEVEL", "invalid"); err != nil {
					t.Fatalf("Failed to set RMBASE_LOG_LEVEL: %v", err)
				}
				if err := os.Setenv("RMBASE_LOG_FILE", "/invalid/path/log.txt"); err != nil {
					t.Fatalf("Failed to set RMBASE_LOG_FILE: %v", err)
				}
				if err := os.Setenv("RMBASE_LOG_FORMAT", "invalid"); err != nil {
					t.Fatalf("Failed to set RMBASE_LOG_FORMAT: %v", err)
				}
				if err := os.Setenv("RMBASE_LOG_MAXSIZE", "invalid"); err != nil {
					t.Fatalf("Failed to set RMBASE_LOG_MAXSIZE: %v", err)
				}
				goconfig.ResetConfig()
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := os.Unsetenv("RMBASE_FILE_CFG"); err != nil {
				t.Fatalf("Failed to unset RMBASE_FILE_CFG: %v", err)
			}
			if err := os.Unsetenv("RMBASE_LOG_LEVEL"); err != nil {
				t.Fatalf("Failed to unset RMBASE_LOG_LEVEL: %v", err)
			}
			if err := os.Unsetenv("RMBASE_LOG_FILE"); err != nil {
				t.Fatalf("Failed to unset RMBASE_LOG_FILE: %v", err)
			}
			if err := os.Unsetenv("RMBASE_LOG_FORMAT"); err != nil {
				t.Fatalf("Failed to unset RMBASE_LOG_FORMAT: %v", err)
			}
			if err := os.Unsetenv("RMBASE_LOG_MAXSIZE"); err != nil {
				t.Fatalf("Failed to unset RMBASE_LOG_MAXSIZE: %v", err)
			}
			goconfig.ResetConfig()

			if tt.setup != nil {
				tt.setup()
			}

			err := InitBase()
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
