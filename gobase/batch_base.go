package batch_base

import (
	"fmt"
	"os"

	goconfig "github.com/real-rm/goconfig"
	golog "github.com/real-rm/golog"
	gomail "github.com/real-rm/gomail"
	gosms "github.com/real-rm/gosms"
)

// hostnameFunc is a variable to store the hostname function for testing
var hostnameFunc = os.Hostname

// getMailSender is a variable to store the mail sender function for testing
var getMailSender = gomail.GetSendMailObj

// newSMSEngine is a variable to allow injection for testing
var newSMSEngine = gosms.NewTwilioEngine

// configStringFunc is a variable to allow mocking the ConfigString function for testing
var configStringFunc = goconfig.ConfigString

// configFunc is a variable to allow mocking the Config function for testing
var configFunc = goconfig.Config

// InitBase initializes common components like config and logging
func InitBase() error {
	// Load configuration
	if err := goconfig.LoadConfig(); err != nil {
		return fmt.Errorf("failed to load config: %v", err)
	}

	// Initialize logging
	if err := golog.InitLog(); err != nil {
		return fmt.Errorf("failed to initialize logging: %v", err)
	}

	return nil
}

// ErrorNotifyEmail sends error notification via email
func ErrorNotifyEmail(message string, to []string, batchFileName string) error {
	mailer, err := getMailSender()
	if err != nil {
		errMsg := fmt.Sprintf("failed to get send mail object: %v", err)
		golog.Error(errMsg, "error", err)
		return fmt.Errorf("%s", errMsg)
	}
	hostname := GetHostname()
	if hostname == "" || message == "" || batchFileName == "" {
		errMsg := fmt.Sprintf("hostname or message or batchFileName is not set, hostname: %s, message: %s, batchFileName: %s", hostname, message, batchFileName)
		golog.Error(errMsg)
		return fmt.Errorf("%s", errMsg)
	}
	body := fmt.Sprintf("%s at %s in %s", message, hostname, batchFileName)
	// Create email message
	email := &gomail.EmailMessage{
		From:     "<EMAIL>",
		To:       to,
		Subject:  "RealMaster APP Status Notify",
		Text:     body,
		HTML:     "<p>" + body + "</p>",
		Priority: true,
	}

	// Send email using mock engine if configured
	if _, ok := mailer.MockMail.(*gomail.MockMailEngine); ok {
		err = mailer.SendMail("mockmail", email)
	} else {
		// Send email using SESH engine
		engine := "SES"
		if goconfig.Config("mailEngine.mockMail.mock") == "true" {
			engine = "mockmail"
		}
		err = mailer.SendMail(engine, email)
	}
	if err != nil {
		errMsg := fmt.Sprintf("failed to send error notification email: %v", err)
		golog.Error(errMsg, "error", err)
		return fmt.Errorf("%s", errMsg)
	}
	return nil
}

// ErrorNotifySMS sends error notification via SMS
func ErrorNotifySMS(message string, batchFileName string) error {
	developer_mode := configFunc("serverBase.developer_mode")
	if developer_mode == "true" {
		return nil
	}
	// get twilio sid and authToken
	sid, _ := configStringFunc("twilio.sid")
	authToken, _ := configStringFunc("twilio.authToken")
	if sid == "" || authToken == "" {
		errMsg := fmt.Sprintf("twilio sid or authToken is not set, sid: %s, authToken: %s", sid, authToken)
		golog.Error(errMsg)
		return fmt.Errorf("%s", errMsg)
	}
	// get to, from, body
	to, _ := configStringFunc("contact.alertSMS")
	if to == "" {
		to = "6472223733"
	}
	from := "+19056142609"
	hostname := GetHostname()
	if message == "" || hostname == "" || batchFileName == "" {
		errMsg := fmt.Sprintf("message or hostname or batchFileName is not set, message: %s, hostname: %s, batchFileName: %s", message, hostname, batchFileName)
		golog.Error(errMsg)
		return fmt.Errorf("%s", errMsg)
	}
	body := fmt.Sprintf("%s at %s in %s", message, hostname, batchFileName)
	// engine
	engine, err := newSMSEngine(sid, authToken)
	if err != nil {
		errMsg := fmt.Sprintf("failed to create SMS engine: %v", err)
		golog.Error(errMsg, "error", err)
		return fmt.Errorf("%s", errMsg)
	}
	// sender
	sender, err := gosms.NewSMSSender(engine)
	if err != nil {
		errMsg := fmt.Sprintf("failed to create SMS sender: %v", err)
		golog.Error(errMsg, "error", err)
		return fmt.Errorf("%s", errMsg)
	}
	// send SMS
	opt := gosms.SendOption{
		To:      to,
		From:    from,
		Message: body,
	}
	if err := sender.Send(opt); err != nil {
		errMsg := fmt.Sprintf("failed to send error notification SMS: %v", err)
		golog.Error(errMsg, "error", err)
		return fmt.Errorf("%s", errMsg)
	}
	golog.Infof("SMS sent to %s, message: %s", to, body)
	return nil
}

// GetHostname returns the hostname of the machine
func GetHostname() string {
	hostname, err := hostnameFunc()
	if err != nil {
		golog.Error("Failed to get hostname", "error", err)
		return ""
	}
	return hostname
}

// Exit exits the program with an error message
func Exit(err interface{}, code ...int) {
	if err != nil {
		golog.Error("Program exit with error", "error", err)
	}
	if len(code) > 0 {
		os.Exit(code[0])
	}
	os.Exit(1)
}
