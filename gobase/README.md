# GoBase

GoBase is a Go library that provides common functionality for batch processing applications, including error handling, logging, email notifications, and SMS alerts.

## Features

- **Error Handling**: Comprehensive error handling with email and SMS notifications
- **Logging**: Configurable logging system with multiple log levels and formats
- **Email Notifications**: Support for both mock and real email engines (SES)
- **SMS Alerts**: Integration with <PERSON>wilio for SMS notifications
- **Configuration**: Flexible configuration system with environment variable support

## Installation

```bash
go get github.com/real-rm/gobase
```

## Configuration

The library uses a configuration file (INI format) and environment variables. Here's an example configuration:

```ini
[serverBase]
developer_mode = false

[mailEngine]
mailEngine = mockmail
mockMail.mock = true
mockMail.verbose = 1
mockMail.defaultEmail = <EMAIL>

[twilio]
sid = your_twilio_sid
authToken = your_twilio_auth_token

[contact]
alertSMS = 1234567890

[log]
level = info
file = /path/to/log/file.log
format = json
maxSize = 100
```

### Environment Variables

You can override configuration values using environment variables:

```bash
RMBASE_SERVERBASE_DEVELOPER_MODE=false
RMBASE_TWILIO_SID=your_twilio_sid
RMBASE_TWILIO_AUTHTOKEN=your_twilio_auth_token
RMBASE_LOG_LEVEL=info
RMBASE_LOG_FILE=/path/to/log/file.log
```

## Usage

### Basic Setup

```go
package main

import (
    "github.com/real-rm/gobase"
)

func main() {
    // Initialize base components (config and logging)
    if err := gobase.InitBase(); err != nil {
        gobase.Exit(err)
    }
    
    // Your application code here
}
```

### Error Notifications

```go
// Send error notification via email
err := gobase.ErrorNotifyEmail(
    "Error message",
    []string{"<EMAIL>"},
    "batch_file.go",
)

// Send error notification via SMS
err := gobase.ErrorNotifySMS(
    "Error message",
    "batch_file.go",
)

// exit
gobase.Exit()
```

### Logging

The library automatically initializes logging based on your configuration. You can use the standard Go logging functions:

```go
import "github.com/real-rm/golog"

golog.Info("Info message")
golog.Error("Error message", "error", err)
```

## Testing

The library includes comprehensive test coverage. To run the tests:

```bash
go test ./...
```

To run tests with coverage:

```bash
go test -cover ./...
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [gomail](https://github.com/real-rm/gomail) - Email handling
- [gosms](https://github.com/real-rm/gosms) - SMS handling
- [golog](https://github.com/real-rm/golog) - Logging
- [goconfig](https://github.com/real-rm/goconfig) - Configuration management