# Golang Naming Conventions

## 1. Package Names
- Use short, concise, lowercase names (e.g., `http`, `json`, `util`)
- Avoid underscores or mixedCaps
- Package name should match the directory name
- Choose meaningful but simple names that won't conflict with common variable names

## 2. Variable Names
- Use camelCase for internal variables (`myVariable`)
- Use PascalCase for exported variables (`ExportedVariable`)
- Keep names descriptive but concise
- Common short names, like counters (i, j, k) or readers (r), are NOT acceptable. Please use descriptive names.
- Avoid abbreviations unless they're well known (HTTP, JSON, etc.)

## 3. Function Names
- Use camelCase for internal functions (`myFunction()`)
- Use PascalCase for exported functions (`ExportedFunction()`)
- Name should describe what the function does
- Common prefixes:
  - `New` for constructors (`NewClient()`)
  - `Get/Set` for accessors/mutators
  - `Is/Has` for boolean functions

## 4. Interface Names
- Use PascalCase
- Often end in `-er` (`<PERSON>`, `Writer`, `Stringer`)
- Single method interfaces should use method name + "er" (`Reader` for `Read`)

## 5. Struct Names
- Use PascalCase for exported structs
- Use camelCase for internal structs
- Field names follow same rules as variables

## 6. Constants
- Use ALL_CAPS for constants

## 7. Error Values
- Error strings should not be capitalized
- Should not end with punctuation
- Should be as concise as possible
- Common format: `errors.New("something went wrong")`

## 8. Test Functions
- Begin with `Test`
- Follow with the name of the function being tested
- Use underscores to separate logical components
- Example: `TestMyFunction_ValidInput`

## 9. File Names
- Use lowercase
- Use underscores to separate words
- Test files end in `_test.go`
- One file per logical component

## 10. Best Practices
- Avoid redundancy in names (e.g., `customer.CustomerID` should be `customer.ID`)
- Use meaningful names that describe purpose
- Shorter names for smaller scopes
- Longer, more descriptive names for larger scopes
- Be consistent with the standard library's naming conventions

## 11. Comments
- Begin with the name of the thing being documented
- Use complete sentences
- End with a period
- For exported items, must start with the item name

Example:
