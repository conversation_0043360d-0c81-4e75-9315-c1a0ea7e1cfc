# Data Structure Replace Logic

## When the same 'if' statement is used for multiple purposes and the logic is complex, replace it with a data structure driven approach

### Example

```go
if mode == 'A' {
    doSomethingA1()
} else if mode == 'B' {
    doSomethingB1()
}

if mode == 'A' {
    doSomethingA2()
}
```

### Replace with a map

```go
actions := map[rune][]func(){
    'A': {doSomethingA1, doSomethingA2},
    'B': {doSomethingB1},
}

for _, action := range actions[mode] {
    action()
}
```
