# VSCode Setup Guide

## Installing gomarkdoc

Install the gomarkdoc tool for generating API documentation:

```bash
# Install the latest version
go install github.com/princjef/gomarkdoc/cmd/gomarkdoc@latest

# Generate API documentation
gomarkdoc --output api.md .
```

## Setting up Git Pre-commit Hooks[Or you can run gomarkdoc+lint+test by yourself]

Set up pre-commit hooks to run API doc generation, linting, and unit tests before each commit:

```bash
# Create pre-commit hook file
touch .git/hooks/pre-commit

# Make it executable
chmod +x .git/hooks/pre-commit

# Edit the pre-commit hook
nano .git/hooks/pre-commit
```

Add the following content to the pre-commit hook:

```bash
#!/bin/bash
echo "Running gomarkdoc before commit..."
gomarkdoc --output api.md .

echo "Running lint..."
golangci-lint run

echo "Running tests..."
go test -v
```

## VSCode Test Configuration

VSCode supports running individual test functions through the interface. Some unit tests may require specific environment settings.

You can configure test environment variables in `.vscode/settings.json`:

```json
{
    "go.testEnvVars": {
        "RMBASE_FILE_CFG": "/home/<USER>/github/goapp/logs/app.test.ini"
    }
}
```



## Markdown Preview

If you encounter an error while previewing `api.md`, you can install the **Office Viewer** extension to resolve the issue.
