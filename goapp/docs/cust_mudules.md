
## how to customize modules?
https://dev.to/ansu/building-and-consuming-custom-packages-in-go-a-complete-guide-15ce
### step 1
```
mkdir mymodule
cd mymodule
go mod init github.com/real-rm/mymodule
go get github.com/spf13/viper
vi mymodule.go

```

### step 2
```

package mymodule

import (
 "fmt"
 "github.com/spf13/viper"
)

func ReadConfig(configFilePath string) error {
 // Initialize a new viper configuration object
 viper.SetConfigFile(configFilePath)
 err := viper.ReadInConfig()
 if err != nil {
  return fmt.Errorf("error reading config file: %s", err)
 }
 return nil
}
```

### step 3
```
go build
git commit -am 'init(mymodule): init my module'
git push origin main
```

### step 4
```
export GOPRIVATE=*
vi ~/.gitconfig
[url "ssh://**************"]
    insteadOf = https://github.com
go get github.com/real-rm/mymodule
vi app.go
```

### step 5
```
import (
    "fmt"

    mymodule "github.com/real-rm/mymodule"
)
mymodule.ReadConfig("config.yaml")
```