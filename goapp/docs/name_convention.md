# Some naming conventions

## Common verb

- `get` fetch data without modifying the data
- `set` set data simply, without triggering other actions
- `update` update data, implicitly trigger other actions
- `save` delete data
- `build` build a data structure from other data or data structures. i.e. build config from env
- `generate` generate a data or structure from scratch or nothing. i.e. generate a new id/hash/token
- `parse` parse a data or structure from a string
- `validate` validate a string, data or structure
- `convert` convert a data or structure to another data or structure
- `transform` transform a data or structure to another data or structure
- `format` format a data or structure to **a string**

## Common noun

- `ts` created timestamp from source
- `mt` modified timestamp from source
- `_mt` modified timestamp for this record
- `_ts` created timestamp for this record


