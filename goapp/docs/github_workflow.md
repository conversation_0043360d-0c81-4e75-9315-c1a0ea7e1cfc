# GitHub Workflow Setup Guide

## Prerequisites
Before setting up the GitHub workflow, ensure you have the following:
- **GitHub Actions enabled** for your repository.
- **Organization-level secret** `WORKFLOW_TOKEN_GITHUB` created.
- **Actions permissions** set to allow all workflows.

## Setting Actions Permissions
To avoid encountering the error `Go CI: No jobs were run`, you must configure the repository settings:
1. Navigate to your **GitHub repository**.
2. Go to **Settings** > **Actions** > **General**.
3. Under **Actions permissions**, select **Allow all actions and reusable workflows**.
4. Save the settings.

## GitHub Workflow Configuration
Create a workflow file in your repository under `.github/workflows/ci.yml` with the following content:

```yaml
name: CI Workflow

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
  workflow_dispatch: # Settings for manually triggered workflows

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.WORKFLOW_TOKEN_GITHUB }}
      
      - name: Configure Git for private repos
        run: |
          git config --global url."https://${{ secrets.WORKFLOW_TOKEN_GITHUB }}@github.com/".insteadOf "https://github.com/"

      - name: Configure GOPRIVATE
        run: echo "GOPRIVATE=github.com/real-rm/*" >> $GITHUB_ENV
      
      - name: Build Project
        run: |
          go mod tidy
          go build -v ./...
```

## Organization Secrets Configuration
1. Navigate to **GitHub Organization Settings**.
2. Go to **Security** > **Secrets and variables** > **Actions**.
3. Click **New repository secret**.
4. Set the **Name** as `WORKFLOW_TOKEN_GITHUB`.
5. Set the **Value** as a valid **GitHub Personal Access Token (PAT)** with `repo` scope.
6. Click **Save secret**.

This setup ensures that:
- The workflow can access private repositories via `GOPRIVATE`.
- The correct authentication token is used for checking out the repository.
- The repository allows all GitHub Actions to execute without restrictions.

## Troubleshooting
- If the workflow fails with `Go CI: No jobs were run`, check if **Actions permissions** are correctly set.
- Ensure the `WORKFLOW_TOKEN_GITHUB` secret exists and has the correct access scope.
- Verify that the `GOPRIVATE` variable is correctly set for private Go modules.

This configuration will enable your GitHub Actions workflow to function correctly with **organization secrets** and **private Go modules**.

