<mxfile host="65bd71144e">
    <diagram id="qfeiB93r9Nyb9wTXFTtM" name="Page-1">
        <mxGraphModel dx="1882" dy="1972" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" background="none" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="5" value="1:n" style="edgeStyle=none;html=1;rounded=0;" parent="1" source="2" target="4" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="&lt;h1&gt;Country&lt;/h1&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-140" y="10" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="1:n" style="edgeStyle=none;html=1;rounded=0;" parent="1" source="4" target="6" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="1:n" style="edgeStyle=none;html=1;entryX=0;entryY=0.25;entryDx=0;entryDy=0;rounded=0;" parent="1" source="4" target="8" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="1:n" style="edgeStyle=none;html=1;rounded=0;" parent="1" source="4" target="19" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="1:n" style="edgeStyle=none;html=1;entryX=0.453;entryY=-0.012;entryDx=0;entryDy=0;entryPerimeter=0;rounded=0;" parent="1" source="4" target="16" edge="1">
                    <mxGeometry x="-0.3171" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="24" value="1:n" style="edgeStyle=none;html=1;entryX=0.25;entryY=0;entryDx=0;entryDy=0;rounded=0;" parent="1" source="4" target="17" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="46" value="1:n" style="edgeStyle=none;html=1;entryX=0;entryY=0;entryDx=0;entryDy=0;rounded=0;" parent="1" source="4" target="30" edge="1">
                    <mxGeometry x="-0.3895" y="1" relative="1" as="geometry">
                        <mxPoint x="160" y="670" as="targetPoint"/>
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="71" value="1:n" style="edgeStyle=none;html=1;entryX=0.75;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="4" target="70">
                    <mxGeometry x="-0.3609" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="4" value="&lt;h1&gt;Prov&lt;/h1&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="30" y="80" width="70" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="1:n" style="edgeStyle=none;html=1;rounded=0;" parent="1" source="6" target="8" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="27" value="0/1:n" style="edgeStyle=none;html=1;entryX=0;entryY=0;entryDx=0;entryDy=0;rounded=0;" parent="1" source="6" target="15" edge="1">
                    <mxGeometry x="-0.4333" y="4" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="59" value="0/1:n" style="edgeStyle=none;html=1;entryX=0.25;entryY=0;entryDx=0;entryDy=0;rounded=0;" parent="1" source="6" target="35" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="60" value="0/1:n" style="edgeStyle=none;html=1;entryX=0.167;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.07;exitY=1.075;exitDx=0;exitDy=0;exitPerimeter=0;rounded=0;" parent="1" source="6" target="30" edge="1">
                    <mxGeometry x="-0.3137" y="2" relative="1" as="geometry">
                        <mxPoint y="1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="6" value="&lt;h1&gt;Region&lt;/h1&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="150" y="190" width="100" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="1:*" style="edgeStyle=none;html=1;entryX=0.132;entryY=0.013;entryDx=0;entryDy=0;entryPerimeter=0;rounded=0;" parent="1" source="8" target="15" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="57" value="n:m" style="edgeStyle=none;html=1;entryX=0.75;entryY=0;entryDx=0;entryDy=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;rounded=0;" parent="1" source="8" target="35" edge="1">
                    <mxGeometry x="-0.0211" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="61" style="edgeStyle=none;html=1;rounded=0;" parent="1" source="8" target="30" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="66" value="n:m" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];rounded=0;" parent="61" vertex="1" connectable="0">
                    <mxGeometry x="0.0774" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="8" value="&lt;h1&gt;CityTown&lt;/h1&gt;&lt;div&gt;An administration Area.&lt;/div&gt;&lt;div&gt;A city may include multiple sub-city or townships.&lt;/div&gt;&lt;div&gt;A propery belong at lease one city ow township&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="425" y="215" width="165" height="130" as="geometry"/>
                </mxCell>
                <mxCell id="37" value="0/1:n" style="edgeStyle=none;html=1;entryX=1;entryY=0.168;entryDx=0;entryDy=0;entryPerimeter=0;rounded=0;" parent="1" source="15" target="35" edge="1">
                    <mxGeometry x="0.4655" y="4" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="42" value="n:m&lt;br&gt;(belong to multiple cmty &lt;br&gt;of different boards)" style="edgeStyle=none;html=1;entryX=1;entryY=0;entryDx=0;entryDy=0;rounded=0;" parent="1" source="15" target="30" edge="1">
                    <mxGeometry x="0.1626" y="7" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="85" value="*:n" style="edgeStyle=none;html=1;" edge="1" parent="1" source="15" target="84">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="&lt;h1&gt;Cmty&lt;/h1&gt;&lt;div&gt;Real Estate Board Community.&lt;/div&gt;&lt;div&gt;The same location can belong to multiple communities in different boards.&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="490" y="400" width="190" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="34" value="0/1: n" style="edgeStyle=none;html=1;exitX=0.75;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.25;entryDx=0;entryDy=0;rounded=0;" parent="1" source="16" target="30" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="160" y="690" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="36" value="" style="edgeStyle=none;html=1;entryX=0;entryY=0.25;entryDx=0;entryDy=0;rounded=0;" parent="1" source="16" target="35" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="40" value="0/1:n" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];rounded=0;" parent="36" vertex="1" connectable="0">
                    <mxGeometry x="-0.1889" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="87" style="edgeStyle=none;html=1;entryX=0;entryY=0.25;entryDx=0;entryDy=0;" edge="1" parent="1" source="16" target="84">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="88" value="0/1:n" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="87">
                    <mxGeometry x="-0.0312" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="16" value="&lt;h1&gt;CensusCmty&lt;/h1&gt;&lt;p&gt;Census smalles community&lt;/p&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-180" y="470" width="160" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="45" value="0/1:n" style="edgeStyle=none;html=1;entryX=-0.042;entryY=0.12;entryDx=0;entryDy=0;entryPerimeter=0;rounded=0;" parent="1" source="17" target="30" edge="1">
                    <mxGeometry x="-0.2712" y="5" relative="1" as="geometry">
                        <mxPoint x="-1" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="90" style="edgeStyle=none;html=1;entryX=-0.012;entryY=0.121;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="17" target="84">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="91" value="*:n" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="90">
                    <mxGeometry x="-0.8432" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="92" value="0/1:n" style="edgeStyle=none;html=1;entryX=-0.015;entryY=0.157;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="17" target="35">
                    <mxGeometry x="-0.2862" y="-3" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="17" value="&lt;h1&gt;RmCmty&lt;/h1&gt;&lt;div&gt;We may define customized community area&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="-160" y="310" width="160" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="21" value="1:*" style="edgeStyle=none;html=1;entryX=0.25;entryY=0;entryDx=0;entryDy=0;rounded=0;" parent="1" source="19" target="15" edge="1">
                    <mxGeometry x="-0.15" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="48" value="1:n" style="edgeStyle=none;html=1;rounded=0;" parent="1" source="19" target="47" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="55" value="1:n" style="edgeStyle=none;html=1;entryX=0;entryY=0;entryDx=0;entryDy=0;rounded=0;" parent="1" source="19" target="51" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="&lt;h1&gt;RealtyBoard&lt;/h1&gt;&lt;div&gt;Each board has its own rules and regulation, and User Term&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="655" y="150" width="170" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="44" value="" style="edgeStyle=none;html=1;entryX=-0.016;entryY=0.164;entryDx=0;entryDy=0;entryPerimeter=0;rounded=0;" parent="1" source="30" target="43" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="53" value="1:*" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];rounded=0;" parent="44" vertex="1" connectable="0">
                    <mxGeometry x="-0.2047" y="2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="89" value="n:m" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="30" target="84">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="30" value="&lt;h1&gt;Property&lt;/h1&gt;&lt;div&gt;A house, Condo unit, a factory or a commerical unit&lt;/div&gt;&lt;div&gt;Key: uaddrp or geohash + unit for assignment&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="160" y="700" width="120" height="150" as="geometry"/>
                </mxCell>
                <mxCell id="39" value="0/1:n" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.25;exitY=1;exitDx=0;exitDy=0;rounded=0;" parent="1" source="35" target="30" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="86" value="*:n" style="edgeStyle=none;html=1;entryX=0;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="35" target="84">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="35" value="&lt;h1&gt;Building&lt;/h1&gt;&lt;div&gt;A condo or townhouse complex, which includes many condo or townhouse units&lt;/div&gt;&lt;div&gt;Key: uaddr or geohash for assignment&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="210" y="320" width="130" height="150" as="geometry"/>
                </mxCell>
                <mxCell id="52" value="" style="edgeStyle=none;html=1;rounded=0;" parent="1" source="43" target="51" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="54" value="n:m&lt;br&gt;One listing may include &lt;br&gt;multiple addresses or sale types.&lt;br&gt;We split them into multiple merged listings" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];rounded=0;" parent="52" vertex="1" connectable="0">
                    <mxGeometry x="-0.1001" relative="1" as="geometry">
                        <mxPoint x="13" y="-40" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="43" value="&lt;h1&gt;MergedListing&lt;/h1&gt;&lt;div&gt;A merged listing entity for a property in a certain time period for the same kind of transaction from the same brokerage.&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;Key: property + saleRentType + startDate + unifiedBrokerageName&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="330" y="760" width="190" height="180" as="geometry"/>
                </mxCell>
                <mxCell id="56" value="1:n" style="edgeStyle=none;html=1;rounded=0;" parent="1" source="47" target="51" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="47" value="&lt;h1&gt;Brokerage&lt;/h1&gt;&lt;div&gt;A real estate Company&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="750" y="320" width="140" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="51" value="&lt;h1&gt;Listing&lt;/h1&gt;&lt;div&gt;A single listing from a board or an assignment&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="750" y="740" width="160" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="65" value="0/1:n" style="edgeStyle=none;html=1;exitX=1;exitY=0.25;exitDx=0;exitDy=0;rounded=0;" parent="1" source="8" target="8" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="68" value="&lt;h1&gt;Legend&lt;/h1&gt;&lt;div&gt;n,m: 1 or many&lt;/div&gt;&lt;div&gt;0/1:&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;0 or 1&lt;/div&gt;&lt;div&gt;*:&amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;0 or many&lt;/div&gt;&lt;div&gt;Sold line Arrow: from greater or parent to minor or child&lt;/div&gt;&lt;div&gt;1:n :&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;One Parent to Multiple Child&lt;/div&gt;&lt;div&gt;0/1:n :&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;One or Non Parent to Multiple Child.&lt;/div&gt;&lt;div&gt;Dot line arraw: watch&lt;/div&gt;" style="text;html=1;strokeColor=default;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="390" y="-20" width="370" height="150" as="geometry"/>
                </mxCell>
                <mxCell id="73" value="n:m" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="69" target="35">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="74" style="edgeStyle=none;html=1;entryX=-0.025;entryY=0.35;entryDx=0;entryDy=0;entryPerimeter=0;exitX=1;exitY=0.25;exitDx=0;exitDy=0;" edge="1" parent="1" source="69" target="30">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="75" value="n:m" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="74">
                    <mxGeometry x="-0.0753" y="-2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="69" value="&lt;h1&gt;School&lt;/h1&gt;&lt;div&gt;Merged school entity&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="-120" y="680" width="130" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="72" value="1:n" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="70" target="69">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="70" value="&lt;h1&gt;SchBoard&lt;/h1&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="-130" y="610" width="130" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="77" style="edgeStyle=none;html=1;entryX=-0.033;entryY=0.43;entryDx=0;entryDy=0;entryPerimeter=0;dashed=1;" edge="1" parent="1" source="76" target="30">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="78" style="edgeStyle=none;html=1;entryX=0;entryY=0.75;entryDx=0;entryDy=0;dashed=1;" edge="1" parent="1" source="76" target="35">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="79" style="edgeStyle=none;html=1;entryX=0;entryY=0.25;entryDx=0;entryDy=0;dashed=1;" edge="1" parent="1" source="76" target="15">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="80" style="edgeStyle=none;html=1;entryX=0.438;entryY=0.994;entryDx=0;entryDy=0;entryPerimeter=0;dashed=1;" edge="1" parent="1" source="76" target="69">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="81" style="edgeStyle=none;html=1;entryX=0.25;entryY=1;entryDx=0;entryDy=0;exitX=0.25;exitY=0;exitDx=0;exitDy=0;dashed=1;" edge="1" parent="1" source="76" target="16">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="82" style="edgeStyle=none;html=1;entryX=0;entryY=1;entryDx=0;entryDy=0;exitX=0;exitY=0;exitDx=0;exitDy=0;dashed=1;" edge="1" parent="1" source="76" target="17">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="83" style="edgeStyle=none;html=1;entryX=0;entryY=0.708;entryDx=0;entryDy=0;entryPerimeter=0;dashed=1;" edge="1" parent="1" source="76" target="43">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="76" value="&lt;h1&gt;User&lt;/h1&gt;&lt;div&gt;Watch: CityTown, Cmty, School, Building, Property or MergedListing&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="-157.5" y="840" width="117.5" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="84" value="&lt;h1&gt;Happening&lt;/h1&gt;&lt;div&gt;An incident or criminal event&lt;/div&gt;" style="text;html=1;strokeColor=none;fillColor=none;spacing=5;spacingTop=-20;whiteSpace=wrap;overflow=hidden;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="540" y="610" width="170" height="70" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>