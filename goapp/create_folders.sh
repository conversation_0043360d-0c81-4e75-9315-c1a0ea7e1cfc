#!/bin/bash

# Create root directories
mkdir -p docs/change_requests
mkdir -p build/{tools,bin,assets/{css,js,images}}
mkdir -p src/{lib,controller,entities,batch/{mls_import/{lib/{reso,rets},trb,car},prop},test}

# Create README files
touch docs/README.md
touch src/README.md
touch src/lib/README.md
touch src/controller/README.md
touch src/entities/README.md
touch src/batch/mls_import/lib/reso/README.md
touch src/batch/mls_import/lib/rets/README.md
touch src/batch/mls_import/trb/README.md
touch src/batch/mls_import/car/README.md
touch src/test/README.md
touch README.md

# Create source files
touch src/lib/{db,helper}.go
touch src/controller/{controller,controller_test}.go
touch src/entities/{user,post}.go
touch src/batch/mls_import/lib/reso/{reso,reso_test}.go
touch src/batch/mls_import/lib/rets/{rets,rets_test}.go
touch src/batch/mls_import/trb/{trb_import,trb_import_test}.go
touch src/batch/mls_import/car/{car_import,car_import_test}.go
touch src/batch/prop/{abc,abc_test}.go
touch src/main.go
touch src/test/{test,web_app_test}.go

# Create other files
touch build/assets/favicon.ico
touch .gitignore

# Create Makefile with basic targets
cat > Makefile << 'EOF'
build: # build binaries
test: # run all tests
clean: # clean compiled files
EOF

# Create sample data directories
mkdir -p src/batch/mls_import/trb/sample_data
mkdir -p src/batch/mls_import/car/sample_data

echo "Directory structure and files created successfully!"
