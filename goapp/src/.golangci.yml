run:
  timeout: 5m

issues:
  exclude-dirs:
    - "/usr/local/go/src" # Skip standard library directory
    - "vendor"            # Skip vendor directory
    - "third_party"       # Skip third-party code
    - ".*/go/pkg/mod/.*"  # Skip all Go modules cache directories

  exclude-files:
    - ".*_test.go"        # Optional: Skip all *_test.go files

  exclude-use-default: false
  max-issues-per-linter: 50
  max-same-issues: 5

  exclude-rules:
    - path: "generated.go"
      linters:
        - "gofmt"
    - path: "batch/.*"      # ignore batch directory for typecheck
      linters:
        - "typecheck"
    - path: "lib/file_server.go"  # ignore range over int warning
      linters:
        - "gosimple"
      text: "for loop can be modernized using range over int"

linters:
  enable:
    - govet
    - errcheck
    - staticcheck
    - gosimple
    - unused
    - ineffassign
    - typecheck
    - gofmt
    - misspell
    - unconvert
    - gocritic
    # - depguard

linters-settings:
  errcheck:
    check-type-assertions: true
    check-blank: true

  staticcheck:
    checks: ["all"]

  misspell:
    locale: "US"