// Package db provides a layer of proxy for common collection operations, which measure the response time
package lib

import (
	"bytes"
	"context"
	"crypto/tls"
	"crypto/x509"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/elastic/go-elasticsearch/v8"
	"github.com/elastic/go-elasticsearch/v8/esapi"
	"github.com/real-rm/goconfig"
	golog "github.com/real-rm/golog"
)

var (
	esClient     *elasticsearch.Client
	esVerbose    int
	slowQueryLog = 2 * time.Second // Same threshold as MongoDB
)

type ESConfig struct {
	Cert     string `toml:"cert"`
	Host     string `toml:"host"`
	Password string `toml:"password"`
	User     string `toml:"user"`
	Verbose  int    `toml:"verbose"`
}

// InitElastic initializes the Elasticsearch client using configuration
func InitElastic() error {
	var cfg ESConfig
	// Read Elasticsearch config
	if esCfg := goconfig.Config("elastic"); esCfg != nil {
		if m, ok := esCfg.(map[string]interface{}); ok {
			cert, ok := m["cert"].(string)
			if !ok {
				return fmt.Errorf("invalid cert type in config")
			}
			cfg.Cert = cert

			host, ok := m["host"].(string)
			if !ok {
				return fmt.Errorf("invalid host type in config")
			}
			cfg.Host = host

			password, ok := m["password"].(string)
			if !ok {
				return fmt.Errorf("invalid password type in config")
			}
			cfg.Password = password

			user, ok := m["user"].(string)
			if !ok {
				return fmt.Errorf("invalid user type in config")
			}
			cfg.User = user

			if verbose, ok := m["verbose"].(int64); ok {
				cfg.Verbose = int(verbose)
			}
		}
	}

	esVerbose = cfg.Verbose

	// Load certificate
	cert, err := os.ReadFile(cfg.Cert)
	if err != nil {
		return fmt.Errorf("error reading certificate: %w", err)
	}

	// Create cert pool and configure TLS
	certPool := x509.NewCertPool()
	certPool.AppendCertsFromPEM(cert)

	esCfg := elasticsearch.Config{
		Addresses: []string{cfg.Host},
		Username:  cfg.User,
		Password:  cfg.Password,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				RootCAs: certPool,
			},
		},
	}

	client, err := elasticsearch.NewClient(esCfg)
	if err != nil {
		return fmt.Errorf("error creating Elasticsearch client: %w", err)
	}

	// Test connection
	res, err := client.Info()
	if err != nil {
		return fmt.Errorf("error connecting to Elasticsearch: %w", err)
	}
	defer func() {
		if err := res.Body.Close(); err != nil {
			golog.Warnf("Error closing response body: %v", err)
		}
	}()

	if res.IsError() {
		return fmt.Errorf("error response from Elasticsearch: %s", res.String())
	}

	esClient = client
	golog.Info("Connected to Elasticsearch", "host", cfg.Host)
	return nil
}

// ESWrapper wraps Elasticsearch operations with monitoring
type ESWrapper struct {
	client *elasticsearch.Client
}

// NewESWrapper creates a new Elasticsearch wrapper
func NewESWrapper() *ESWrapper {
	return &ESWrapper{client: esClient}
}

// logOperation logs slow operations and verbose queries
func (w *ESWrapper) logOperation(start time.Time, op string, req interface{}) {
	duration := time.Since(start)

	if duration > slowQueryLog {
		golog.Warnf("Slow Elasticsearch %s operation (%.2fs): %+v", op, duration.Seconds(), req)
	}
	if esVerbose > 2 {
		golog.Debugf("Elasticsearch %s operation (%.2fs): %+v", op, duration.Seconds(), req)
	}
}

// Index indexes a document
func (w *ESWrapper) Index(ctx context.Context, index string, id string, body interface{}) (*esapi.Response, error) {
	start := time.Now()

	payload, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}

	req := esapi.IndexRequest{
		Index:      index,
		DocumentID: id,
		Body:       bytes.NewReader(payload),
	}

	res, err := req.Do(ctx, w.client)
	w.logOperation(start, "Index", map[string]interface{}{
		"index": index,
		"id":    id,
		"body":  body,
	})

	return res, err
}

// Search performs a search query
func (w *ESWrapper) Search(ctx context.Context, index string, query interface{}) (*esapi.Response, error) {
	start := time.Now()

	payload, err := json.Marshal(query)
	if err != nil {
		return nil, err
	}

	req := esapi.SearchRequest{
		Index: []string{index},
		Body:  bytes.NewReader(payload),
	}

	res, err := req.Do(ctx, w.client)
	w.logOperation(start, "Search", map[string]interface{}{
		"index": index,
		"query": query,
	})

	return res, err
}

// Update updates a document
func (w *ESWrapper) Update(ctx context.Context, index string, id string, body interface{}) (*esapi.Response, error) {
	start := time.Now()

	payload, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}

	req := esapi.UpdateRequest{
		Index:      index,
		DocumentID: id,
		Body:       bytes.NewReader(payload),
	}

	res, err := req.Do(ctx, w.client)
	w.logOperation(start, "Update", map[string]interface{}{
		"index": index,
		"id":    id,
		"body":  body,
	})

	return res, err
}

// Delete deletes a document
func (w *ESWrapper) Delete(ctx context.Context, index string, id string) (*esapi.Response, error) {
	start := time.Now()

	req := esapi.DeleteRequest{
		Index:      index,
		DocumentID: id,
	}

	res, err := req.Do(ctx, w.client)
	w.logOperation(start, "Delete", map[string]interface{}{
		"index": index,
		"id":    id,
	})

	return res, err
}

// Bulk performs bulk operations
func (w *ESWrapper) Bulk(ctx context.Context, body interface{}) (*esapi.Response, error) {
	start := time.Now()

	payload, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}

	req := esapi.BulkRequest{
		Body: bytes.NewReader(payload),
	}

	res, err := req.Do(ctx, w.client)
	w.logOperation(start, "Bulk", body)

	return res, err
}

// CreateIndex creates a new index
func (w *ESWrapper) CreateIndex(ctx context.Context, index string, mapping interface{}) (*esapi.Response, error) {
	start := time.Now()

	payload, err := json.Marshal(mapping)
	if err != nil {
		return nil, err
	}

	req := esapi.IndicesCreateRequest{
		Index: index,
		Body:  bytes.NewReader(payload),
	}

	res, err := req.Do(ctx, w.client)
	w.logOperation(start, "CreateIndex", map[string]interface{}{
		"index":   index,
		"mapping": mapping,
	})

	return res, err
}

// DeleteIndex deletes an index
func (w *ESWrapper) DeleteIndex(ctx context.Context, index string) (*esapi.Response, error) {
	start := time.Now()

	req := esapi.IndicesDeleteRequest{
		Index: []string{index},
	}

	res, err := req.Do(ctx, w.client)
	w.logOperation(start, "DeleteIndex", map[string]interface{}{
		"index": index,
	})

	return res, err
}
