package lib

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/real-rm/goconfig"
)

func TestInitElastic(t *testing.T) {
	content := `
	[elastic]
	host = "https://localhost:9200"
	user = "elastic"
	password = "testpass"
	cert = "/path/to/cert.p12"
	verbose = 3
	`
	tmpfile, err := os.CreateTemp("", "config-goconfig-test.toml")
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	defer os.Remove(tmpfile.Name())
	if _, err := tmpfile.Write([]byte(content)); err != nil {
		t.Fatal(err)
	}
	if err := tmpfile.Close(); err != nil {
		t.Fatal(err)
	}
	os.Setenv("RMBASE_FILE_CFG", tmpfile.Name())
	defer os.Unsetenv("RMBASE_FILE_CFG")
	goconfig.LoadConfig()
	InitElastic()

	// Skip test if certificate doesn't exist
	t.<PERSON><PERSON>("Skipping Elasticsearch tests - requires valid certificate and running instance")
}

func TestESWrapper(t *testing.T) {
	if esClient == nil {
		t.Skip("Elasticsearch client not initialized")
	}

	ctx := context.Background()
	es := NewESWrapper()

	// Test document
	doc := map[string]interface{}{
		"title":   "Test Document",
		"content": "This is a test document",
		"created": time.Now(),
	}

	// Test index creation
	t.Run("CreateIndex", func(t *testing.T) {
		mapping := map[string]interface{}{
			"mappings": map[string]interface{}{
				"properties": map[string]interface{}{
					"title": map[string]interface{}{
						"type": "text",
					},
					"content": map[string]interface{}{
						"type": "text",
					},
					"created": map[string]interface{}{
						"type": "date",
					},
				},
			},
		}

		res, err := es.CreateIndex(ctx, "test-index", mapping)
		if err != nil {
			t.Errorf("CreateIndex() error = %v", err)
		}
		if res.IsError() {
			t.Errorf("CreateIndex() failed: %s", res.String())
		}
	})

	// Test document indexing
	t.Run("Index", func(t *testing.T) {
		res, err := es.Index(ctx, "test-index", "1", doc)
		if err != nil {
			t.Errorf("Index() error = %v", err)
		}
		if res.IsError() {
			t.Errorf("Index() failed: %s", res.String())
		}
	})

	// Test search
	t.Run("Search", func(t *testing.T) {
		query := map[string]interface{}{
			"query": map[string]interface{}{
				"match": map[string]interface{}{
					"title": "Test",
				},
			},
		}

		res, err := es.Search(ctx, "test-index", query)
		if err != nil {
			t.Errorf("Search() error = %v", err)
		}
		if res.IsError() {
			t.Errorf("Search() failed: %s", res.String())
		}
	})

	// Test update
	t.Run("Update", func(t *testing.T) {
		update := map[string]interface{}{
			"doc": map[string]interface{}{
				"content": "Updated content",
			},
		}

		res, err := es.Update(ctx, "test-index", "1", update)
		if err != nil {
			t.Errorf("Update() error = %v", err)
		}
		if res.IsError() {
			t.Errorf("Update() failed: %s", res.String())
		}
	})

	// Test delete
	t.Run("Delete", func(t *testing.T) {
		res, err := es.Delete(ctx, "test-index", "1")
		if err != nil {
			t.Errorf("Delete() error = %v", err)
		}
		if res.IsError() {
			t.Errorf("Delete() failed: %s", res.String())
		}
	})

	// Test delete index
	t.Run("DeleteIndex", func(t *testing.T) {
		res, err := es.DeleteIndex(ctx, "test-index")
		if err != nil {
			t.Errorf("DeleteIndex() error = %v", err)
		}
		if res.IsError() {
			t.Errorf("DeleteIndex() failed: %s", res.String())
		}
	})
}

func TestSlowOperation(t *testing.T) {
	if esClient == nil {
		t.Skip("Elasticsearch client not initialized")
	}

	es := NewESWrapper()
	ctx := context.Background()

	// Test slow query logging
	t.Run("SlowQuery", func(t *testing.T) {
		// Create a slow query by adding a delay
		query := map[string]interface{}{
			"query": map[string]interface{}{
				"match_all": map[string]interface{}{},
			},
			"script_fields": map[string]interface{}{
				"slow_field": map[string]interface{}{
					"script": map[string]interface{}{
						"lang": "painless",
						"source": `
							Thread.sleep(3000);
							return 1;
						`,
					},
				},
			},
		}

		_, _ = es.Search(ctx, "test-index", query)
		// The slow query should trigger a warning log
	})
}
