

Please change the config.go to read a toml file, which is from enviroment variable RMBASE_FILE_CFG. If there is a command line --config [config-file.ini], then the command line take priority.
Provide a API method of config(string) to get a section of the config or a value for the given name. 


------------------------------------------------------------------------------------------------
Please create a mongodb.go in lib.
It reads config like this.
"""
[dbs]
mongo4 = true
v36 = true
verbose = 3

[preDefColls.emailMXFailed]
collName = "email_mx_failed"
dbName = "chome"

[preDefColls.emailWhitelist]
collName = "email_whitelist"
dbName = "chome"

[preDefColls.mailLog]
collName = "mail_log"
dbName = "chome"

  [preDefColls.mailLog.options]
  expireAfterSeconds = 2_592_000 # 30 * 24 * 3600

    [preDefColls.mailLog.options.timeseries]
    metaField = "metadata"
    timeField = "timestamp"

[preDefColls.uuid]
collName = "user_uuid"
dbName = "chome"

[dbs.chome]
uri = "mongodb://d1:d1@127.0.0.1:27017,127.0.0.1:27018/chomeca?authSource=admin&replicaSet=rs0&readPreference=nearest&minPoolSize=1&maxPoolSize=1&tls=false"
"""

It provides interface like this.
mongodb.Database(dbName string) mongoDatabase
mongodb.Coll(collName string, dbName string) mongoCollection
mongoDatabase.Coll(collName string) mongoCollection
mongoCollection shall provide all the normal interface/methods, which add _ts when creating records, and add/update _mt when updating/createing records.
It also monitoring the response time. When the response time is longer than 2 seconds, it'll print warning with query in log.

@mongodb.go Please do not use m,k,v these kind of variable name. Instead, please use self descriptional names.


------------------------------------------------------------------------------------------------

Please create the log.go file in lib.
It reads config from [golog] dir, level, verbose/info/error(file names for these 3 levels), format(text or json).
It uses slog lib to do it's job.
It shall log timestamp in all three files. It shall log file/function/line-number when error.
It provides verbose/debug/info/warn/error/fatal level of log functions or whatever matches the slog standards.

@log.go Please do not use m,k,v these kind of variable name. Instead, please use self descriptional names.

------------------------------------------------------------------------------------------------

@mongodb.go add log as info level for successful connection of db and pre-defined collections. Also log verbose of all queries when verboseLevel is higher than 2.

------------------------------------------------------------------------------------------------

can you create test files for config.go, log.go and mongodb.go?
>>
go test -v ./lib/...

------------------------------------------------------------------------------------------------

Please create the helper.go file in lib.
It provides a function to generate a UUID-like string of specified length.


------------------------------------------------------------------------------------------------

Please create the mail.go file in lib.
It provides a function to send an email message through the remote HTTP API like this.
...

------------------------------------------------------------------------------------------------

Write db.go to provide access to elasticsearch server.
It reads from config of the following.
"""
[elastic]
cert = "/opt/homebrew/Cellar/elasticsearch-full/7.17.4/libexec/elastic-stack-ca.p12"
host = "https://127.0.0.1:9200"
password = "1231234"
user = "elastic"
verbose = 3
"""
It provides a proxy layer of normal elasticsearch driver API with the ability of collecting response time and log slow queries as in the mongodb.go file.


