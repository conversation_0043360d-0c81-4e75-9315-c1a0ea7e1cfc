
## Getting Started

1. Clone the repository
2. Navigate to src directory:
   ```bash
   cd src
   ```
3. Install dependencies:
   ```bash
   go mod download
   ```
4. Build the application:
   ```bash
   make build
   ```

## Development

Common make commands (run from src directory):
bash
make test # Run tests
make lint # Run linters
make build # Build the application
make clean # Clean build artifacts

## Configuration

Configuration is loaded from TOML files. The config file path can be specified via:
- Command line flag: `--config path/to/config.ini`
- Environment variable: `RMBASE_FILE_CFG=path/to/config.ini`

Example configuration can be found in `configs/app.ini`.
Local test configuration can be found in `configs/local.test.ini`.

## Testing

Run tests from the src directory:
```bash
make test
```

Or run specific tests:

```bash
cd src
go test ./lib/...
```

Or run verbose tests:

```bash
go test -v ./lib/...
```

Or run verbose tests with coverage:

```bash
go test -v ./lib/... -count=1
```

### Linter
Using golangci-lint for code linting. The project includes a `.golangci.yml` configuration file with predefined rules.

#### Installation

```bash
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
```

#### Usage

Run linter using make command:
```bash
make lint
```

Or run directly with our configuration:
```bash
golangci-lint run
```

Our `.golangci.yml` includes:
- 12 enabled linters (govet, errcheck, staticcheck, etc.)
- Skip rules for test files and vendor directories
- Custom linter settings for each enabled linter

For detailed configuration, check `.golangci.yml` in the project root.
