package main

import (
	"context"
	"fmt"

	goconfig "github.com/real-rm/goconfig"
	gohelper "github.com/real-rm/gohelper"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

// setting up logging, and establishing MongoDB connection.
func init() {
	// Load configuration
	if err := goconfig.LoadConfig(); err != nil {
		fmt.Printf("Failed to load config: %v", err)
	}

	// Initialize logging first
	if err := golog.InitLog(); err != nil {
		golog.Fatalf("Failed to initialize logging: %v", err)
	}

	// Initialize MongoDB last (after config is loaded)
	if err := gomongo.InitMongoDB(); err != nil {
		golog.Fatalf("Failed to initialize MongoDB: %v", err)
	}
}

func processProperties(ctx context.Context) error {
	// Get collection using goconfig and gomongo
	coll := gomongo.Coll("vow", "properties")
	fmt.Printf("coll: %v\n", coll)
	// Get query parameters from config
	query := bson.M{
		"status": "A",
	}

	options := gomongo.QueryOptions{
		Projection: bson.D{
			{Key: "status", Value: 1},
			{Key: "lp", Value: 1},
		},
		Sort: bson.D{{Key: "lp", Value: 1}},
	}

	// Get cursor
	cursor, err := coll.Find(ctx, query, options)
	fmt.Printf("cursor: %v\n", cursor)
	if err != nil {
		return fmt.Errorf("failed to execute query: %v", err)
	}

	opts := gohelper.StreamingOptions{
		Stream: cursor,
		Process: func(item interface{}) error {
			golog.Info("11Processing item", "data", item)
			fmt.Printf("11Processing item: %v\n", item)
			return nil
		},
		End: func(err error) {
			if err != nil {
				golog.Error("Stream ended with error", "error", err)
			} else {
				golog.Info("Stream completed successfully")
			}
		},
		Error: func(err error) {
			golog.Error("Processing error", "error", err)
		},
		High:          10,
		Low:           0,
		SpeedInterval: 100,
		Verbose:       3,
		Event:         "mongoData",
	}

	err = gohelper.Streaming(ctx, &opts)
	if err != nil {
		golog.Error("Failed to stream data", "error", err)
	}
	return nil
}

func main() {
	ctx := context.Background()
	if err := processProperties(ctx); err != nil {
		golog.Fatal("Failed to process properties", "error", err)
	}
}
