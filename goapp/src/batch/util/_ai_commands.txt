Fx:

Please write a batch genUUID.go in batch/util folder. It shall accept command line -n [number] -d [digits] -f [output-file], and generate 'number' of uuid with 'digits' of characters then save them into 'output-file' in the format of "line-number:uuid" on each line. Please use the lib/helper.go.
----
Please check for duplicates in the output file and print the line number, uuid, amount of duplicates, and the percentage of total uuids.
----
