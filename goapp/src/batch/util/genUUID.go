// Package main provides a command line utility for generating UUIDs
//
// Usage:
//
//	genUUID -n <number> -d <digits> -f <filename>
//
// Flags:
//
//	-n int
//	  	number of UUIDs to generate (default 1)
//	-d int
//	  	number of digits in each UUID (default 32)
//	-f string
//	  	output file path (required)
//
// The program generates the specified number of UUIDs with the given length
// and writes them to the output file in the format:
// <index>:<uuid>
//
// Example output:
// 1:a1b2c3d4e5f6g7h8i9j0
// 2:k1l2m3n4o5p6q7r8s9t0
package main

import (
	"bufio"
	"flag"
	"fmt"
	"os"
	"strings"
	"time"

	gohelper "github.com/real-rm/gohelper"
)

func main() {
	var (
		number   int
		digits   int
		filename string
	)

	// Parse command line flags
	flag.IntVar(&number, "n", 1, "number of UUIDs to generate")
	flag.IntVar(&digits, "d", 32, "number of digits in each UUID")
	flag.StringVar(&filename, "f", "", "output file path")
	flag.Parse()

	// Validate inputs
	if number < 1 {
		fmt.Fprintf(os.Stderr, "Error: number must be positive\n")
		os.Exit(1)
	}
	if digits < 1 {
		fmt.Fprintf(os.Stderr, "Error: digits must be positive\n")
		os.Exit(1)
	}
	if filename == "" {
		fmt.Fprintf(os.Stderr, "Error: output file is required\n")
		os.Exit(1)
	}

	startTime := time.Now()

	// Generate UUIDs
	var builder strings.Builder
	for i := 1; i <= number; i++ {
		uuid, err := gohelper.GenUUID(digits)
		if err != nil {
			fmt.Fprintf(os.Stderr, "Error generating UUID: %v\n", err)
			os.Exit(1)
		}
		builder.WriteString(fmt.Sprintf("%d:%s\n", i, uuid))
	}

	// Write to file
	err := os.WriteFile(filename, []byte(builder.String()), 0644)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error writing to file: %v\n", err)
		os.Exit(1)
	}

	// Check for duplicates
	var uuids = make(map[string][]int) // Store line numbers for each UUID
	var totalDuplicates int
	scanner := bufio.NewScanner(strings.NewReader(builder.String()))
	lineNum := 0
	for scanner.Scan() {
		lineNum++
		line := scanner.Text()
		parts := strings.Split(line, ":")
		if len(parts) == 2 {
			uuid := parts[1]
			uuids[uuid] = append(uuids[uuid], lineNum)
		}
	}

	// Print duplicate information
	for uuid, lines := range uuids {
		if len(lines) > 1 {
			fmt.Printf("Duplicate UUID %s found at lines: %v\n", uuid, lines)
			totalDuplicates += len(lines) - 1 // Subtract 1 since first occurrence isn't a duplicate
		}
	}

	if totalDuplicates > 0 {
		duplicatePercentage := float64(totalDuplicates) / float64(number) * 100
		fmt.Printf("\nTotal duplicates found: %d (%.2f%% of total UUIDs)\n",
			totalDuplicates, duplicatePercentage)
	} else {
		fmt.Println("\nNo duplicates found")
	}

	elapsed := time.Since(startTime)
	fmt.Printf("Successfully generated %d UUIDs and saved to %s\n", number, filename)
	fmt.Printf("Time taken: %v\n", elapsed)
}
