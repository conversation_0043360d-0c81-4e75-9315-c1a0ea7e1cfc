package golog

import (
	"bytes"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	goconfig "github.com/real-rm/goconfig"
)

// resetConfig resets the goconfig state
func resetConfig() {
	goconfig.ResetConfig()
}

// captureStdout captures stdout output and returns it as a string
func captureStdout(f func()) string {
	old := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w
	outC := make(chan string)
	go func() {
		var buf bytes.Buffer
		if _, err := io.Copy(&buf, r); err != nil {
			fmt.Printf("Failed to copy stdout: %v", err)
		}
		outC <- buf.String()
	}()
	f()
	if err := w.Close(); err != nil {
		fmt.Printf("Failed to close stdout: %v", err)
	}
	os.Stdout = old
	return <-outC
}

func TestCaptureStdout(t *testing.T) {
	stdout := captureStdout(func() {
		fmt.Println("This is a test")
	})
	if !strings.Contains(stdout, "This is a test") {
		t.Error("captureStdout did not capture the output")
	}
}

func TestInitLog(t *testing.T) {
	// Create temporary directory for logs
	tmpDir, err := os.MkdirTemp("", "logs-*")
	fmt.Println("tmpDir", tmpDir)
	if err != nil {
		t.Fatal(err)
	}
	defer func() {
		if err := os.RemoveAll(tmpDir); err != nil {
			fmt.Printf("Failed to remove temporary directory: %v", err)
		}
	}()

	// Test cases for different configurations
	testCases := []struct {
		name           string
		config         string
		standardOutput bool
		expectStdout   bool
	}{
		{
			name: "Without standard output",
			config: `
[golog]
dir = "` + tmpDir + `"
level = "debug"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "text"
`,
			standardOutput: false,
			expectStdout:   false,
		},
		{
			name: "With standard output",
			config: `
[golog]
dir = "` + tmpDir + `"
level = "debug"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "text"
standard_output = true
`,
			standardOutput: true,
			expectStdout:   true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Reset config state before each test case
			resetConfig()

			// Create a temporary config file
			tmpfile, err := os.CreateTemp(tmpDir, "config-*.toml")
			fmt.Println("tmpfile", tmpfile.Name())
			if err != nil {
				t.Fatal(err)
			}
			configPath := tmpfile.Name()
			defer func() {
				if err := os.Remove(configPath); err != nil {
					fmt.Printf("Failed to remove config file: %v", err)
				}
			}()

			// Write config content
			if _, err := tmpfile.Write([]byte(tc.config)); err != nil {
				t.Fatal(err)
			}
			if err := tmpfile.Sync(); err != nil {
				t.Fatal(err)
			}
			if err := tmpfile.Close(); err != nil {
				t.Fatal(err)
			}
			// Set config file path and load configuration
			if err := os.Setenv("RMBASE_FILE_CFG", configPath); err != nil {
				t.Fatal(err)
			}

			// Wait a bit to ensure file system sync
			time.Sleep(100 * time.Millisecond)

			// Load config and verify it exists
			if _, err := os.Stat(configPath); err != nil {
				t.Fatalf("Config file does not exist: %v", err)
			}

			// Read and verify config content
			content, err := os.ReadFile(configPath)
			if err != nil {
				t.Fatalf("Failed to read config file: %v", err)
			}
			if !strings.Contains(string(content), tc.config) {
				t.Fatal("Config file content mismatch")
			}

			if err := goconfig.LoadConfig(); err != nil {
				t.Fatalf("Failed to load config: %v", err)
			}

			// Initialize logger
			if err := InitLog(); err != nil {
				t.Fatalf("InitLog() error = %v", err)
			}

			// Override the logger's output to use the test directory
			if handler, ok := Logger.Handler().(*customHandler); ok {
				// Create a new file in the test directory
				logFile := filepath.Join(tmpDir, "app.log")
				file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
				if err != nil {
					t.Fatalf("Failed to create log file: %v", err)
				}
				defer func() {
					if err := file.Close(); err != nil {
						t.Fatalf("Failed to close log file: %v", err)
					}
				}()

				// Use both file and stdout if standard_output is true
				if tc.standardOutput {
					// Create a pipe to capture output
					r, w, _ := os.Pipe()
					oldStdout := os.Stdout
					os.Stdout = w
					outC := make(chan string)
					go func() {
						var buf bytes.Buffer
						if _, err := io.Copy(&buf, r); err != nil {
							fmt.Printf("Failed to copy stdout: %v", err)
						}
						outC <- buf.String()
					}()

					handler.w = io.MultiWriter(file, w)

					// Test logging to different levels
					tests := []struct {
						name     string
						logFunc  func(interface{}, ...interface{})
						logFile  string
						message  string
						contains []string
					}{
						{
							name:     "Debug",
							logFunc:  Debug,
							logFile:  "verbose.log",
							message:  "debug message",
							contains: []string{"debug message"},
						},
						{
							name:     "Info",
							logFunc:  Info,
							logFile:  "info.log",
							message:  "info message",
							contains: []string{"info message"},
						},
						{
							name:     "Error",
							logFunc:  Error,
							logFile:  "error.log",
							message:  "error message",
							contains: []string{"error message"},
						},
						{
							name:     "Verbose",
							logFunc:  Verbose,
							logFile:  "verbose.log",
							message:  "verbose message",
							contains: []string{"verbose message"},
						},
					}

					for _, tt := range tests {
						t.Run(tt.name, func(t *testing.T) {
							// Execute the logging function
							tt.logFunc(tt.message)

							// Read log file
							content, err := os.ReadFile(filepath.Join(tmpDir, tt.logFile))
							if err != nil {
								t.Fatalf("Failed to read log file: %v", err)
							}

							// Print log content for debugging
							fmt.Printf("Log content: %s\n", string(content))

							// Check if log contains expected content in file
							logStr := string(content)
							for _, substr := range tt.contains {
								if !strings.Contains(logStr, substr) {
									t.Errorf("Log file doesn't contain %q", substr)
								}
							}
						})
					}

					// Close the pipe and restore stdout
					if err := w.Close(); err != nil {
						t.Fatalf("Failed to close stdout: %v", err)
					}
					os.Stdout = oldStdout
					stdout := <-outC

					// Check stdout output
					for _, tt := range tests {
						for _, substr := range tt.contains {
							if !strings.Contains(stdout, substr) {
								t.Errorf("Stdout doesn't contain %q, got: %q", substr, stdout)
							}
						}
					}
				} else {
					handler.w = file

					// Test logging to different levels
					tests := []struct {
						name     string
						logFunc  func(interface{}, ...interface{})
						logFile  string
						message  string
						contains []string
					}{
						{
							name:     "Debug",
							logFunc:  Debug,
							logFile:  "verbose.log",
							message:  "debug message",
							contains: []string{"debug message"},
						},
						{
							name:     "Info",
							logFunc:  Info,
							logFile:  "info.log",
							message:  "info message",
							contains: []string{"info message"},
						},
						{
							name:     "Error",
							logFunc:  Error,
							logFile:  "error.log",
							message:  "error message",
							contains: []string{"error message"},
						},
						{
							name:     "Verbose",
							logFunc:  Verbose,
							logFile:  "verbose.log",
							message:  "verbose message",
							contains: []string{"verbose message"},
						},
					}

					for _, tt := range tests {
						t.Run(tt.name, func(t *testing.T) {
							// Execute the logging function
							tt.logFunc(tt.message)

							// Read log file
							content, err := os.ReadFile(filepath.Join(tmpDir, tt.logFile))
							if err != nil {
								t.Fatalf("Failed to read log file: %v", err)
							}

							// Print log content for debugging
							fmt.Printf("Log content: %s\n", string(content))

							// Check if log contains expected content in file
							logStr := string(content)
							for _, substr := range tt.contains {
								if !strings.Contains(logStr, substr) {
									t.Errorf("Log file doesn't contain %q", substr)
								}
							}
						})
					}
				}
			}
		})
	}
}

// customTestError is a custom error type for testing
type customTestError struct {
	msg string
}

func (e *customTestError) Error() string {
	return e.msg
}

// TestErrorHandling tests the Error function's handling of different error types
func TestErrorHandling(t *testing.T) {
	// Create a buffer to capture log output
	var buf bytes.Buffer

	// Create a custom handler that writes to our buffer
	handler := &customHandler{
		w:        &buf,
		minLevel: slog.LevelDebug,
	}

	// Create a logger with our custom handler
	testLogger := slog.New(handler)

	// Save the original logger and restore it after the test
	originalLogger := Logger
	Logger = testLogger
	defer func() {
		Logger = originalLogger
	}()

	testCases := []struct {
		name           string
		err            error
		expectedOutput string
		description    string
	}{
		{
			name:           "fmt.Errorf",
			err:            fmt.Errorf("test error with details: %s", "some detail"),
			expectedOutput: "test error with details: some detail",
			description:    "Should correctly display fmt.Errorf error message",
		},
		{
			name:           "errors.New",
			err:            errors.New("simple error message"),
			expectedOutput: "simple error message",
			description:    "Should correctly display errors.New error message",
		},
		{
			name:           "custom error",
			err:            &customTestError{msg: "custom error message"},
			expectedOutput: "custom error message",
			description:    "Should correctly display custom error message",
		},
		{
			name:           "wrapped error",
			err:            fmt.Errorf("wrapped: %w", errors.New("base error")),
			expectedOutput: "wrapped: base error",
			description:    "Should correctly display wrapped error message",
		},
		{
			name:           "nil error",
			err:            nil,
			expectedOutput: "null",
			description:    "Should display 'null' for nil error",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Clear the buffer before each test
			buf.Reset()

			// Call Error function with the test error
			Error("Test error", "error", tc.err)

			// Get the logged output
			output := buf.String()

			// Check if the output contains the expected error message
			if !strings.Contains(output, tc.expectedOutput) {
				t.Errorf("%s: expected output to contain %q, but got %q",
					tc.description, tc.expectedOutput, output)
			}

			// Verify that the log contains the basic structure
			if !strings.Contains(output, "level=ERROR") {
				t.Errorf("Output should contain 'level=ERROR', got: %q", output)
			}

			if !strings.Contains(output, "msg=Test error") {
				t.Errorf("Output should contain 'msg=Test error', got: %q", output)
			}
		})
	}
}

// TestTryFormatJSON tests the tryFormatJSON function directly
func TestTryFormatJSON(t *testing.T) {
	testCases := []struct {
		name     string
		input    interface{}
		expected string
	}{
		{
			name:     "fmt.Errorf",
			input:    fmt.Errorf("test error: %s", "detail"),
			expected: "test error: detail",
		},
		{
			name:     "errors.New",
			input:    errors.New("simple error"),
			expected: "simple error",
		},
		{
			name:     "custom error",
			input:    &customTestError{msg: "custom message"},
			expected: "custom message",
		},
		{
			name:     "nil error",
			input:    (error)(nil),
			expected: "null",
		},
		{
			name:     "string",
			input:    "regular string",
			expected: "\"regular string\"", // JSON formatted string
		},
		{
			name:     "number",
			input:    42,
			expected: "42",
		},
		{
			name:     "struct",
			input:    struct{ Name string }{Name: "test"},
			expected: "{\n  \"Name\": \"test\"\n}",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tryFormatJSON(tc.input)
			if result != tc.expected {
				t.Errorf("tryFormatJSON(%v) = %q, expected %q", tc.input, result, tc.expected)
			}
		})
	}
}
