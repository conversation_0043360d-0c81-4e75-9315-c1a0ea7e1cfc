# golog

A flexible and powerful logging package for Go applications that provides structured logging with multiple log levels and output formats.

## Features

- Multiple log levels (Debug, Verbose, Info, Warn, Error, Fatal)
- Structured logging with key-value pairs
- Support for both JSON and text log formats
- Separate log files for different log levels
- Automatic caller information for error logs
- Configurable log directory and file paths
- Built on top of Go's `slog` package for modern logging capabilities

## Installation

```bash
go get github.com/real-rm/golog
```

## Configuration

The package uses a TOML configuration format. Configure your logging settings in your config file:

```toml
[golog]
dir = "/var/log/myapp"      # Log directory
level = "info"             # Global log level (debug, info, warn, error)
verbose = "verbose.log"    # Verbose log file
info = "info.log"         # Info log file
error = "error.log"       # Error log file
format = "json"           # Log format (json or text)
```

## Usage

### Initialization

```go
import "github.com/real-rm/golog"

func main() {
    err := golog.InitLog()
    if err != nil {
        panic(err)
    }
}
```

### Basic Logging

```go
// Debug level logging
golog.Debug("Debug message", "key", "value")

// Verbose level logging
golog.Verbose("Verbose message", "key", "value")

// Info level logging
golog.Info("Info message", "key", "value")

// Warning level logging
golog.Warn("Warning message", "key", "value")

// Error level logging (includes caller information)
golog.Error("Error message", "key", "value")

// Fatal level logging (exits the program)
golog.Fatal("Fatal message", "key", "value")
```

### Formatted Logging

```go
// Debug with formatting
golog.Debugf("Debug message: %s", "formatted")

// Info with formatting
golog.Infof("Info message: %s", "formatted")

// Error with formatting
golog.Errorf("Error message: %s", "formatted")

// Fatal with formatting
golog.Fatalf("Fatal message: %s", "formatted")
```

## Log Levels

The package supports the following log levels:

- `Debug`: Detailed information for debugging
- `Verbose`: More detailed information than Info
- `Info`: General operational information
- `Warn`: Warning messages for potentially harmful situations
- `Error`: Error messages for failures
- `Fatal`: Critical errors that cause program termination

## Log Format

### JSON Format Example
```json
{"time":"2024-03-14T12:00:00Z","level":"INFO","msg":"Info message","key":"value"}
```

### Text Format Example
```
time=2024-03-14T12:00:00Z level=INFO msg="Info message" key=value
```

## Error Logging

Error logs automatically include caller information:
- File name
- Line number
- Function name

Example error log:
```json
{
    "time": "2024-03-14T12:00:00Z",
    "level": "ERROR",
    "msg": "Database connection failed",
    "error": "connection timeout",
    "file": "main.go",
    "line": 42
}
```

## Dependencies

- log/slog (Go standard library)
- github.com/real-rm/goconfig

## Best Practices

1. Always initialize the logger at the start of your application
2. Use appropriate log levels for different types of messages
3. Include relevant context using key-value pairs
4. Use structured logging for better log analysis
5. Configure separate log files for different levels to manage log volume
6. Use JSON format when logs need to be processed by log aggregation tools
7. Use text format for human-readable logs during development

## Example Configuration

```toml
[golog]
dir = "/var/log/myapp"
level = "info"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "json"
```

This configuration will:
- Store logs in `/var/log/myapp`
- Set the global log level to "info"
- Create separate files for verbose, info, and error logs
- Use JSON format for structured logging

```bash
export GOPRIVATE=*
vi ~/.gitconfig

```
add 
```
[url "ssh://**************"]
    insteadOf = https://github.com
```