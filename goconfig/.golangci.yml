version: "2"

run:
  timeout: 5m

issues:
  max-issues-per-linter: 50
  max-same-issues: 5


linters:
  enable:
    - govet
    - errcheck
    - staticcheck
    - unused
    - ineffassign
    - misspell
    - unconvert
    - gocritic
    # - depguard
  exclusions:
    paths:
      - /usr/local/go/src
      - vendor$
      - third_party$
      - .*/go/pkg/mod/.*
      - .*_test.go
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
