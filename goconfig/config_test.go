package goconfig

import (
	"os"
	"path/filepath"
	"reflect"
	"testing"
)

func resetConfig() {
	configFile = ""
	configLoaded = false
	configData = nil
}

func TestLoadConfig(t *testing.T) {
	// Create a temporary config file
	content := `
[dbs]
verbose = 3

[dbs.chome]
uri = "mongodb://d1:d1@mac:27017/tmpTest"

[preDefColls.testColl]
collName = "test_collection"
dbName = "tmpTest"

[preDefColls.emailWhitelist]
collName = "email_whitelist"
dbName = "chome"

[golog]
dir = "/tmp/logs"
level = "info"
verbose = "verbose.log"
info = "info.log"
error = "error.log"
format = "text"


[test]
test1 = null
test2 = "null"
`
	tmpfile, err := os.CreateTemp("", "config-goconfig-test.toml")
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove(tmpfile.Name())

	if _, err := tmpfile.Write([]byte(content)); err != nil {
		t.Fatal(err)
	}
	if err := tmpfile.Close(); err != nil {
		t.Fatal(err)
	}

	// Test command line flag
	t.Run("LoadFromFlag", func(t *testing.T) {
		resetConfig()
		configFile = tmpfile.Name()
		if err := LoadConfig(); err != nil {
			t.Errorf("LoadConfig() error = %v", err)
		}
	})

	// Test environment variable
	t.Run("LoadFromEnv", func(t *testing.T) {
		resetConfig()
		os.Setenv("RMBASE_FILE_CFG", tmpfile.Name())
		defer os.Unsetenv("RMBASE_FILE_CFG")

		if err := LoadConfig(); err != nil {
			t.Errorf("LoadConfig() error = %v", err)
		}
	})

	// Test Config retrieval
	t.Run("ConfigRetrieval", func(t *testing.T) {
		resetConfig()
		configFile = tmpfile.Name()
		if err := LoadConfig(); err != nil {
			t.Fatalf("Failed to load config: %v", err)
		}

		tests := []struct {
			path string
			want interface{}
		}{
			{"dbs.verbose", int64(3)},
			{"preDefColls.testColl.collName", "test_collection"},
			{"preDefColls.testColl.dbName", "tmpTest"},
			{"nonexistent.path", nil},
		}

		for _, tt := range tests {
			t.Run(tt.path, func(t *testing.T) {
				got := Config(tt.path)
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("Config(%q) = %v, want %v", tt.path, got, tt.want)
				}
			})
		}
	})
}

func TestConfig(t *testing.T) {
	// Create a temporary config file with more complex test cases
	content := `
[numbers]
int = 42
float = 3.14
negative = -1

[strings]
empty = ""
with_spaces = "hello world"
special_chars = "!@#$%^&*()"

[arrays]
numbers = [1, 2, 3, 4, 5]
strings = ["a", "b", "c"]
mixed = [1, "two", 3.0]

[objects]
nested = { key = "value" }
deep = { level1 = { level2 = { level3 = "deep" } } }

[empty]
`
	tmpfile, err := os.CreateTemp("", "config-goconfig-test.toml")
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove(tmpfile.Name())

	if _, err := tmpfile.Write([]byte(content)); err != nil {
		t.Fatal(err)
	}
	if err := tmpfile.Close(); err != nil {
		t.Fatal(err)
	}

	// Reset and load the config file
	resetConfig()
	configFile = tmpfile.Name()
	if err := LoadConfig(); err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	tests := []struct {
		name     string
		path     string
		want     interface{}
		wantType string
	}{
		{
			name:     "Integer value",
			path:     "numbers.int",
			want:     int64(42),
			wantType: "int64",
		},
		{
			name:     "Float value",
			path:     "numbers.float",
			want:     3.14,
			wantType: "float64",
		},
		{
			name:     "Negative number",
			path:     "numbers.negative",
			want:     int64(-1),
			wantType: "int64",
		},
		{
			name:     "Empty string",
			path:     "strings.empty",
			want:     "",
			wantType: "string",
		},
		{
			name:     "String with spaces",
			path:     "strings.with_spaces",
			want:     "hello world",
			wantType: "string",
		},
		{
			name:     "String with special characters",
			path:     "strings.special_chars",
			want:     "!@#$%^&*()",
			wantType: "string",
		},
		{
			name:     "Number array",
			path:     "arrays.numbers",
			want:     []interface{}{int64(1), int64(2), int64(3), int64(4), int64(5)},
			wantType: "[]interface {}",
		},
		{
			name:     "String array",
			path:     "arrays.strings",
			want:     []interface{}{"a", "b", "c"},
			wantType: "[]interface {}",
		},
		{
			name:     "Mixed array",
			path:     "arrays.mixed",
			want:     []interface{}{int64(1), "two", 3.0},
			wantType: "[]interface {}",
		},
		{
			name:     "Nested object",
			path:     "objects.nested",
			want:     map[string]interface{}{"key": "value"},
			wantType: "map[string]interface {}",
		},
		{
			name:     "Deep nested object",
			path:     "objects.deep.level1.level2.level3",
			want:     "deep",
			wantType: "string",
		},
		{
			name:     "Empty section",
			path:     "empty",
			want:     map[string]interface{}{},
			wantType: "map[string]interface {}",
		},
		{
			name:     "Non-existent path",
			path:     "nonexistent.path",
			want:     nil,
			wantType: "<nil>",
		},
		{
			name:     "Empty path",
			path:     "",
			want:     configData,
			wantType: "map[string]interface {}",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := Config(tt.path)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Config(%q) = %v (%T), want %v (%s)", tt.path, got, got, tt.want, tt.wantType)
			}
		})
	}
}

func TestLoadFromTestIni(t *testing.T) {
	// Get the absolute path of test.ini in the goconfig directory
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	testIniPath := filepath.Join(currentDir, "test.ini")

	// Test loading from test.ini
	t.Run("LoadFromTestIni", func(t *testing.T) {
		resetConfig()
		configFile = testIniPath

		if err := LoadConfig(); err != nil {
			t.Fatalf("Failed to load config from test.ini: %v", err)
		}

		tests := []struct {
			name     string
			path     string
			want     interface{}
			wantType string
		}{
			{
				name:     "DB Verbose",
				path:     "dbs.verbose",
				want:     int64(3),
				wantType: "int64",
			},
			{
				name:     "MongoDB URI",
				path:     "dbs.chome.uri",
				want:     "mongodb://d1:d1@mac:27017/tmpTest",
				wantType: "string",
			},
			{
				name:     "Collection Name",
				path:     "preDefColls.testColl.collName",
				want:     "test_collection",
				wantType: "string",
			},
			{
				name:     "Log Directory",
				path:     "golog.dir",
				want:     "/tmp/logs",
				wantType: "string",
			},
			{
				name:     "Null Value",
				path:     "test.test1",
				want:     nil,
				wantType: "<nil>",
			},
			{
				name:     "String Null",
				path:     "test.test2",
				want:     "null",
				wantType: "string",
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				got := Config(tt.path)
				if !reflect.DeepEqual(got, tt.want) {
					t.Errorf("Config(%q) = %v (%T), want %v (%s)", tt.path, got, got, tt.want, tt.wantType)
				}
			})
		}
	})
}

func TestResetConfig(t *testing.T) {
	// Create a temporary config file
	tmpDir, err := os.MkdirTemp("", "config-test-*")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpDir)

	// Create test config file
	configContent := `
[test]
value = "test_value"
`
	tmpfile, err := os.CreateTemp(tmpDir, "config-*.toml")
	if err != nil {
		t.Fatal(err)
	}
	configPath := tmpfile.Name()

	if _, err := tmpfile.Write([]byte(configContent)); err != nil {
		t.Fatal(err)
	}
	if err := tmpfile.Close(); err != nil {
		t.Fatal(err)
	}

	// Set environment variable and load config
	os.Setenv("RMBASE_FILE_CFG", configPath)
	if err := LoadConfig(); err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	// Verify config is loaded
	if !configLoaded {
		t.Error("Config should be loaded")
	}
	if configFile == "" {
		t.Error("Config file path should be set")
	}
	if configData == nil {
		t.Error("Config data should be set")
	}

	// Reset config
	ResetConfig()

	// Verify all state is reset
	if configLoaded {
		t.Error("Config should not be loaded after reset")
	}
	if configFile != "" {
		t.Error("Config file path should be empty after reset")
	}
	if configData != nil {
		t.Error("Config data should be nil after reset")
	}

	// Verify we can load config again after reset
	if err := LoadConfig(); err != nil {
		t.Fatalf("Failed to load config after reset: %v", err)
	}
}

func TestConfigString(t *testing.T) {
	// Create a temporary config file
	content := `
[strings]
valid = "test string"
number = 42
empty = ""
`
	tmpfile, err := os.CreateTemp("", "config-goconfig-test.toml")
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove(tmpfile.Name())

	if _, err := tmpfile.Write([]byte(content)); err != nil {
		t.Fatal(err)
	}
	if err := tmpfile.Close(); err != nil {
		t.Fatal(err)
	}

	// Reset and load the config file
	resetConfig()
	configFile = tmpfile.Name()
	if err := LoadConfig(); err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	tests := []struct {
		name    string
		path    string
		want    string
		wantErr bool
	}{
		{
			name:    "Valid string",
			path:    "strings.valid",
			want:    "test string",
			wantErr: false,
		},
		{
			name:    "Empty string",
			path:    "strings.empty",
			want:    "",
			wantErr: false,
		},
		{
			name:    "Number to string",
			path:    "strings.number",
			want:    "42",
			wantErr: false,
		},
		{
			name:    "Non-existent path",
			path:    "nonexistent.path",
			want:    "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ConfigString(tt.path)
			if (err != nil) != tt.wantErr {
				t.Errorf("ConfigString() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("ConfigString() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestConfigInt(t *testing.T) {
	// Create a temporary config file
	content := `
[numbers]
valid = 42
string = "42"
invalid = "not a number"
float = 3.14
`
	tmpfile, err := os.CreateTemp("", "config-goconfig-test.toml")
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove(tmpfile.Name())

	if _, err := tmpfile.Write([]byte(content)); err != nil {
		t.Fatal(err)
	}
	if err := tmpfile.Close(); err != nil {
		t.Fatal(err)
	}

	// Reset and load the config file
	resetConfig()
	configFile = tmpfile.Name()
	if err := LoadConfig(); err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	tests := []struct {
		name    string
		path    string
		want    int
		wantErr bool
	}{
		{
			name:    "Valid integer",
			path:    "numbers.valid",
			want:    42,
			wantErr: false,
		},
		{
			name:    "String number",
			path:    "numbers.string",
			want:    0,
			wantErr: true,
		},
		{
			name:    "Invalid number",
			path:    "numbers.invalid",
			want:    0,
			wantErr: true,
		},
		{
			name:    "Float number",
			path:    "numbers.float",
			want:    0,
			wantErr: true,
		},
		{
			name:    "Non-existent path",
			path:    "nonexistent.path",
			want:    0,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ConfigInt(tt.path)
			if (err != nil) != tt.wantErr {
				t.Errorf("ConfigInt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("ConfigInt() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestConfigFloat(t *testing.T) {
	// Create a temporary config file
	content := `
[numbers]
valid = 42.0
string = "42.0"
invalid = "not a number"
int = 42
`
	tmpfile, err := os.CreateTemp("", "config-goconfig-test.toml")
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove(tmpfile.Name())

	if _, err := tmpfile.Write([]byte(content)); err != nil {
		t.Fatal(err)
	}
	if err := tmpfile.Close(); err != nil {
		t.Fatal(err)
	}

	// Reset and load the config file
	resetConfig()
	configFile = tmpfile.Name()
	if err := LoadConfig(); err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	tests := []struct {
		name    string
		path    string
		want    float64
		wantErr bool
	}{
		{
			name:    "Valid float",
			path:    "numbers.valid",
			want:    42.0,
			wantErr: false,
		},
		{
			name:    "String float",
			path:    "numbers.string",
			want:    0,
			wantErr: true,
		},
		{
			name:    "Invalid number",
			path:    "numbers.invalid",
			want:    0,
			wantErr: true,
		},
		{
			name:    "Integer number",
			path:    "numbers.int",
			want:    42.0,
			wantErr: false,
		},
		{
			name:    "Non-existent path",
			path:    "nonexistent.path",
			want:    0,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ConfigFloat(tt.path)
			if (err != nil) != tt.wantErr {
				t.Errorf("ConfigFloat() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("ConfigFloat() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestConfigBool(t *testing.T) {
	// Create a temporary config file
	content := `
[booleans]
true = true
false = false
string = "true"
invalid = "not a boolean"
`
	tmpfile, err := os.CreateTemp("", "config-goconfig-test.toml")
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove(tmpfile.Name())

	if _, err := tmpfile.Write([]byte(content)); err != nil {
		t.Fatal(err)
	}
	if err := tmpfile.Close(); err != nil {
		t.Fatal(err)
	}

	// Reset and load the config file
	resetConfig()
	configFile = tmpfile.Name()
	if err := LoadConfig(); err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	tests := []struct {
		name    string
		path    string
		want    bool
		wantErr bool
	}{
		{
			name:    "True boolean",
			path:    "booleans.true",
			want:    true,
			wantErr: false,
		},
		{
			name:    "False boolean",
			path:    "booleans.false",
			want:    false,
			wantErr: false,
		},
		{
			name:    "String boolean",
			path:    "booleans.string",
			want:    false,
			wantErr: true,
		},
		{
			name:    "Invalid boolean",
			path:    "booleans.invalid",
			want:    false,
			wantErr: true,
		},
		{
			name:    "Non-existent path",
			path:    "nonexistent.path",
			want:    false,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ConfigBool(tt.path)
			if (err != nil) != tt.wantErr {
				t.Errorf("ConfigBool() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("ConfigBool() = %v, want %v", got, tt.want)
			}
		})
	}
}
