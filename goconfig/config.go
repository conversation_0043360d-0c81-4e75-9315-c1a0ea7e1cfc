// Package lib provides core functionality and utilities for the application.
// This file contains configuration management functionality including loading
// and accessing configuration values from TOML files.
package goconfig

import (
	"flag"
	"fmt"
	"os"
	"strings"

	"github.com/real-rm/go-toml"
)

var (
	configFile   string
	configData   map[string]interface{}
	configLoaded bool
)

func init() {
	// Define command line flag for config file
	flag.StringVar(&configFile, "config", "", "path to config file")
}

// ResetConfig resets the configuration state
func ResetConfig() {
	configFile = ""
	configData = nil
	configLoaded = false
}

// LoadConfig initializes the configuration from either command line flag or environment variable
func LoadConfig() error {
	if configLoaded {
		return nil
	}

	flag.Parse()

	// Command line flag takes priority
	if configFile == "" {
		configFile = os.Getenv("RMBASE_FILE_CFG")
	}

	if configFile == "" {
		return fmt.Errorf("no config file specified. Use --config flag or RMBASE_FILE_CFG environment variable")
	}

	// Read the config file
	data, err := os.ReadFile(configFile)
	if err != nil {
		return fmt.Errorf("error reading config file: %w", err)
	}
	// Unmarshal TOML data
	configData = make(map[string]interface{})
	if err := toml.Unmarshal(data, &configData); err != nil {
		return fmt.Errorf("error parsing config file: %w", err)
	}

	configLoaded = true
	return nil
}

// Config retrieves configuration values using dot notation
func Config(path string) interface{} {
	// Load config if not already loaded
	if !configLoaded {
		if err := LoadConfig(); err != nil {
			fmt.Printf("error loading config: %v", err)
			return nil
		}
	}

	if configData == nil {
		fmt.Printf("config data is nil")
		return nil
	}

	// Handle empty path
	if path == "" {
		fmt.Printf("path is empty")
		return configData
	}

	// Split path by dots and traverse the map
	parts := strings.Split(path, ".")
	var current interface{} = configData

	for _, part := range parts {
		if current == nil {
			fmt.Printf("current is nil")
			return nil
		}

		if m, ok := current.(map[string]interface{}); ok {
			current = m[part]
		} else {
			fmt.Printf("current is not a map")
			return nil
		}
	}

	return current
}

func ConfigString(path string) (string, error) {
	val := Config(path)
	if val == nil {
		return "", fmt.Errorf("config is nil")
	}
	if s, ok := val.(string); ok {
		return s, nil
	} else {
		fmt.Printf("config is not a string, val: %v", val)
		return fmt.Sprintf("%v", val), nil
	}
}

func ConfigInt(path string) (int, error) {
	val := Config(path)
	if val == nil {
		return 0, fmt.Errorf("config is nil")
	}

	switch v := val.(type) {
	case int:
		return v, nil
	case int64:
		return int(v), nil
	case float64:
		return 0, fmt.Errorf("config is a float64, not an integer: %v", val)
	default:
		return 0, fmt.Errorf("config is not an integer, val: %v", val)
	}
}

func ConfigFloat(path string) (float64, error) {
	val := Config(path)
	if val == nil {
		return 0, fmt.Errorf("config is nil")
	}

	switch v := val.(type) {
	case float64:
		return v, nil
	case int:
		return float64(v), nil
	case int64:
		return float64(v), nil
	default:
		return 0, fmt.Errorf("config is not a float64 or int, val: %v", val)
	}
}

func ConfigBool(path string) (bool, error) {
	val := Config(path)
	if val == nil {
		return false, fmt.Errorf("config is nil")
	}
	if b, ok := val.(bool); ok {
		return b, nil
	} else {
		fmt.Printf("config is not a bool, val: %v", val)
		return false, fmt.Errorf("config is not a bool, val: %v", val)
	}
}
