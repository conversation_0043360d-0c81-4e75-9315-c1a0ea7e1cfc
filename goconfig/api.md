<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# goconfig

```go
import "github.com/real-rm/goconfig"
```

Package lib provides core functionality and utilities for the application. This file contains configuration management functionality including loading and accessing configuration values from TOML files.

## Index

- [func Config\(path string\) interface\{\}](<#Config>)
- [func ConfigBool\(path string\) \(bool, error\)](<#ConfigBool>)
- [func ConfigFloat\(path string\) \(float64, error\)](<#ConfigFloat>)
- [func ConfigInt\(path string\) \(int, error\)](<#ConfigInt>)
- [func ConfigString\(path string\) \(string, error\)](<#ConfigString>)
- [func LoadConfig\(\) error](<#LoadConfig>)
- [func ResetConfig\(\)](<#ResetConfig>)


<a name="Config"></a>
## func [Config](<https://github.com/real-rm/goconfig/blob/main/config.go#L66>)

```go
func Config(path string) interface{}
```

Config retrieves configuration values using dot notation

<a name="ConfigBool"></a>
## func [ConfigBool](<https://github.com/real-rm/goconfig/blob/main/config.go#L156>)

```go
func ConfigBool(path string) (bool, error)
```



<a name="ConfigFloat"></a>
## func [ConfigFloat](<https://github.com/real-rm/goconfig/blob/main/config.go#L138>)

```go
func ConfigFloat(path string) (float64, error)
```



<a name="ConfigInt"></a>
## func [ConfigInt](<https://github.com/real-rm/goconfig/blob/main/config.go#L120>)

```go
func ConfigInt(path string) (int, error)
```



<a name="ConfigString"></a>
## func [ConfigString](<https://github.com/real-rm/goconfig/blob/main/config.go#L107>)

```go
func ConfigString(path string) (string, error)
```



<a name="LoadConfig"></a>
## func [LoadConfig](<https://github.com/real-rm/goconfig/blob/main/config.go#L34>)

```go
func LoadConfig() error
```

LoadConfig initializes the configuration from either command line flag or environment variable

<a name="ResetConfig"></a>
## func [ResetConfig](<https://github.com/real-rm/goconfig/blob/main/config.go#L27>)

```go
func ResetConfig()
```

ResetConfig resets the configuration state

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)
