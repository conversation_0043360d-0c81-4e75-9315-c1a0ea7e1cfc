package goprocess

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	FinishWithError    = "Finish with Error"
	FinishWithoutError = "Finish"
	RunningNormal      = "Running normal"
	RunningWithWarning = "Running with warning"
	RunningError       = "Running with Error"
	HasRunningProcess  = "there's a running process. please kill or use force to start"
)

// NormalStatusList contains statuses that are considered normal
var NormalStatusList = []string{
	FinishWithoutError,
	RunningNormal,
}

// WarningStatusList contains statuses that are considered warnings
var WarningStatusList = []string{
	RunningWithWarning,
}

// ErrorStatusList contains statuses that indicate errors
var ErrorStatusList = []string{
	FinishWithError,
	RunningError,
}

// AllStatusList contains all possible statuses
var AllStatusList = append(append(NormalStatusList, WarningStatusList...), ErrorStatusList...)
var gHostName string

func init() {
	var err error
	gHostName, err = os.Hostname()
	if err != nil || gHostName == "" {
		gHostName = os.Getenv("HOSTNAME")
		if gHostName == "" {
			golog.Error("failed to get hostname", "error", err, "env_hostname", os.Getenv("HOSTNAME"))
			os.Exit(1)
		}
	}
}

// ProcessMonitor tracks operation timestamps and their intervals,
// periodically verifies if any operation exceeds its allowed interval,
// and updates process status in MongoDB.
// It's useful for monitoring long-running processes, detecting stalled operations,
// and maintaining process metadata.
type ProcessMonitor struct {
	mu                sync.Mutex
	operationTimes    map[string][]time.Time
	maxIntervals      map[string]time.Duration
	updateInterval    time.Duration
	processStatusCol  *gomongo.MongoCollection
	id                string
	fileName          string
	stopChan          chan struct{}
	multiplier        float64 // Multiplier for max interval tolerance
	lastStatus        string
	registeredFuncs   map[string]func() // Registered functions to run in monitor loop
	stopOnce          sync.Once
	maxOperationTimes int // Maximum number of timestamps to store for each operation
}

// ProcessMonitorOptions contains options for creating a new ProcessMonitor
type ProcessMonitorOptions struct {
	ProcessStatusCol  *gomongo.MongoCollection
	UpdateInterval    time.Duration
	Multiplier        float64
	MaxOperationTimes int // Maximum number of timestamps to store for each operation
	ID                string
}

// NewProcessMonitor creates a new process monitor with options
func NewProcessMonitor(opts ProcessMonitorOptions) (*ProcessMonitor, error) {
	if opts.ProcessStatusCol == nil {
		golog.Error("ProcessStatusCol required")
		return nil, fmt.Errorf("ProcessStatusCol required")
	}
	execPath, err := os.Executable()
	if err != nil {
		golog.Error("failed to get executable path", "error", err)
		return nil, fmt.Errorf("failed to get executable path: %v", err)
	}
	fileName := filepath.Base(execPath)
	if strings.ContainsAny(fileName, `/\:*?"<>|`) {
		errMsg := "fileName contains invalid characters"
		golog.Error(errMsg)
		return nil, fmt.Errorf("%s", errMsg)
	}

	interval := 5 * time.Minute // Default interval
	if opts.UpdateInterval > 0 {
		interval = opts.UpdateInterval
	}

	multiplier := 1.0 // Default multiplier
	if opts.Multiplier > 0 {
		multiplier = opts.Multiplier
	}

	maxOperationTimes := 10 // Default max operation times
	if opts.MaxOperationTimes > 0 {
		maxOperationTimes = opts.MaxOperationTimes
	}
	id := fmt.Sprintf("%d", os.Getpid())
	if opts.ID != "" {
		id = opts.ID
	}

	newProcessMonitor := &ProcessMonitor{
		operationTimes:    make(map[string][]time.Time),
		maxIntervals:      make(map[string]time.Duration),
		updateInterval:    interval,
		processStatusCol:  opts.ProcessStatusCol,
		id:                id,
		fileName:          fileName,
		stopChan:          make(chan struct{}),
		multiplier:        multiplier,
		registeredFuncs:   make(map[string]func()),
		maxOperationTimes: maxOperationTimes,
	}
	newProcessMonitor.StartMonitoring()
	return newProcessMonitor, nil
}

// SetMultiplier sets the multiplier for max interval tolerance
func (pm *ProcessMonitor) SetMultiplier(multiplier float64) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	pm.multiplier = multiplier
}

// StartMonitoring runs the monitoring process in a separate goroutine
func (pm *ProcessMonitor) StartMonitoring() {
	go pm.monitorLoop()
}

// StopMonitoring stops the monitoring process
func (pm *ProcessMonitor) StopMonitoring() {
	pm.stopOnce.Do(func() { close(pm.stopChan) })
}

// Tick records the time of an operation
func (pm *ProcessMonitor) Tick(operation string) {
	if operation == "" {
		golog.Warn("Empty operation name provided to Tick")
		return
	}
	pm.mu.Lock()
	defer pm.mu.Unlock()

	now := time.Now()
	times := pm.operationTimes[operation]
	if len(times) >= pm.maxOperationTimes {
		pm.operationTimes[operation] = append(times[1:], now)
	} else {
		pm.operationTimes[operation] = append(times, now)
	}
	// refresh times
	times = pm.operationTimes[operation]
	// Calculate max interval
	var maxInterval time.Duration
	if len(times) > 1 {
		maxInterval = time.Duration(0)
		for i := 1; i < len(times); i++ {
			interval := times[i].Sub(times[i-1])
			if interval > maxInterval {
				maxInterval = interval
			}
		}
	} else {
		maxInterval = pm.updateInterval
	}
	pm.maxIntervals[operation] = maxInterval
}

// RegisterMonitorFunc registers a function to be executed in the monitor loop
func (pm *ProcessMonitor) RegisterMonitorFunc(name string, fn func()) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	pm.registeredFuncs[name] = fn
}

// UnregisterMonitorFunc removes a registered function from the monitor loop
func (pm *ProcessMonitor) UnregisterMonitorFunc(name string) {
	pm.mu.Lock()
	defer pm.mu.Unlock()
	delete(pm.registeredFuncs, name)
}

// monitorLoop continuously monitors operations and updates MongoDB
func (pm *ProcessMonitor) monitorLoop() {
	ticker := time.NewTicker(pm.updateInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			func() {
				defer func() {
					if r := recover(); r != nil {
						golog.Error("Recovered from panic in checkOperations", "panic", r)
					}
				}()
				pm.checkOperations()

				// Execute all registered functions
				pm.mu.Lock()
				for name, fn := range pm.registeredFuncs {
					func(name string, fn func()) {
						defer func() {
							if r := recover(); r != nil {
								golog.Error("Recovered from panic in registered function",
									"name", name,
									"panic", r)
							}
						}()
						fn()
					}(name, fn)
				}
				pm.mu.Unlock()
			}()
			// Only update to RunningNormal if checkOperations didn't set RunningError
			pm.mu.Lock()
			curStatus := pm.lastStatus
			pm.mu.Unlock()
			if curStatus == RunningNormal {
				opts := UpdateProcessStatusOptions{
					Status: RunningNormal,
				}
				if err := pm.UpdateProcessStatus(opts); err != nil {
					golog.Error("Error updating process status", "error", err)
				}
			}
		case <-pm.stopChan:
			return
		}
	}
}

// checkOperations checks if any operations have exceeded their max interval
func (pm *ProcessMonitor) checkOperations() {
	pm.mu.Lock()

	now := time.Now()
	hasError := false
	errorMsg := ""

	for operation, times := range pm.operationTimes {
		if len(times) == 0 {
			continue
		}

		lastTime := times[len(times)-1]
		maxInterval := pm.maxIntervals[operation]
		tolerance := time.Duration(float64(maxInterval) * pm.multiplier)
		if now.Sub(lastTime) > tolerance {
			hasError = true
			errorMsg = fmt.Sprintf("Operation '%s' has exceeded max interval (current: %v, max: %v, tolerance: %v)",
				operation, now.Sub(lastTime), maxInterval, tolerance)
			golog.Warn(errorMsg)
		}
	}

	// Update lastStatus and release the lock before calling UpdateProcessStatus
	status := RunningNormal
	if hasError {
		status = RunningWithWarning
	}
	pm.lastStatus = status
	pm.mu.Unlock()

	if hasError {
		opts := UpdateProcessStatusOptions{
			Status:   RunningWithWarning,
			ErrorMsg: &errorMsg,
		}
		if err := pm.UpdateProcessStatus(opts); err != nil {
			golog.Error("Error updating process status", "error", err)
		}
	}
}

// UpdateProcessStatusOptions contains options for updating the process status in MongoDB
type UpdateProcessStatusOptions struct {
	Status   string
	StartTs  *time.Time
	ErrorMsg *string
	Stats    interface{}
}

// UpdateProcessStatus updates the process status in MongoDB
func (pm *ProcessMonitor) UpdateProcessStatus(opts UpdateProcessStatusOptions) error {
	if opts.Status == "" {
		golog.Error("status required")
		return fmt.Errorf("status required")
	}
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	now := time.Now()
	nextTs := now.Add(pm.updateInterval)
	update := bson.M{
		"$set": bson.M{
			"pid":      os.Getpid(),
			"ppid":     os.Getppid(),
			"host":     gHostName,
			"fileName": pm.fileName,
			"status":   opts.Status,
			"nextTs":   nextTs,
		},
	}

	if opts.StartTs != nil {
		update["$set"].(bson.M)["startTs"] = opts.StartTs
	}

	if opts.ErrorMsg != nil && *opts.ErrorMsg != "" {
		update["$set"].(bson.M)["notify"] = *opts.ErrorMsg
	} else {
		update["$unset"] = bson.M{"notify": 1}
	}

	if opts.Stats != nil {
		update["$set"].(bson.M)["stats"] = opts.Stats
	}
	// fmt.Println("---update", update)

	// Force update the status
	_, err := pm.processStatusCol.UpdateOne(
		ctx,
		bson.M{"_id": pm.id + "_" + gHostName},
		update,
		options.Update().SetUpsert(true),
	)
	if err != nil {
		golog.Error("Error updating process status", "error", err)
		return err
	}

	// Update lastStatus
	pm.mu.Lock()
	pm.lastStatus = opts.Status
	pm.mu.Unlock()

	return nil
}

// CheckRunningProcess checks if a process is running
func (pm *ProcessMonitor) CheckRunningProcess() (bool, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	processId := pm.id + "_" + gHostName
	golog.Debug("CheckRunningProcess", "id", processId, "hostname", gHostName)

	query := bson.M{"_id": processId}
	golog.Debug("MongoDB query", "query", query)

	res := pm.processStatusCol.FindOne(ctx, query)
	raw, err := res.Raw()
	if err != nil {
		if err.Error() == "mongo: no documents in result" {
			golog.Debug("No process document found", "id", processId)
			return false, nil
		}
		golog.Error("Error getting raw data",
			"error", err,
			"id", processId,
			"error_type", fmt.Sprintf("%T", err),
			"error_string", err.Error())
		return false, fmt.Errorf("failed to get raw data: %v", err)
	}

	status, err := raw.LookupErr("status")
	if err != nil {
		golog.Error("Error getting status",
			"error", err,
			"id", processId,
			"error_type", fmt.Sprintf("%T", err),
			"error_string", err.Error())
		return false, fmt.Errorf("failed to get status: %v", err)
	}
	statusStr := status.StringValue()
	golog.Debug("Process status", "id", processId, "status", statusStr)

	// Check if the process is running based on its status
	switch statusStr {
	case RunningNormal, RunningWithWarning, RunningError:
		return true, nil
	case FinishWithoutError, FinishWithError:
		return false, nil
	default:
		golog.Warn("Unknown process status", "status", statusStr)
		return false, nil
	}
}

// UpdateStatusWhenAllFinish updates the status and calls the exit function
func (pm *ProcessMonitor) UpdateStatusWhenAllFinish(errorMsg string, exitFn interface{}) {
	status := FinishWithoutError
	if errorMsg != "" {
		status = FinishWithError
	}
	now := time.Now()
	opts := UpdateProcessStatusOptions{
		Status:   status,
		StartTs:  &now,
		ErrorMsg: &errorMsg,
	}
	err := pm.UpdateProcessStatus(opts)
	if err != nil {
		golog.Error("Error updating process status", "error", err)
		if errorMsg == "" {
			errorMsg = fmt.Sprintf("failed to update process status: %v", err)
		} else {
			errorMsg = fmt.Sprintf("%s (failed to update process status: %v)", errorMsg, err)
		}
	}
	if exitFn == nil {
		golog.Error("exitFn is nil")
		return
	}
	switch fn := exitFn.(type) {
	case func(interface{}, ...int):
		fn(errorMsg)
	case func(string):
		fn(errorMsg)
	case func(error):
		if errorMsg == "" {
			fn(nil)
		} else {
			fn(fmt.Errorf("%s", errorMsg))
		}
	case func():
		fn()
	default:
		golog.Error("exitFn is not a valid function", "exitFn", exitFn)
	}
}

// Reset completely resets the ProcessMonitor state
func (pm *ProcessMonitor) Reset() {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	// Stop monitoring if it's running
	pm.stopOnce.Do(func() { close(pm.stopChan) })

	// Reset all state
	pm.operationTimes = make(map[string][]time.Time)
	pm.maxIntervals = make(map[string]time.Duration)
	pm.lastStatus = ""
	pm.registeredFuncs = make(map[string]func())
	pm.stopChan = make(chan struct{})
	pm.stopOnce = sync.Once{}

	// Start monitoring again
	go pm.monitorLoop()
}
