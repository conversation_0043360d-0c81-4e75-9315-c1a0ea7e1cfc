<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# goprocess

```go
import "github.com/real-rm/goprocess"
```

## Index

- [Constants](<#constants>)
- [Variables](<#variables>)
- [type ProcessMonitor](<#ProcessMonitor>)
  - [func NewProcessMonitor\(opts ProcessMonitorOptions\) \(\*ProcessMonitor, error\)](<#NewProcessMonitor>)
  - [func \(pm \*ProcessMonitor\) CheckRunningProcess\(\) \(bool, error\)](<#ProcessMonitor.CheckRunningProcess>)
  - [func \(pm \*ProcessMonitor\) RegisterMonitorFunc\(name string, fn func\(\)\)](<#ProcessMonitor.RegisterMonitorFunc>)
  - [func \(pm \*ProcessMonitor\) Reset\(\)](<#ProcessMonitor.Reset>)
  - [func \(pm \*ProcessMonitor\) SetMultiplier\(multiplier float64\)](<#ProcessMonitor.SetMultiplier>)
  - [func \(pm \*ProcessMonitor\) StartMonitoring\(\)](<#ProcessMonitor.StartMonitoring>)
  - [func \(pm \*ProcessMonitor\) StopMonitoring\(\)](<#ProcessMonitor.StopMonitoring>)
  - [func \(pm \*ProcessMonitor\) Tick\(operation string\)](<#ProcessMonitor.Tick>)
  - [func \(pm \*ProcessMonitor\) UnregisterMonitorFunc\(name string\)](<#ProcessMonitor.UnregisterMonitorFunc>)
  - [func \(pm \*ProcessMonitor\) UpdateProcessStatus\(opts UpdateProcessStatusOptions\) error](<#ProcessMonitor.UpdateProcessStatus>)
  - [func \(pm \*ProcessMonitor\) UpdateStatusWhenAllFinish\(errorMsg string, exitFn interface\{\}\)](<#ProcessMonitor.UpdateStatusWhenAllFinish>)
- [type ProcessMonitorOptions](<#ProcessMonitorOptions>)
- [type UpdateProcessStatusOptions](<#UpdateProcessStatusOptions>)


## Constants

<a name="FinishWithError"></a>

```go
const (
    FinishWithError    = "Finish with Error"
    FinishWithoutError = "Finish"
    RunningNormal      = "Running normal"
    RunningWithWarning = "Running with warning"
    RunningError       = "Running with Error"
    HasRunningProcess  = "there's a running process. please kill or use force to start"
)
```

## Variables

<a name="AllStatusList"></a>AllStatusList contains all possible statuses

```go
var AllStatusList = append(append(NormalStatusList, WarningStatusList...), ErrorStatusList...)
```

<a name="ErrorStatusList"></a>ErrorStatusList contains statuses that indicate errors

```go
var ErrorStatusList = []string{
    FinishWithError,
    RunningError,
}
```

<a name="NormalStatusList"></a>NormalStatusList contains statuses that are considered normal

```go
var NormalStatusList = []string{
    FinishWithoutError,
    RunningNormal,
}
```

<a name="WarningStatusList"></a>WarningStatusList contains statuses that are considered warnings

```go
var WarningStatusList = []string{
    RunningWithWarning,
}
```

<a name="ProcessMonitor"></a>
## type [ProcessMonitor](<https://github.com/real-rm/goprocess/blob/main/process.go#L65-L79>)

ProcessMonitor tracks operation timestamps and their intervals, periodically verifies if any operation exceeds its allowed interval, and updates process status in MongoDB. It's useful for monitoring long\-running processes, detecting stalled operations, and maintaining process metadata.

```go
type ProcessMonitor struct {
    // contains filtered or unexported fields
}
```

<a name="NewProcessMonitor"></a>
### func [NewProcessMonitor](<https://github.com/real-rm/goprocess/blob/main/process.go#L91>)

```go
func NewProcessMonitor(opts ProcessMonitorOptions) (*ProcessMonitor, error)
```

NewProcessMonitor creates a new process monitor with options

<a name="ProcessMonitor.CheckRunningProcess"></a>
### func \(\*ProcessMonitor\) [CheckRunningProcess](<https://github.com/real-rm/goprocess/blob/main/process.go#L366>)

```go
func (pm *ProcessMonitor) CheckRunningProcess() (bool, error)
```

CheckRunningProcess checks if a process is running

<a name="ProcessMonitor.RegisterMonitorFunc"></a>
### func \(\*ProcessMonitor\) [RegisterMonitorFunc](<https://github.com/real-rm/goprocess/blob/main/process.go#L195>)

```go
func (pm *ProcessMonitor) RegisterMonitorFunc(name string, fn func())
```

RegisterMonitorFunc registers a function to be executed in the monitor loop

<a name="ProcessMonitor.Reset"></a>
### func \(\*ProcessMonitor\) [Reset](<https://github.com/real-rm/goprocess/blob/main/process.go#L459>)

```go
func (pm *ProcessMonitor) Reset()
```

Reset completely resets the ProcessMonitor state

<a name="ProcessMonitor.SetMultiplier"></a>
### func \(\*ProcessMonitor\) [SetMultiplier](<https://github.com/real-rm/goprocess/blob/main/process.go#L144>)

```go
func (pm *ProcessMonitor) SetMultiplier(multiplier float64)
```

SetMultiplier sets the multiplier for max interval tolerance

<a name="ProcessMonitor.StartMonitoring"></a>
### func \(\*ProcessMonitor\) [StartMonitoring](<https://github.com/real-rm/goprocess/blob/main/process.go#L151>)

```go
func (pm *ProcessMonitor) StartMonitoring()
```

StartMonitoring runs the monitoring process in a separate goroutine

<a name="ProcessMonitor.StopMonitoring"></a>
### func \(\*ProcessMonitor\) [StopMonitoring](<https://github.com/real-rm/goprocess/blob/main/process.go#L156>)

```go
func (pm *ProcessMonitor) StopMonitoring()
```

StopMonitoring stops the monitoring process

<a name="ProcessMonitor.Tick"></a>
### func \(\*ProcessMonitor\) [Tick](<https://github.com/real-rm/goprocess/blob/main/process.go#L161>)

```go
func (pm *ProcessMonitor) Tick(operation string)
```

Tick records the time of an operation

<a name="ProcessMonitor.UnregisterMonitorFunc"></a>
### func \(\*ProcessMonitor\) [UnregisterMonitorFunc](<https://github.com/real-rm/goprocess/blob/main/process.go#L202>)

```go
func (pm *ProcessMonitor) UnregisterMonitorFunc(name string)
```

UnregisterMonitorFunc removes a registered function from the monitor loop

<a name="ProcessMonitor.UpdateProcessStatus"></a>
### func \(\*ProcessMonitor\) [UpdateProcessStatus](<https://github.com/real-rm/goprocess/blob/main/process.go#L310>)

```go
func (pm *ProcessMonitor) UpdateProcessStatus(opts UpdateProcessStatusOptions) error
```

UpdateProcessStatus updates the process status in MongoDB

<a name="ProcessMonitor.UpdateStatusWhenAllFinish"></a>
### func \(\*ProcessMonitor\) [UpdateStatusWhenAllFinish](<https://github.com/real-rm/goprocess/blob/main/process.go#L416>)

```go
func (pm *ProcessMonitor) UpdateStatusWhenAllFinish(errorMsg string, exitFn interface{})
```

UpdateStatusWhenAllFinish updates the status and calls the exit function

<a name="ProcessMonitorOptions"></a>
## type [ProcessMonitorOptions](<https://github.com/real-rm/goprocess/blob/main/process.go#L82-L88>)

ProcessMonitorOptions contains options for creating a new ProcessMonitor

```go
type ProcessMonitorOptions struct {
    ProcessStatusCol  *gomongo.MongoCollection
    UpdateInterval    time.Duration
    Multiplier        float64
    MaxOperationTimes int // Maximum number of timestamps to store for each operation
    ID                string
}
```

<a name="UpdateProcessStatusOptions"></a>
## type [UpdateProcessStatusOptions](<https://github.com/real-rm/goprocess/blob/main/process.go#L302-L307>)

UpdateProcessStatusOptions contains options for updating the process status in MongoDB

```go
type UpdateProcessStatusOptions struct {
    Status   string
    StartTs  *time.Time
    ErrorMsg *string
    Stats    interface{}
}
```

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)
