# goprocess

A Go package for monitoring and managing long-running processes with MongoDB integration.

## Features

- Process status tracking and monitoring
- Automatic interval checking for operations
- MongoDB integration for status persistence
- Configurable warning and error thresholds
- Function registration system for custom monitoring tasks
- Panic recovery and error handling

## Installation

```bash
go get github.com/real-rm/goprocess
```

## Quick Start

```go
import (
    "github.com/real-rm/goprocess"
    "github.com/real-rm/gomongo"
    "time"
    "log"
)

func main() {
    // Create a MongoDB collection for process status
    coll := gomongo.Coll("your_db", "process_status")

    // Create a new process monitor
    monitor, err := goprocess.NewProcessMonitor(goprocess.ProcessMonitorOptions{
        ProcessStatusCol: coll,
        UpdateInterval:   time.Second * 30,
        Multiplier:       1.5,
        ID:               "processID"
    })
    if err != nil {
        log.Fatal(err)
    }
    defer monitor.StopMonitoring()

    // Record operations using Tick
    monitor.Tick("deleteOne")
    monitor.Tick("updateOne")

    // Register custom functions to run at every UpdateInterval tick
    monitor.RegisterMonitorFunc("updateStat", func() {
        // send stat info to stat.realmaster.com
        fmt.Println("send this id and status to stat.realmaster.com", monitor.id, monitor.lastStatus)
    })

    // Check if another instance is running
    isRunning, err := monitor.CheckRunningProcess()
    if err != nil {
        log.Printf("Error checking running process: %v", err)
    }
    if isRunning {
        log.Println("Another instance is already running")
        return
    }

    // Adjust multiplier if needed
    monitor.SetMultiplier(2.0)

    // Your main process logic here
    // ...

    // When done, update final status
    monitor.UpdateStatusWhenAllFinish("", func(msg string) {
        log.Printf("Process finished with message: %s", msg)
    })
}
```

## API Reference

### ProcessMonitorOptions

```go
type ProcessMonitorOptions struct {
    ProcessStatusCol *gomongo.MongoCollection  // MongoDB collection for status
    UpdateInterval   time.Duration            // Interval for status updates
    Multiplier       float64                  // Multiplier for interval tolerance
    ID               string                  //process id
}
```

### UpdateProcessStatusOptions

```go
type UpdateProcessStatusOptions struct {
    Status   string                 // Process status
    StartTs  *time.Time            // Process start time
    ErrorMsg *string               // Error message if any
    Stats    interface{}           // Custom statistics
}
```

### Main Methods

- `NewProcessMonitor(opts ProcessMonitorOptions)`: Creates a new process monitor
- `Tick(operation string)`: Records an operation timestamp
- `UpdateProcessStatus(opts UpdateProcessStatusOptions)`: Updates process status
- `CheckRunningProcess()`: Checks if another instance is running
- `SetMultiplier(multiplier float64)`: Adjusts interval tolerance
- `RegisterMonitorFunc(name string, fn func())`: Registers a monitoring function
- `UnregisterMonitorFunc(name string)`: Removes a registered function
- `UpdateStatusWhenAllFinish(errorMsg string, exitFn interface{})`: Updates final status
- `StopMonitoring()`: Stops the monitoring process

## Status Constants

```go
const (
    FinishWithError    = "Finish with Error"
    FinishWithoutError = "Finish"
    RunningNormal      = "Running normal"
    RunningWithWarning = "Running with warning"
    RunningError       = "Running with Error"
    HasRunningProcess  = "there's a running process. please kill or use force to start"
)
```

## Status Lists

The package provides several status lists for different process states:

```go
var NormalStatusList = []string{
    FinishWithoutError,
    RunningNormal,
}

var WarningStatusList = []string{
    RunningWithWarning,
}

var ErrorStatusList = []string{
    FinishWithError,
    RunningError,
}

var AllStatusList = append(append(NormalStatusList, WarningStatusList...), ErrorStatusList...)
```

## Best Practices

1. Always call `StopMonitoring()` when the process is done
2. Use meaningful operation names in `Tick`
3. Set appropriate `UpdateInterval` based on your process needs
4. Handle errors returned by `UpdateProcessStatus`
5. Use the multiplier to adjust tolerance for operation intervals
6. Register monitoring functions for custom checks
7. Clean up registered functions when they're no longer needed
8. The process ID and hostname are automatically used to create unique document IDs
9. Operations are tracked with a maximum of 10 timestamps per operation
10. Status updates are automatically persisted to MongoDB