package goprocess

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"

	gohelper "github.com/real-rm/gohelper"
	gomongo "github.com/real-rm/gomongo"
	"go.mongodb.org/mongo-driver/bson"
)

// setupTest sets up the test environment
func setupTestProcess(t *testing.T) (*gomongo.MongoCollection, func(*gomongo.MongoCollection)) {
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}
	configPath, err := filepath.Abs(filepath.Join(currentDir, "local.test.ini"))
	if err != nil {
		t.Fatalf("Failed to get absolute path: %v", err)
	}
	gohelper.SetRmbaseFileCfg(configPath)

	// Initialize test environment
	if err := gohelper.SetupTestEnv(gohelper.TestOptions{
		UseEnvConfig: true,
	}); err != nil {
		t.Fatalf("Failed to setup test environment: %v", err)
	}
	coll := gomongo.Coll("tmp", "processStatus")

	// Clean up collection before test
	if _, err := coll.DeleteMany(context.Background(), bson.M{}); err != nil {
		t.Errorf("Failed to cleanup collection: %v", err)
	}

	// Save original executable path
	originalExecutable := os.Args[0]
	os.Args[0] = "valid_path"

	// Return collection and cleanup function
	return coll, func(coll *gomongo.MongoCollection) {
		// Clean up collection after test
		if _, err := coll.DeleteMany(context.Background(), bson.M{}); err != nil {
			t.Errorf("Failed to cleanup collection: %v", err)
		}
		// Restore original executable path
		os.Args[0] = originalExecutable
	}
}

// TestMain runs before all tests
func TestMain(m *testing.M) {
	// Run tests
	code := m.Run()

	// Clean up after all tests
	coll := gomongo.Coll("tmp", "processStatus")
	if _, err := coll.DeleteMany(context.Background(), bson.M{}); err != nil {
		fmt.Printf("Failed to cleanup collection after all tests: %v\n", err)
	}

	os.Exit(code)
}

func TestProcessMonitor(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	// Create a new process monitor with 30 second update interval
	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		UpdateInterval:   time.Second * 2,
		Multiplier:       2.0,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Test multiplier setting
	if monitor.multiplier != 2.0 {
		t.Errorf("Expected multiplier 2.0, got %v", monitor.multiplier)
	}

	// Test operation recording
	operation := "test-operation"
	monitor.Tick(operation)
	time.Sleep(2 * time.Second)
	monitor.Tick(operation)
	time.Sleep(time.Second)
	monitor.Tick(operation)

	// Check if max interval is calculated correctly
	monitor.mu.Lock()
	times := monitor.operationTimes[operation]
	if len(times) != 3 {
		t.Errorf("Expected 3 recorded times, got %d", len(times))
	}

	maxInterval := monitor.maxIntervals[operation]
	if maxInterval == 0 {
		t.Error("Expected non-zero max interval")
	}
	monitor.mu.Unlock()

	// Test operation checking with multiplier
	time.Sleep(3 * time.Second)
	monitor.checkOperations()

	result := coll.FindOne(context.Background(), bson.M{"_id": monitor.id + "_" + gHostName})
	if err := result.Err(); err != nil {
		if err.Error() == "mongo: no documents in result" {
			t.Error("Expected to find process status document")
			return
		}
		t.Errorf("Failed to find process status document: %v", err)
		return
	}

	var doc map[string]interface{}
	if err := result.Decode(&doc); err != nil {
		t.Errorf("Failed to decode document: %v", err)
		return
	}

	if doc["status"] != RunningNormal {
		t.Errorf("Expected status %s, got %s", RunningNormal, doc["status"])
	}

	// Wait for monitorLoop to run
	// 5 seconds will cause the status to be RunningError
	time.Sleep(5 * time.Second)

	// Verify that the status was updated to RunningError
	result = coll.FindOne(context.Background(), bson.M{"_id": monitor.id + "_" + gHostName})
	if err := result.Err(); err != nil {
		if err.Error() == "mongo: no documents in result" {
			t.Error("Expected to find process status document")
			return
		}
		t.Errorf("Failed to find process status document: %v", err)
		return
	}

	if err := result.Decode(&doc); err != nil {
		t.Errorf("Failed to decode document: %v", err)
		return
	}

	if doc["status"] != RunningWithWarning {
		t.Errorf("Expected status %s, got %s", RunningWithWarning, doc["status"])
	}

	// Test stopping the monitor
	monitor.StopMonitoring()
}

// TestProcessMonitorUpdateStatusWithErrorAndStats tests updating status with both error message and stats
func TestProcessMonitorUpdateStatusWithErrorAndStats(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}
	defer monitor.StopMonitoring()

	// Reset monitor state
	monitor.Reset()
	time.Sleep(2 * time.Second) // Ensure goroutine exits and restarts

	// Clean MongoDB collection
	if _, err := coll.DeleteMany(context.Background(), bson.M{}); err != nil {
		t.Errorf("Failed to cleanup collection: %v", err)
	}

	// Test updating status with error message and stats
	errorMsg := "test error"
	stats := map[string]interface{}{
		"processed": 100,
		"errors":    5,
		"duration":  "2h",
	}

	err = monitor.UpdateProcessStatus(UpdateProcessStatusOptions{
		Status:   RunningWithWarning,
		ErrorMsg: &errorMsg,
		Stats:    stats,
	})
	if err != nil {
		t.Errorf("Failed to update status with error and stats: %v", err)
	}
	time.Sleep(100 * time.Millisecond)

	// Verify error message and stats
	result := coll.FindOne(context.Background(), bson.M{"_id": monitor.id + "_" + gHostName})
	if err := result.Err(); err != nil {
		t.Error("Failed to find process status document")
		return
	}

	var doc map[string]interface{}
	if err := result.Decode(&doc); err != nil {
		t.Errorf("Failed to decode document: %v", err)
		return
	}

	if doc["stats"] == nil {
		t.Error("Expected stats to be set")
	}
}

// TestProcessMonitorStatusUpdateWithStats tests status updates with statistics
func TestProcessMonitorStatusUpdateWithStats(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}
	defer monitor.StopMonitoring()

	// Reset monitor state
	monitor.Reset()
	time.Sleep(2 * time.Second) // Ensure goroutine exits and restarts

	// Clean MongoDB collection
	if _, err := coll.DeleteMany(context.Background(), bson.M{}); err != nil {
		t.Errorf("Failed to cleanup collection: %v", err)
	}

	// Test updating status with stats
	stats := map[string]interface{}{
		"processed": 100,
		"errors":    5,
		"duration":  "2h",
	}

	err = monitor.UpdateProcessStatus(UpdateProcessStatusOptions{
		Status: RunningNormal,
		Stats:  stats,
	})
	if err != nil {
		t.Errorf("Failed to update status with stats: %v", err)
	}
	time.Sleep(100 * time.Millisecond)

	// Verify stats in MongoDB
	result := coll.FindOne(context.Background(), bson.M{"_id": monitor.id + "_" + gHostName})
	if err := result.Err(); err != nil {
		t.Error("Failed to find process status document")
		return
	}

	var doc map[string]interface{}
	if err := result.Decode(&doc); err != nil {
		t.Errorf("Failed to decode document: %v", err)
		return
	}

	if doc["stats"] == nil {
		t.Error("Expected stats to be set")
	}

	// Test updating status with error message and stats
	errorMsg := "test error"
	err = monitor.UpdateProcessStatus(UpdateProcessStatusOptions{
		Status:   RunningWithWarning,
		ErrorMsg: &errorMsg,
		Stats:    stats,
	})
	if err != nil {
		t.Errorf("Failed to update status with error and stats: %v", err)
	}
	time.Sleep(100 * time.Millisecond)

	// Verify error message and stats
	result = coll.FindOne(context.Background(), bson.M{"_id": monitor.id + "_" + gHostName})
	if err := result.Err(); err != nil {
		t.Error("Failed to find process status document")
		return
	}

	if err := result.Decode(&doc); err != nil {
		t.Errorf("Failed to decode document: %v", err)
		return
	}

	if doc["notify"] != errorMsg {
		t.Errorf("Expected error message %s, got %v", errorMsg, doc["notify"])
	}
	if doc["stats"] == nil {
		t.Error("Expected stats to be set")
	}
}

// TestProcessMonitorExitFunctions tests different types of exit functions
func TestProcessMonitorExitFunctions(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}
	defer monitor.StopMonitoring()

	// Stop monitoring to prevent status updates from monitor loop
	monitor.StopMonitoring()
	time.Sleep(1 * time.Second) // Ensure goroutine exits

	// Only test with error message and exit function to avoid race
	errorMsg := "test error"
	monitor.UpdateStatusWhenAllFinish(errorMsg, func(msg string) {
		if msg != errorMsg {
			t.Errorf("Expected error message '%s', got '%s'", errorMsg, msg)
		}
	})
	time.Sleep(1 * time.Second) // Wait for MongoDB to update

	// Verify status in MongoDB
	result := coll.FindOne(context.Background(), bson.M{"_id": monitor.id + "_" + gHostName})
	if err := result.Err(); err != nil {
		t.Error("Failed to find process status document")
		return
	}

	var doc map[string]interface{}
	if err := result.Decode(&doc); err != nil {
		t.Errorf("Failed to decode document: %v", err)
		return
	}

	if doc["status"] != FinishWithError {
		t.Errorf("Expected status %s, got %s", FinishWithError, doc["status"])
	}
}

func TestProcessMonitorMultiplier(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		UpdateInterval:   time.Second,
		Multiplier:       1.0,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Test different multiplier values
	testCases := []struct {
		multiplier float64
		expected   float64
	}{
		{1.0, 1.0},
		{2.0, 2.0},
		{0.5, 0.5},
		{1.5, 1.5},
	}

	for _, tc := range testCases {
		monitor.SetMultiplier(tc.multiplier)
		if monitor.multiplier != tc.expected {
			t.Errorf("Expected multiplier %v, got %v", tc.expected, monitor.multiplier)
		}
	}
}

func TestProcessMonitorOperationTracking(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Record multiple operations
	operation := "test-operation"
	for i := 0; i < 15; i++ { // More than 10 to test the limit
		monitor.Tick(operation)
		time.Sleep(10 * time.Millisecond)
	}

	monitor.mu.Lock()
	times := monitor.operationTimes[operation]
	if len(times) != 10 {
		t.Errorf("Expected 10 recorded times (max limit), got %d", len(times))
	}
	monitor.mu.Unlock()
}

func TestProcessMonitorErrorStatus(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Record an operation
	operation := "test-operation"
	monitor.Tick(operation)

	// Wait longer than the tolerance
	time.Sleep(2 * time.Second)

	// Check operations - should set RunningError status
	monitor.checkOperations()

	// Verify that the status was updated to RunningError
	result := gomongo.Coll("tmp", "processStatus").FindOne(context.Background(), bson.M{"_id": monitor.id + "_" + gHostName})
	if err := result.Err(); err != nil {
		t.Error("Failed to find process status document")
		return
	}

	var doc map[string]interface{}
	if err := result.Decode(&doc); err != nil {
		t.Errorf("Failed to decode document: %v", err)
		return
	}

	if doc["status"] != RunningWithWarning {
		t.Errorf("Expected status %s, got %s", RunningWithWarning, doc["status"])
	}

	// Verify that an error message was set
	if doc["notify"] == nil {
		t.Error("Expected error message to be set")
	}
}

func TestProcessMonitorStatusUpdate(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Test status update with all parameters
	now := time.Now()
	errorMsg := "test error"
	stats := map[string]interface{}{
		"processed": 100,
		"errors":    0,
	}

	opts := UpdateProcessStatusOptions{
		Status:   RunningNormal,
		StartTs:  &now,
		ErrorMsg: &errorMsg,
		Stats:    stats,
	}
	err = monitor.UpdateProcessStatus(opts)
	if err != nil {
		t.Errorf("Error updating status: %v", err)
	}

	// Test status update with minimal parameters
	opts = UpdateProcessStatusOptions{
		Status: RunningNormal,
	}
	err = monitor.UpdateProcessStatus(opts)
	if err != nil {
		t.Errorf("Error updating status: %v", err)
	}
}

func TestProcessMonitorUpdateStatusWhenAllFinish(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Test with error message
	errorMsg := "test error"
	monitor.UpdateStatusWhenAllFinish(errorMsg, func(msg string) {
		if msg != errorMsg {
			t.Errorf("Expected error message '%s', got '%s'", errorMsg, msg)
		}
	})

	// Verify status in MongoDB
	result := coll.FindOne(context.Background(), bson.M{"_id": monitor.id + "_" + gHostName})
	if err := result.Err(); err != nil {
		t.Error("Failed to find process status document")
		return
	}

	var doc map[string]interface{}
	if err := result.Decode(&doc); err != nil {
		t.Errorf("Failed to decode document: %v", err)
		return
	}

	if doc["status"] != FinishWithError {
		t.Errorf("Expected status %s, got %s", FinishWithError, doc["status"])
	}

	// Test without error message
	monitor.UpdateStatusWhenAllFinish("", func(msg string) {
		if msg != "" {
			t.Errorf("Expected empty error message, got '%s'", msg)
		}
	})

	// Verify status in MongoDB
	result = coll.FindOne(context.Background(), bson.M{"_id": monitor.id + "_" + gHostName})
	if err := result.Err(); err != nil {
		t.Error("Failed to find process status document")
		return
	}

	if err := result.Decode(&doc); err != nil {
		t.Errorf("Failed to decode document: %v", err)
		return
	}

	if doc["status"] != FinishWithoutError {
		t.Errorf("Expected status %s, got %s", FinishWithoutError, doc["status"])
	}
}

func TestProcessMonitorRegisterPattern(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}
	defer monitor.StopMonitoring()

	// Create a channel to track function execution
	executed := make(chan bool, 1)

	// Register a test function
	monitor.RegisterMonitorFunc("test_func", func() {
		select {
		case executed <- true:
		default:
		}
	})

	// Wait for the monitor loop to execute the registered function
	select {
	case <-executed:
		// Function was executed successfully
	case <-time.After(2 * time.Second):
		t.Error("Registered function was not executed within expected time")
	}

	// Test multiple registered functions
	executionCount := make(map[string]int)
	expectedCount := 3

	for i := 0; i < expectedCount; i++ {
		name := fmt.Sprintf("func_%d", i)
		currentName := name
		monitor.RegisterMonitorFunc(currentName, func() {
			executionCount[currentName]++
		})
	}

	// Wait for multiple executions
	time.Sleep(3 * time.Second)

	// Verify that each function was executed at least once
	for i := 0; i < expectedCount; i++ {
		name := fmt.Sprintf("func_%d", i)
		if count := executionCount[name]; count == 0 {
			t.Errorf("Function %s was not executed", name)
		}
	}

	// Test unregistering a function
	monitor.UnregisterMonitorFunc("func_0")
	initialCount := executionCount["func_0"]
	time.Sleep(2 * time.Second)

	// Verify that unregistered function is not executed again
	if count := executionCount["func_0"]; count != initialCount {
		t.Errorf("Unregistered function was executed again, count changed from %d to %d", initialCount, count)
	}

	// Test panic recovery in registered functions
	panicChan := make(chan struct{}, 1)
	monitor.RegisterMonitorFunc("panic_func", func() {
		select {
		case panicChan <- struct{}{}:
		default:
		}
		panic("test panic")
	})

	// Wait for the panic function to execute
	select {
	case <-panicChan:
		// Panic function was executed
	case <-time.After(2 * time.Second):
		t.Error("Panic function was not executed within expected time")
	}

	// Clean up all channels
	close(executed)
	close(panicChan)
}

func TestProcessMonitorMaxOperationTimes(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	// Test with custom MaxOperationTimes
	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol:  coll,
		UpdateInterval:    time.Second,
		MaxOperationTimes: 5,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Record operations
	operation := "test-operation"
	for i := 0; i < 10; i++ {
		monitor.Tick(operation)
		time.Sleep(10 * time.Millisecond)
	}

	monitor.mu.Lock()
	times := monitor.operationTimes[operation]
	if len(times) != 5 {
		t.Errorf("Expected 5 recorded times (custom max limit), got %d", len(times))
	}
	monitor.mu.Unlock()
}

func TestProcessMonitorEmptyOperation(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Test empty operation name
	monitor.Tick("")
	monitor.mu.Lock()
	if len(monitor.operationTimes) != 0 {
		t.Error("Expected no operations to be recorded for empty operation name")
	}
	monitor.mu.Unlock()
}

func TestProcessMonitorRegisterUnregister(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Test registering a function
	funcCalled := false
	testFunc := func() {
		funcCalled = true
	}
	monitor.RegisterMonitorFunc("test", testFunc)

	// Wait for monitor loop to run
	time.Sleep(2 * time.Second)

	if !funcCalled {
		t.Error("Expected registered function to be called")
	}

	// Test unregistering the function
	funcCalled = false
	monitor.UnregisterMonitorFunc("test")
	time.Sleep(2 * time.Second)

	if funcCalled {
		t.Error("Expected unregistered function not to be called")
	}
}

func TestProcessMonitorPanicRecovery(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Register a function that panics
	monitor.RegisterMonitorFunc("panic", func() {
		panic("test panic")
	})

	// Wait for monitor loop to run
	time.Sleep(2 * time.Second)

	// If we reach here, the panic was recovered
	monitor.StopMonitoring()
}

func TestProcessMonitorDefaultValues(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	// Test with minimal options
	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Verify default values
	if monitor.updateInterval != 5*time.Minute {
		t.Errorf("Expected default update interval of 5 minutes, got %v", monitor.updateInterval)
	}
	if monitor.multiplier != 1.0 {
		t.Errorf("Expected default multiplier of 1.0, got %v", monitor.multiplier)
	}
	if monitor.maxOperationTimes != 10 {
		t.Errorf("Expected default max operation times of 10, got %v", monitor.maxOperationTimes)
	}
}

func TestProcessMonitorUpdateStatusWithStats(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Test updating status with stats
	stats := map[string]interface{}{
		"processed": 100,
		"errors":    5,
		"duration":  "2h",
	}

	err = monitor.UpdateProcessStatus(UpdateProcessStatusOptions{
		Status: RunningNormal,
		Stats:  stats,
	})
	if err != nil {
		t.Errorf("Failed to update status with stats: %v", err)
	}

	// Verify stats in MongoDB
	result := coll.FindOne(context.Background(), bson.M{"_id": monitor.id + "_" + gHostName})
	if err := result.Err(); err != nil {
		t.Error("Failed to find process status document")
		return
	}

	var doc map[string]interface{}
	if err := result.Decode(&doc); err != nil {
		t.Errorf("Failed to decode document: %v", err)
		return
	}

	if doc["stats"] == nil {
		t.Error("Expected stats to be set")
	}
}

func TestProcessMonitorUpdateStatusWithError(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Test updating status with error message
	errorMsg := "test error message"
	err = monitor.UpdateProcessStatus(UpdateProcessStatusOptions{
		Status:   RunningWithWarning,
		ErrorMsg: &errorMsg,
	})
	if err != nil {
		t.Errorf("Failed to update status with error: %v", err)
	}

	// Verify error message in MongoDB
	result := coll.FindOne(context.Background(), bson.M{"_id": monitor.id + "_" + gHostName})
	if err := result.Err(); err != nil {
		t.Error("Failed to find process status document")
		return
	}

	var doc map[string]interface{}
	if err := result.Decode(&doc); err != nil {
		t.Errorf("Failed to decode document: %v", err)
		return
	}

	if doc["notify"] != errorMsg {
		t.Errorf("Expected error message %s, got %s", errorMsg, doc["notify"])
	}
}

func TestProcessMonitorUpdateStatusWithStartTime(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Test updating status with start time
	startTime := time.Now()
	err = monitor.UpdateProcessStatus(UpdateProcessStatusOptions{
		Status:  RunningNormal,
		StartTs: &startTime,
	})
	if err != nil {
		t.Errorf("Failed to update status with start time: %v", err)
	}

	// Verify start time in MongoDB
	result := coll.FindOne(context.Background(), bson.M{"_id": monitor.id + "_" + gHostName})
	if err := result.Err(); err != nil {
		t.Error("Failed to find process status document")
		return
	}

	var doc map[string]interface{}
	if err := result.Decode(&doc); err != nil {
		t.Errorf("Failed to decode document: %v", err)
		return
	}

	if doc["startTs"] == nil {
		t.Error("Expected start time to be set")
	}
}

// TestProcessMonitorConcurrentUpdates tests concurrent status updates
func TestProcessMonitorConcurrentUpdates(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}
	defer monitor.StopMonitoring()

	// Create multiple goroutines to update status concurrently
	done := make(chan struct{})
	for i := 0; i < 5; i++ {
		go func(i int) {
			defer func() { done <- struct{}{} }()
			status := RunningNormal
			if i%2 == 0 {
				status = RunningWithWarning
			}
			err := monitor.UpdateProcessStatus(UpdateProcessStatusOptions{
				Status: status,
			})
			if err != nil {
				t.Errorf("Failed to update status: %v", err)
			}
		}(i)
	}

	// Wait for all updates to complete
	for i := 0; i < 5; i++ {
		<-done
	}

	// Verify final status
	time.Sleep(time.Second)
	running, err := monitor.CheckRunningProcess()
	if err != nil {
		t.Errorf("Unexpected error checking running process: %v", err)
	}
	if !running {
		t.Error("Expected running process after concurrent updates")
	}
}

// TestProcessMonitorOperationIntervals tests operation interval tracking
func TestProcessMonitorOperationIntervals(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol:  coll,
		UpdateInterval:    time.Second,
		MaxOperationTimes: 3,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}
	defer monitor.StopMonitoring()

	// Record operations with different intervals
	operation := "test-operation"
	monitor.Tick(operation)
	time.Sleep(100 * time.Millisecond)
	monitor.Tick(operation)
	time.Sleep(200 * time.Millisecond)
	monitor.Tick(operation)
	time.Sleep(300 * time.Millisecond)
	monitor.Tick(operation)

	// Verify operation times
	monitor.mu.Lock()
	times := monitor.operationTimes[operation]
	if len(times) != 3 {
		t.Errorf("Expected 3 recorded times, got %d", len(times))
	}

	// Verify max interval
	maxInterval := monitor.maxIntervals[operation]
	if maxInterval == 0 {
		t.Error("Expected non-zero max interval")
	}
	monitor.mu.Unlock()

	// Wait for status update
	time.Sleep(time.Second)

	// Verify status
	running, err := monitor.CheckRunningProcess()
	if err != nil {
		t.Errorf("Unexpected error checking running process: %v", err)
	}
	if !running {
		t.Error("Expected running process")
	}
}

// TestProcessMonitorMonitorLoop tests the monitor loop functionality
func TestProcessMonitorMonitorLoop(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}
	defer monitor.StopMonitoring()

	// Register a test function that will be called in monitor loop
	executed := make(chan struct{})
	monitor.RegisterMonitorFunc("test_func", func() {
		select {
		case executed <- struct{}{}:
		default:
		}
	})

	// Wait for the monitor loop to execute the registered function
	select {
	case <-executed:
		// Function was executed successfully
	case <-time.After(2 * time.Second):
		t.Error("Registered function was not executed within expected time")
	}

	// Test panic recovery in monitor loop
	panicChan := make(chan struct{})
	monitor.RegisterMonitorFunc("panic_func", func() {
		select {
		case panicChan <- struct{}{}:
		default:
		}
		panic("test panic")
	})

	// Wait for the panic function to execute
	select {
	case <-panicChan:
		// Panic function was executed
	case <-time.After(2 * time.Second):
		t.Error("Panic function was not executed within expected time")
	}
}

// TestProcessMonitorOperationTrackingWithWarning tests operation tracking with warning scenarios
func TestProcessMonitorOperationTrackingWithWarning(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol:  coll,
		UpdateInterval:    time.Second,
		MaxOperationTimes: 3,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}

	// Stop monitoring to prevent status updates
	monitor.StopMonitoring()

	// Test operation with warning
	monitor.Tick("warning_op")
	time.Sleep(2 * time.Second)
	monitor.checkOperations()

	// Verify warning status
	result := coll.FindOne(context.Background(), bson.M{"_id": monitor.id + "_" + gHostName})
	if err := result.Err(); err != nil {
		t.Error("Failed to find process status document")
		return
	}

	var doc map[string]interface{}
	if err := result.Decode(&doc); err != nil {
		t.Errorf("Failed to decode document: %v", err)
		return
	}

	if doc["status"] != RunningWithWarning {
		t.Errorf("Expected status %s, got %s", RunningWithWarning, doc["status"])
	}
}

// TestProcessMonitorUpdateStatusErrors tests error scenarios in UpdateProcessStatus
func TestProcessMonitorUpdateStatusErrors(t *testing.T) {
	coll, cleanup := setupTestProcess(t)
	defer cleanup(coll)

	monitor, err := NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: coll,
		UpdateInterval:   time.Second,
	})
	if err != nil {
		t.Fatalf("Failed to create process monitor: %v", err)
	}
	defer monitor.StopMonitoring()

	// Test empty status
	err = monitor.UpdateProcessStatus(UpdateProcessStatusOptions{
		Status: "",
	})
	if err == nil {
		t.Error("Expected error for empty status")
	}

	// Test with invalid MongoDB collection
	_, err = NewProcessMonitor(ProcessMonitorOptions{
		ProcessStatusCol: nil,
		UpdateInterval:   time.Second,
	})
	if err == nil {
		t.Error("Expected error for nil ProcessStatusCol")
	}

	// Test with invalid status
	err = monitor.UpdateProcessStatus(UpdateProcessStatusOptions{
		Status: "InvalidStatus",
	})
	if err != nil {
		t.Errorf("Unexpected error for invalid status: %v", err)
	}
}
