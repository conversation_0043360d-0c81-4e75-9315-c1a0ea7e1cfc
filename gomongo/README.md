# go-mongo

A Go language MongoDB API wrapper that provides a simple and efficient way to interact with MongoDB databases.

## Features

- Simple and intuitive API for MongoDB operations
- Automatic timestamp management for documents
- Configurable database connections
- Predefined collection support
- Comprehensive CRUD operations
- Support for MongoDB aggregation and change streams
- **MongoDB Transaction Support** with ACID compliance
- Built-in logging and performance monitoring

## Installation

```bash
go get github.com/real-rm/gomongo
```

## Configuration

The package uses a configuration system that supports TOML format. You need to configure your MongoDB connections and predefined collections in your config file:

```toml
[dbs]
verbose = 1  # Logging verbosity level
[dbs.mydb]
uri = "mongodb://localhost:27017/mydatabase"


```

## Usage

### Initialization

```go
import "github.com/real-rm/gomongo"

func main() {
    err := gomongo.InitMongoDB()
    if err != nil {
        log.Fatal(err)
    }
}
```

### Basic Operations

#### Get a Collection

```go
// Get a collection directly
collection := gomongo.Coll("mydb", "users")

// Or get a database first
db := gomongo.Database("mydb")
collection := db.Coll("users")
```

#### Insert Documents

```go
ctx := context.Background()
doc := bson.M{"name": "John", "age": 30}
result, err := collection.InsertOne(ctx, doc)
```

#### Find Documents

```go
// Find with options
opts := []interface{}{
    gomongo.QueryOptions{
        Projection: bson.M{"name": 1},
        Sort:      bson.M{"age": -1},
        Limit:     10,
        Skip:      0,
    },
}
cursor, err := collection.Find(ctx, bson.M{"age": bson.M{"$gt": 18}}, opts...)
```

#### Update Documents

```go
filter := bson.M{"name": "John"}
update := bson.M{"$set": bson.M{"age": 31}}
result, err := collection.UpdateOne(ctx, filter, update)
```

#### Delete Documents

```go
filter := bson.M{"name": "John"}
result, err := collection.DeleteOne(ctx, filter)
```

### Transactions

The package provides comprehensive transaction support for ACID operations across multiple documents and collections.

#### Basic Transaction

```go
// Start a session
session, err := gomongo.StartSession("mydb")
if err != nil {
    return err
}
defer session.EndSession()

// Start a transaction
txn, err := session.StartTransaction()
if err != nil {
    return err
}

// Get session context for operations
sessCtx := txn.GetSessionContext()

// Perform operations within transaction
usersColl := gomongo.Coll("mydb", "users")
accountsColl := gomongo.Coll("mydb", "accounts")

// Insert a user
userResult, err := usersColl.InsertOne(sessCtx, bson.M{
    "name":  "John Doe",
    "email": "<EMAIL>",
})
if err != nil {
    txn.AbortTransaction()
    return err
}

// Insert an account for the user
_, err = accountsColl.InsertOne(sessCtx, bson.M{
    "userId": userResult.InsertedID,
    "balance": 1000,
    "type":   "savings",
})
if err != nil {
    txn.AbortTransaction()
    return err
}

// Commit the transaction
err = txn.CommitTransaction()
if err != nil {
    return err
}
```

#### Using WithTransaction Helper

```go
// Start a session
session, err := gomongo.StartSession("mydb")
if err != nil {
    return err
}
defer session.EndSession()

// Execute transaction using WithTransaction helper
err = session.WithTransaction(func(sessCtx mongo.SessionContext) error {
    usersColl := gomongo.Coll("mydb", "users")
    accountsColl := gomongo.Coll("mydb", "accounts")

    // Insert a user
    userResult, err := usersColl.InsertOne(sessCtx, bson.M{
        "name":  "Jane Doe",
        "email": "<EMAIL>",
    })
    if err != nil {
        return err
    }

    // Insert an account for the user
    _, err = accountsColl.InsertOne(sessCtx, bson.M{
        "userId": userResult.InsertedID,
        "balance": 2000,
        "type":   "checking",
    })
    return err
})

if err != nil {
    return err
}
```

#### Money Transfer Example

```go
func TransferMoney(fromUserId, toUserId interface{}, amount float64) error {
    session, err := gomongo.StartSession("mydb")
    if err != nil {
        return err
    }
    defer session.EndSession()

    return session.WithTransaction(func(sessCtx mongo.SessionContext) error {
        accountsColl := gomongo.Coll("mydb", "accounts")

        // Deduct from source account
        result, err := accountsColl.UpdateOne(
            sessCtx,
            bson.M{"userId": fromUserId},
            bson.M{"$inc": bson.M{"balance": -amount}},
        )
        if err != nil || result.ModifiedCount == 0 {
            return fmt.Errorf("failed to deduct from source account")
        }

        // Add to destination account
        result, err = accountsColl.UpdateOne(
            sessCtx,
            bson.M{"userId": toUserId},
            bson.M{"$inc": bson.M{"balance": amount}},
        )
        if err != nil || result.ModifiedCount == 0 {
            return fmt.Errorf("failed to add to destination account")
        }

        // Log the transfer
        transfersColl := gomongo.Coll("mydb", "transfers")
        _, err = transfersColl.InsertOne(sessCtx, bson.M{
            "fromUserId": fromUserId,
            "toUserId":   toUserId,
            "amount":     amount,
            "timestamp":  time.Now(),
        })
        return err
    })
}
```

#### Context with Timeout

```go
// Create context with timeout
ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
defer cancel()

// Start session with context
session, err := gomongo.StartSessionWithContext(ctx, "mydb")
if err != nil {
    return err
}
defer session.EndSession()

// Execute transaction with timeout
err = session.WithTransaction(func(sessCtx mongo.SessionContext) error {
    // Your transaction operations here
    usersColl := gomongo.Coll("mydb", "users")
    _, err := usersColl.InsertOne(sessCtx, bson.M{
        "name":  "Timeout Test User",
        "email": "<EMAIL>",
    })
    return err
})
```

## API Reference

### Collection Methods

- `InsertOne(ctx context.Context, document interface{}, opts ...*options.InsertOneOptions)`
- `InsertMany(ctx context.Context, documents []interface{}, opts ...*options.InsertManyOptions)`
- `Find(ctx context.Context, filter interface{}, opts ...interface{})`
- `FindOne(ctx context.Context, filter interface{}, opts ...interface{})`
- `UpdateOne(ctx context.Context, filter interface{}, update interface{}, opts ...*options.UpdateOptions)`
- `UpdateMany(ctx context.Context, filter interface{}, update interface{}, opts ...*options.UpdateOptions)`
- `DeleteOne(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions)`
- `DeleteMany(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions)`
- `FindOneAndUpdate(ctx context.Context, filter interface{}, update interface{}, opts ...*options.FindOneAndUpdateOptions)`
- `ReplaceOne(ctx context.Context, filter interface{}, replacement interface{}, opts ...*options.ReplaceOptions)`
- `FindOneAndReplace(ctx context.Context, filter interface{}, replacement interface{}, opts ...*options.FindOneAndReplaceOptions)`
- `Distinct(ctx context.Context, fieldName string, filter interface{}, opts ...*options.DistinctOptions)`
- `CountDocuments(ctx context.Context, filter interface{}, opts ...*options.CountOptions)`
- `EstimatedDocumentCount(ctx context.Context, opts ...*options.EstimatedDocumentCountOptions)`
- `CreateIndex(ctx context.Context, model mongo.IndexModel, opts ...*options.CreateIndexesOptions)`
- `CreateIndexes(ctx context.Context, models []mongo.IndexModel, opts ...*options.CreateIndexesOptions)`
- `DropIndex(ctx context.Context, name string, opts ...*options.DropIndexesOptions)`
- `Aggregate(ctx context.Context, pipeline interface{}, opts ...*options.AggregateOptions)`
- `Watch(ctx context.Context, pipeline interface{}, opts ...*options.ChangeStreamOptions)`
- `Rename(ctx context.Context, newName string)`
- `FindToArray(ctx context.Context, filter interface{}, opts ...interface{})`
- `Drop(ctx context.Context)`
- `BulkWrite(ctx context.Context, models []mongo.WriteModel, opts ...*options.BulkWriteOptions)`

### Transaction Methods

#### Session Management
- `StartSession(dbName string) (*MongoSession, error)`
- `StartSessionWithContext(ctx context.Context, dbName string) (*MongoSession, error)`
- `EndSession()` - Ends the session and releases resources

#### Transaction Control
- `StartTransaction(opts ...*TransactionOptions) (*MongoTransaction, error)`
- `CommitTransaction() error`
- `AbortTransaction() error`
- `WithTransaction(fn func(mongo.SessionContext) error, opts ...*TransactionOptions) error`

#### Context Management
- `GetSessionContext() mongo.SessionContext`
- `GetTransactionContext() mongo.SessionContext`

### Query Options

```go
type QueryOptions struct {
    Projection interface{}  // Fields to return
    Sort       interface{}  // Sort order
    Limit      int64       // Maximum number of documents to return
    Skip       int64       // Number of documents to skip
}
```

### Transaction Options

```go
type TransactionOptions struct {
    ReadConcern  *readconcern.ReadConcern
    WriteConcern *writeconcern.WriteConcern
}
```

## Automatic Timestamps

The package automatically adds timestamps to documents:
- `_ts`: Creation timestamp
- `_mt`: Last modification timestamp

## Error Handling

All operations return errors that should be checked:

```go
result, err := collection.InsertOne(ctx, doc)
if err != nil {
    log.Printf("Error inserting document: %v", err)
    return
}
```

## Performance Monitoring

The package includes built-in performance monitoring that logs slow operations. The verbosity level can be configured in the database configuration.

## Transaction Best Practices

### 1. Always Use defer for Session Cleanup
```go
session, err := gomongo.StartSession("mydb")
if err != nil {
    return err
}
defer session.EndSession() // Always defer session cleanup
```

### 2. Use WithTransaction for Simple Cases
The `WithTransaction` helper automatically handles commit/abort logic, making it safer and easier to use for most cases.

### 3. Handle Errors Properly
Always check for errors and abort transactions when operations fail.

### 4. Use Appropriate Read/Write Concerns
Choose read and write concerns based on your consistency requirements:

```go
txnOpts := &gomongo.TransactionOptions{
    ReadConcern:  readconcern.Snapshot(),    // For consistent reads
    WriteConcern: writeconcern.Majority(),   // For durability
}
```

### 5. Keep Transactions Short
Long-running transactions can impact performance and increase the risk of conflicts. Keep transaction operations focused and efficient.

### 6. Use Context for Timeouts
Always use context with appropriate timeouts to prevent hanging operations.

## Limitations

1. **MongoDB Version**: Transactions require MongoDB 4.0+ for replica sets and 4.2+ for sharded clusters
2. **Collection Requirements**: Collections must exist before starting transactions
3. **Operation Restrictions**: Some operations (like creating/dropping collections) cannot be performed within transactions
4. **Size Limits**: Transactions have size limits (16MB for the total size of all operations)

## Dependencies

- go.mongodb.org/mongo-driver/mongo
- github.com/real-rm/goconfig
- github.com/real-rm/golog
