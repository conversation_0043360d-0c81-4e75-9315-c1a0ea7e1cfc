<!-- Code generated by gomarkdoc. DO NOT EDIT -->

# gomongo

```go
import "github.com/real-rm/gomongo"
```

## Index

- [func InitMongoDB\(\) error](<#InitMongoDB>)
- [func ListCollections\(dbName string\) \(\[\]string, error\)](<#ListCollections>)
- [func ToBSONDoc\(v interface\{\}\) \(bson.M, error\)](<#ToBSONDoc>)
- [type MongoCollection](<#MongoCollection>)
  - [func Coll\(dbName, collName string\) \*MongoCollection](<#Coll>)
  - [func PreDefColl\(collName string\) \*MongoCollection](<#PreDefColl>)
  - [func \(mc \*MongoCollection\) Aggregate\(ctx context.Context, pipeline interface\{\}, opts ...\*options.AggregateOptions\) \(\*mongo.Cursor, error\)](<#MongoCollection.Aggregate>)
  - [func \(mc \*MongoCollection\) BulkWrite\(ctx context.Context, models \[\]mongo.WriteModel, opts ...\*options.BulkWriteOptions\) \(\*mongo.BulkWriteResult, error\)](<#MongoCollection.BulkWrite>)
  - [func \(mc \*MongoCollection\) CountDocuments\(ctx context.Context, filter interface\{\}, opts ...\*options.CountOptions\) \(int64, error\)](<#MongoCollection.CountDocuments>)
  - [func \(mc \*MongoCollection\) CreateIndex\(ctx context.Context, model mongo.IndexModel, opts ...\*options.CreateIndexesOptions\) \(string, error\)](<#MongoCollection.CreateIndex>)
  - [func \(mc \*MongoCollection\) CreateIndexes\(ctx context.Context, models \[\]mongo.IndexModel, opts ...\*options.CreateIndexesOptions\) \(\[\]string, error\)](<#MongoCollection.CreateIndexes>)
  - [func \(mc \*MongoCollection\) DBName\(\) string](<#MongoCollection.DBName>)
  - [func \(mc \*MongoCollection\) Database\(\) \*mongo.Database](<#MongoCollection.Database>)
  - [func \(mc \*MongoCollection\) DeleteMany\(ctx context.Context, filter interface\{\}, opts ...\*options.DeleteOptions\) \(\*mongo.DeleteResult, error\)](<#MongoCollection.DeleteMany>)
  - [func \(mc \*MongoCollection\) DeleteOne\(ctx context.Context, filter interface\{\}, opts ...\*options.DeleteOptions\) \(\*mongo.DeleteResult, error\)](<#MongoCollection.DeleteOne>)
  - [func \(mc \*MongoCollection\) Distinct\(ctx context.Context, fieldName string, filter interface\{\}, opts ...\*options.DistinctOptions\) \(\[\]interface\{\}, error\)](<#MongoCollection.Distinct>)
  - [func \(mc \*MongoCollection\) Drop\(ctx context.Context\) error](<#MongoCollection.Drop>)
  - [func \(mc \*MongoCollection\) DropIndex\(ctx context.Context, name string, opts ...\*options.DropIndexesOptions\) error](<#MongoCollection.DropIndex>)
  - [func \(mc \*MongoCollection\) EstimatedDocumentCount\(ctx context.Context, opts ...\*options.EstimatedDocumentCountOptions\) \(int64, error\)](<#MongoCollection.EstimatedDocumentCount>)
  - [func \(mc \*MongoCollection\) Find\(ctx context.Context, filter interface\{\}, opts ...interface\{\}\) \(\*mongo.Cursor, error\)](<#MongoCollection.Find>)
  - [func \(mc \*MongoCollection\) FindOne\(ctx context.Context, filter interface\{\}, opts ...interface\{\}\) \*mongo.SingleResult](<#MongoCollection.FindOne>)
  - [func \(mc \*MongoCollection\) FindOneAndReplace\(ctx context.Context, filter interface\{\}, replacement interface\{\}, opts ...\*options.FindOneAndReplaceOptions\) \*mongo.SingleResult](<#MongoCollection.FindOneAndReplace>)
  - [func \(mc \*MongoCollection\) FindOneAndUpdate\(ctx context.Context, filter interface\{\}, update interface\{\}, opts ...\*options.FindOneAndUpdateOptions\) \*mongo.SingleResult](<#MongoCollection.FindOneAndUpdate>)
  - [func \(mc \*MongoCollection\) FindToArray\(ctx context.Context, filter interface\{\}, opts ...interface\{\}\) \(\[\]bson.M, error\)](<#MongoCollection.FindToArray>)
  - [func \(mc \*MongoCollection\) GetClient\(\) \*mongo.Client](<#MongoCollection.GetClient>)
  - [func \(mc \*MongoCollection\) InsertMany\(ctx context.Context, documents \[\]interface\{\}, opts ...\*options.InsertManyOptions\) \(\*mongo.InsertManyResult, error\)](<#MongoCollection.InsertMany>)
  - [func \(mc \*MongoCollection\) InsertOne\(ctx context.Context, document interface\{\}, opts ...\*options.InsertOneOptions\) \(\*mongo.InsertOneResult, error\)](<#MongoCollection.InsertOne>)
  - [func \(mc \*MongoCollection\) Name\(\) string](<#MongoCollection.Name>)
  - [func \(mc \*MongoCollection\) Ping\(ctx context.Context\) error](<#MongoCollection.Ping>)
  - [func \(mc \*MongoCollection\) Rename\(ctx context.Context, newName string\) error](<#MongoCollection.Rename>)
  - [func \(mc \*MongoCollection\) ReplaceOne\(ctx context.Context, filter interface\{\}, replacement interface\{\}, opts ...\*options.ReplaceOptions\) \(\*mongo.UpdateResult, error\)](<#MongoCollection.ReplaceOne>)
  - [func \(mc \*MongoCollection\) UpdateMany\(ctx context.Context, filter interface\{\}, update interface\{\}, opts ...\*options.UpdateOptions\) \(\*mongo.UpdateResult, error\)](<#MongoCollection.UpdateMany>)
  - [func \(mc \*MongoCollection\) UpdateOne\(ctx context.Context, filter interface\{\}, update interface\{\}, opts ...\*options.UpdateOptions\) \(\*mongo.UpdateResult, error\)](<#MongoCollection.UpdateOne>)
  - [func \(mc \*MongoCollection\) Watch\(ctx context.Context, pipeline interface\{\}, opts ...\*options.ChangeStreamOptions\) \(\*mongo.ChangeStream, error\)](<#MongoCollection.Watch>)
- [type MongoSession](<#MongoSession>)
  - [func StartSession\(dbName string\) \(\*MongoSession, error\)](<#StartSession>)
  - [func StartSessionWithContext\(ctx context.Context, dbName string\) \(\*MongoSession, error\)](<#StartSessionWithContext>)
  - [func \(ms \*MongoSession\) EndSession\(\)](<#MongoSession.EndSession>)
  - [func \(ms \*MongoSession\) GetSessionContext\(\) mongo.SessionContext](<#MongoSession.GetSessionContext>)
  - [func \(ms \*MongoSession\) StartTransaction\(opts ...\*TransactionOptions\) \(\*MongoTransaction, error\)](<#MongoSession.StartTransaction>)
  - [func \(ms \*MongoSession\) WithTransaction\(fn func\(mongo.SessionContext\) error, opts ...\*TransactionOptions\) error](<#MongoSession.WithTransaction>)
- [type MongoTransaction](<#MongoTransaction>)
  - [func \(mt \*MongoTransaction\) AbortTransaction\(\) error](<#MongoTransaction.AbortTransaction>)
  - [func \(mt \*MongoTransaction\) CommitTransaction\(\) error](<#MongoTransaction.CommitTransaction>)
  - [func \(mt \*MongoTransaction\) GetSessionContext\(\) mongo.SessionContext](<#MongoTransaction.GetSessionContext>)
- [type QueryOptions](<#QueryOptions>)
- [type TransactionOptions](<#TransactionOptions>)


<a name="InitMongoDB"></a>
## func [InitMongoDB](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L87>)

```go
func InitMongoDB() error
```

InitMongoDB initializes MongoDB connection using configuration

<a name="ListCollections"></a>
## func [ListCollections](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L826>)

```go
func ListCollections(dbName string) ([]string, error)
```



<a name="ToBSONDoc"></a>
## func [ToBSONDoc](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L804>)

```go
func ToBSONDoc(v interface{}) (bson.M, error)
```

ToBSONDoc converts a struct to a BSON document using struct tags. It marshals and unmarshals the struct to ensure BSON tags are properly applied.

<a name="MongoCollection"></a>
## type [MongoCollection](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L42-L44>)



```go
type MongoCollection struct {
    // contains filtered or unexported fields
}
```

<a name="Coll"></a>
### func [Coll](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L236>)

```go
func Coll(dbName, collName string) *MongoCollection
```

Coll returns a wrapped MongoDB collection

<a name="PreDefColl"></a>
### func [PreDefColl](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L250>)

```go
func PreDefColl(collName string) *MongoCollection
```

PreDefColl returns a wrapped MongoDB collection

<a name="MongoCollection.Aggregate"></a>
### func \(\*MongoCollection\) [Aggregate](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L682>)

```go
func (mc *MongoCollection) Aggregate(ctx context.Context, pipeline interface{}, opts ...*options.AggregateOptions) (*mongo.Cursor, error)
```



<a name="MongoCollection.BulkWrite"></a>
### func \(\*MongoCollection\) [BulkWrite](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L792>)

```go
func (mc *MongoCollection) BulkWrite(ctx context.Context, models []mongo.WriteModel, opts ...*options.BulkWriteOptions) (*mongo.BulkWriteResult, error)
```



<a name="MongoCollection.CountDocuments"></a>
### func \(\*MongoCollection\) [CountDocuments](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L632>)

```go
func (mc *MongoCollection) CountDocuments(ctx context.Context, filter interface{}, opts ...*options.CountOptions) (int64, error)
```



<a name="MongoCollection.CreateIndex"></a>
### func \(\*MongoCollection\) [CreateIndex](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L652>)

```go
func (mc *MongoCollection) CreateIndex(ctx context.Context, model mongo.IndexModel, opts ...*options.CreateIndexesOptions) (string, error)
```



<a name="MongoCollection.CreateIndexes"></a>
### func \(\*MongoCollection\) [CreateIndexes](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L662>)

```go
func (mc *MongoCollection) CreateIndexes(ctx context.Context, models []mongo.IndexModel, opts ...*options.CreateIndexesOptions) ([]string, error)
```



<a name="MongoCollection.DBName"></a>
### func \(\*MongoCollection\) [DBName](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L282>)

```go
func (mc *MongoCollection) DBName() string
```

DBName returns the name of the database that contains this collection

<a name="MongoCollection.Database"></a>
### func \(\*MongoCollection\) [Database](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L290>)

```go
func (mc *MongoCollection) Database() *mongo.Database
```

Database returns the underlying mongo.Database instance

<a name="MongoCollection.DeleteMany"></a>
### func \(\*MongoCollection\) [DeleteMany](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L568>)

```go
func (mc *MongoCollection) DeleteMany(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) (*mongo.DeleteResult, error)
```



<a name="MongoCollection.DeleteOne"></a>
### func \(\*MongoCollection\) [DeleteOne](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L558>)

```go
func (mc *MongoCollection) DeleteOne(ctx context.Context, filter interface{}, opts ...*options.DeleteOptions) (*mongo.DeleteResult, error)
```



<a name="MongoCollection.Distinct"></a>
### func \(\*MongoCollection\) [Distinct](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L622>)

```go
func (mc *MongoCollection) Distinct(ctx context.Context, fieldName string, filter interface{}, opts ...*options.DistinctOptions) ([]interface{}, error)
```



<a name="MongoCollection.Drop"></a>
### func \(\*MongoCollection\) [Drop](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L782>)

```go
func (mc *MongoCollection) Drop(ctx context.Context) error
```



<a name="MongoCollection.DropIndex"></a>
### func \(\*MongoCollection\) [DropIndex](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L672>)

```go
func (mc *MongoCollection) DropIndex(ctx context.Context, name string, opts ...*options.DropIndexesOptions) error
```



<a name="MongoCollection.EstimatedDocumentCount"></a>
### func \(\*MongoCollection\) [EstimatedDocumentCount](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L642>)

```go
func (mc *MongoCollection) EstimatedDocumentCount(ctx context.Context, opts ...*options.EstimatedDocumentCountOptions) (int64, error)
```



<a name="MongoCollection.Find"></a>
### func \(\*MongoCollection\) [Find](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L509>)

```go
func (mc *MongoCollection) Find(ctx context.Context, filter interface{}, opts ...interface{}) (*mongo.Cursor, error)
```



<a name="MongoCollection.FindOne"></a>
### func \(\*MongoCollection\) [FindOne](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L535>)

```go
func (mc *MongoCollection) FindOne(ctx context.Context, filter interface{}, opts ...interface{}) *mongo.SingleResult
```



<a name="MongoCollection.FindOneAndReplace"></a>
### func \(\*MongoCollection\) [FindOneAndReplace](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L612>)

```go
func (mc *MongoCollection) FindOneAndReplace(ctx context.Context, filter interface{}, replacement interface{}, opts ...*options.FindOneAndReplaceOptions) *mongo.SingleResult
```



<a name="MongoCollection.FindOneAndUpdate"></a>
### func \(\*MongoCollection\) [FindOneAndUpdate](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L578>)

```go
func (mc *MongoCollection) FindOneAndUpdate(ctx context.Context, filter interface{}, update interface{}, opts ...*options.FindOneAndUpdateOptions) *mongo.SingleResult
```



<a name="MongoCollection.FindToArray"></a>
### func \(\*MongoCollection\) [FindToArray](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L721>)

```go
func (mc *MongoCollection) FindToArray(ctx context.Context, filter interface{}, opts ...interface{}) ([]bson.M, error)
```

FindToArray executes a find operation and returns the results as a slice of bson.M

<a name="MongoCollection.GetClient"></a>
### func \(\*MongoCollection\) [GetClient](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L306>)

```go
func (mc *MongoCollection) GetClient() *mongo.Client
```

GetClient returns the underlying mongo.Client instance

<a name="MongoCollection.InsertMany"></a>
### func \(\*MongoCollection\) [InsertMany](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L763>)

```go
func (mc *MongoCollection) InsertMany(ctx context.Context, documents []interface{}, opts ...*options.InsertManyOptions) (*mongo.InsertManyResult, error)
```



<a name="MongoCollection.InsertOne"></a>
### func \(\*MongoCollection\) [InsertOne](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L446>)

```go
func (mc *MongoCollection) InsertOne(ctx context.Context, document interface{}, opts ...*options.InsertOneOptions) (*mongo.InsertOneResult, error)
```

Collection operation wrappers with timing and timestamps

<a name="MongoCollection.Name"></a>
### func \(\*MongoCollection\) [Name](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L274>)

```go
func (mc *MongoCollection) Name() string
```

Name returns the name of the collection

<a name="MongoCollection.Ping"></a>
### func \(\*MongoCollection\) [Ping](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L298>)

```go
func (mc *MongoCollection) Ping(ctx context.Context) error
```

Ping checks the connection to the MongoDB server

<a name="MongoCollection.Rename"></a>
### func \(\*MongoCollection\) [Rename](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L702>)

```go
func (mc *MongoCollection) Rename(ctx context.Context, newName string) error
```



<a name="MongoCollection.ReplaceOne"></a>
### func \(\*MongoCollection\) [ReplaceOne](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L602>)

```go
func (mc *MongoCollection) ReplaceOne(ctx context.Context, filter interface{}, replacement interface{}, opts ...*options.ReplaceOptions) (*mongo.UpdateResult, error)
```



<a name="MongoCollection.UpdateMany"></a>
### func \(\*MongoCollection\) [UpdateMany](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L485>)

```go
func (mc *MongoCollection) UpdateMany(ctx context.Context, filter interface{}, update interface{}, opts ...*options.UpdateOptions) (*mongo.UpdateResult, error)
```



<a name="MongoCollection.UpdateOne"></a>
### func \(\*MongoCollection\) [UpdateOne](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L461>)

```go
func (mc *MongoCollection) UpdateOne(ctx context.Context, filter interface{}, update interface{}, opts ...*options.UpdateOptions) (*mongo.UpdateResult, error)
```



<a name="MongoCollection.Watch"></a>
### func \(\*MongoCollection\) [Watch](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L692>)

```go
func (mc *MongoCollection) Watch(ctx context.Context, pipeline interface{}, opts ...*options.ChangeStreamOptions) (*mongo.ChangeStream, error)
```



<a name="MongoSession"></a>
## type [MongoSession](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L57-L60>)

MongoSession represents a MongoDB session for transaction management

```go
type MongoSession struct {
    // contains filtered or unexported fields
}
```

<a name="StartSession"></a>
### func [StartSession](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L867>)

```go
func StartSession(dbName string) (*MongoSession, error)
```

StartSession starts a new MongoDB session

<a name="StartSessionWithContext"></a>
### func [StartSessionWithContext](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L887>)

```go
func StartSessionWithContext(ctx context.Context, dbName string) (*MongoSession, error)
```

StartSessionWithContext starts a new MongoDB session with a specific context

<a name="MongoSession.EndSession"></a>
### func \(\*MongoSession\) [EndSession](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L999>)

```go
func (ms *MongoSession) EndSession()
```

EndSession ends the session

<a name="MongoSession.GetSessionContext"></a>
### func \(\*MongoSession\) [GetSessionContext](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L1055>)

```go
func (ms *MongoSession) GetSessionContext() mongo.SessionContext
```

GetSessionContext returns the session context for use in operations

<a name="MongoSession.StartTransaction"></a>
### func \(\*MongoSession\) [StartTransaction](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L912>)

```go
func (ms *MongoSession) StartTransaction(opts ...*TransactionOptions) (*MongoTransaction, error)
```

StartTransaction starts a new transaction within the session

<a name="MongoSession.WithTransaction"></a>
### func \(\*MongoSession\) [WithTransaction](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L1006>)

```go
func (ms *MongoSession) WithTransaction(fn func(mongo.SessionContext) error, opts ...*TransactionOptions) error
```

WithTransaction executes a function within a transaction

<a name="MongoTransaction"></a>
## type [MongoTransaction](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L63-L69>)

MongoTransaction represents a MongoDB transaction

```go
type MongoTransaction struct {
    // contains filtered or unexported fields
}
```

<a name="MongoTransaction.AbortTransaction"></a>
### func \(\*MongoTransaction\) [AbortTransaction](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L973>)

```go
func (mt *MongoTransaction) AbortTransaction() error
```

AbortTransaction aborts the transaction

<a name="MongoTransaction.CommitTransaction"></a>
### func \(\*MongoTransaction\) [CommitTransaction](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L947>)

```go
func (mt *MongoTransaction) CommitTransaction() error
```

CommitTransaction commits the transaction

<a name="MongoTransaction.GetSessionContext"></a>
### func \(\*MongoTransaction\) [GetSessionContext](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L1063>)

```go
func (mt *MongoTransaction) GetSessionContext() mongo.SessionContext
```

GetTransactionContext returns the transaction context for use in operations

<a name="QueryOptions"></a>
## type [QueryOptions](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L46-L52>)



```go
type QueryOptions struct {
    Projection interface{}
    Sort       interface{}
    Limit      int64
    Skip       int64
}
```

<a name="TransactionOptions"></a>
## type [TransactionOptions](<https://github.com/real-rm/gomongo/blob/main/mongodb.go#L72-L75>)

TransactionOptions defines options for starting a transaction

```go
type TransactionOptions struct {
    ReadConcern  *readconcern.ReadConcern
    WriteConcern *writeconcern.WriteConcern
}
```

Generated by [gomarkdoc](<https://github.com/princjef/gomarkdoc>)
