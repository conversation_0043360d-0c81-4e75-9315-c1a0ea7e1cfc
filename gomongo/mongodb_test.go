package gomongo

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"testing"
	"time"

	goconfig "github.com/real-rm/goconfig"
	golog "github.com/real-rm/golog"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readconcern"
	"go.mongodb.org/mongo-driver/mongo/writeconcern"
)

func setupTestMongoDB(t *testing.T) (cleanup func()) {
	// Save original env var
	originalCfg := os.Getenv("RMBASE_FILE_CFG")

	// Get the absolute path to local.test.ini
	// First get the absolute path of the current test file directory
	currentDir, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}

	// Then construct the path to config file
	configPath, err := filepath.Abs(filepath.Join(currentDir, "local.test.ini"))
	if err != nil {
		t.Fatalf("Failed to get absolute path: %v", err)
	}

	t.Logf("Using config file: %s", configPath)

	// Verify config file exists
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		t.Fatalf("Config file not found at %s: %v", configPath, err)
	}

	// Set environment variable to point to test config
	if err := os.Setenv("RMBASE_FILE_CFG", configPath); err != nil {
		t.Fatalf("Failed to set RMBASE_FILE_CFG: %v", err)
	}

	// Add verification of the environment variable
	currentValue := os.Getenv("RMBASE_FILE_CFG")
	t.Logf("RMBASE_FILE_CFG is now set to: %s", currentValue)

	if err := goconfig.LoadConfig(); err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	if err := golog.InitLog(); err != nil {
		t.Fatalf("Failed to initialize logging: %v", err)
	}

	if err := InitMongoDB(); err != nil {
		t.Fatalf("Failed to InitMongoDB: %v", err)
	}

	// Return cleanup function
	return func() {
		if err := os.Setenv("RMBASE_FILE_CFG", originalCfg); err != nil {
			t.Fatalf("Failed to restore RMBASE_FILE_CFG: %v", err)
		}
	}
}

func TestInitMongoDB(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()
}

func cleanupCollection(t *testing.T, coll *MongoCollection) {
	if _, err := coll.DeleteMany(context.Background(), bson.M{}); err != nil {
		t.Errorf("Failed to cleanup collection: %v", err)
	}
}

// getCleanCollection returns a clean collection and handles cleanup after test
func getCleanCollection(t *testing.T) *MongoCollection {
	coll := Coll("tmp", "goapp_test")
	cleanupCollection(t, coll)
	t.Cleanup(func() {
		cleanupCollection(t, coll)
	})
	return coll
}

func TestMongoOperations(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("test operations", func(t *testing.T) {
		// Test InsertOne
		t.Run("InsertOne", func(t *testing.T) {
			coll := getCleanCollection(t)

			ctx := context.Background()
			doc := bson.M{"test": "value"}

			result, err := coll.InsertOne(ctx, doc)
			if err != nil {
				t.Fatalf("InsertOne failed: %v", err)
			}
			if result.InsertedID == nil {
				t.Fatal("InsertOne didn't return an InsertedID")
			}

			// Verify the document was inserted with timestamps
			var found bson.M
			err = coll.FindOne(ctx, bson.M{"_id": result.InsertedID}).Decode(&found)
			if err != nil {
				t.Errorf("Failed to find inserted document: %v", err)
			}
			if found["test"] != "value" {
				t.Errorf("Wrong value in found document: got %v, want %v", found["test"], "value")
			}
			if found["_ts"] == nil {
				t.Error("_ts field missing from inserted document")
			}
			if found["_mt"] == nil {
				t.Error("_mt field missing from inserted document")
			}
		})

		// Test UpdateOne with noModifyMt
		t.Run("UpdateOne with noModifyMt", func(t *testing.T) {
			coll := getCleanCollection(t)

			ctx := context.Background()

			// Insert initial document
			doc := bson.M{
				"_id":   "test123",
				"field": "initial_value",
			}
			_, err := coll.InsertOne(ctx, doc)
			if err != nil {
				t.Fatalf("Failed to insert test document: %v", err)
			}

			// Get the document after insertion to capture initial _mt
			var initial bson.M
			err = coll.FindOne(ctx, bson.M{"_id": "test123"}).Decode(&initial)
			if err != nil {
				t.Fatalf("Failed to find initial document: %v", err)
			}
			initialMt := initial["_mt"]

			// Update with noModifyMt flag
			update := bson.M{
				"noModifyMt": true,
				"$set": bson.M{
					"field": "new_value",
				},
			}

			result, err := coll.UpdateOne(ctx, bson.M{"_id": "test123"}, update)
			if err != nil {
				t.Fatalf("UpdateOne failed: %v", err)
			}
			if result.ModifiedCount != 1 {
				t.Errorf("UpdateOne modified %d documents, want 1", result.ModifiedCount)
			}

			// Verify the update
			var updated bson.M
			err = coll.FindOne(ctx, bson.M{"_id": "test123"}).Decode(&updated)
			if err != nil {
				t.Fatalf("Failed to find updated document: %v", err)
			}

			if updated["field"] != "new_value" {
				t.Errorf("Field not updated, got %v, want %v", updated["field"], "new_value")
			}

			// Verify _mt wasn't modified due to noModifyMt flag
			if updated["_mt"] != initialMt {
				t.Error("_mt field was modified despite noModifyMt flag")
			}
		})

		// Test UpdateOne without noModifyMt
		t.Run("UpdateOne without noModifyMt", func(t *testing.T) {
			coll := getCleanCollection(t)

			ctx := context.Background()

			// Test Case 1: noModifyMt not set
			t.Run("noModifyMt not set", func(t *testing.T) {
				// Insert initial document
				doc := bson.M{
					"_id":   "test_default",
					"field": "initial_value",
				}
				_, err := coll.InsertOne(ctx, doc)
				if err != nil {
					t.Fatalf("Failed to insert test document: %v", err)
				}

				// Get the document after insertion to capture initial _mt
				var initial bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "test_default"}).Decode(&initial)
				if err != nil {
					t.Fatalf("Failed to find initial document: %v", err)
				}
				initialMt := initial["_mt"]

				// Small delay to ensure _mt would be different
				time.Sleep(10 * time.Millisecond)

				// Update without noModifyMt flag
				update := bson.M{
					"$set": bson.M{
						"field": "new_value_default",
					},
				}

				result, err := coll.UpdateOne(ctx, bson.M{"_id": "test_default"}, update)
				if err != nil {
					t.Fatalf("UpdateOne failed: %v", err)
				}
				if result.ModifiedCount != 1 {
					t.Errorf("UpdateOne modified %d documents, want 1", result.ModifiedCount)
				}

				// Verify the update
				var updated bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "test_default"}).Decode(&updated)
				if err != nil {
					t.Fatalf("Failed to find updated document: %v", err)
				}

				if updated["field"] != "new_value_default" {
					t.Errorf("Field not updated, got %v, want %v", updated["field"], "new_value_default")
				}

				// Verify _mt was modified when noModifyMt is not set
				if updated["_mt"] == initialMt {
					t.Error("_mt field was not modified when noModifyMt is not set")
				}
			})

			// Test Case 2: noModifyMt set to false
			t.Run("noModifyMt set to false", func(t *testing.T) {
				// Insert initial document
				doc := bson.M{
					"_id":   "test_false",
					"field": "initial_value",
				}
				_, err := coll.InsertOne(ctx, doc)
				if err != nil {
					t.Fatalf("Failed to insert test document: %v", err)
				}

				// Get the document after insertion to capture initial _mt
				var initial bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "test_false"}).Decode(&initial)
				if err != nil {
					t.Fatalf("Failed to find initial document: %v", err)
				}
				initialMt := initial["_mt"]

				// Small delay to ensure _mt would be different
				time.Sleep(10 * time.Millisecond)

				// Update with noModifyMt explicitly set to false
				update := bson.M{
					"noModifyMt": false,
					"$set": bson.M{
						"field": "new_value_false",
					},
				}

				result, err := coll.UpdateOne(ctx, bson.M{"_id": "test_false"}, update)
				if err != nil {
					t.Fatalf("UpdateOne failed: %v", err)
				}
				if result.ModifiedCount != 1 {
					t.Errorf("UpdateOne modified %d documents, want 1", result.ModifiedCount)
				}

				// Verify the update
				var updated bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "test_false"}).Decode(&updated)
				if err != nil {
					t.Fatalf("Failed to find updated document: %v", err)
				}

				if updated["field"] != "new_value_false" {
					t.Errorf("Field not updated, got %v, want %v", updated["field"], "new_value_false")
				}

				// Verify _mt was modified when noModifyMt is false
				if updated["_mt"] == initialMt {
					t.Error("_mt field was not modified when noModifyMt is set to false")
				}
			})
		})

		// Test Find with projection and sort
		t.Run("Find with projection and sort", func(t *testing.T) {
			coll := getCleanCollection(t)
			ctx := context.Background()
			cleanupCollection(t, coll)
			// Insert test documents
			docs := []interface{}{
				bson.M{"_id": "1", "field": "value2", "extra": "should_not_see"},
				bson.M{"_id": "2", "field": "value1", "extra": "should_not_see"},
			}
			for _, doc := range docs {
				_, err := coll.InsertOne(ctx, doc)
				if err != nil {
					t.Fatalf("Failed to insert test document: %v", err)
				}
			}
			options := QueryOptions{
				Projection: bson.D{{Key: "field", Value: 1}},
				Sort:       bson.D{{Key: "field", Value: 1}},
			}
			cursor, err := coll.Find(ctx, bson.M{}, options)
			if err != nil {
				t.Fatalf("Find failed: %v", err)
			}
			defer func() {
				if err := cursor.Close(ctx); err != nil {
					t.Errorf("Failed to close cursor: %v", err)
				}
			}()
			var results []bson.M
			if err = cursor.All(ctx, &results); err != nil {
				t.Fatalf("Failed to decode results: %v", err)
			}
			// 只检查插入的两条数据
			var filtered []bson.M
			for _, r := range results {
				if r["_id"] == "1" || r["_id"] == "2" {
					filtered = append(filtered, r)
				}
			}
			if len(filtered) != 2 {
				t.Errorf("Got %d filtered results, want 2", len(filtered))
			}
			if filtered[0]["field"] != "value1" || filtered[1]["field"] != "value2" {
				t.Error("Results not properly sorted")
			}
			for _, result := range filtered {
				if result["extra"] != nil {
					t.Error("Projection failed - extra field present")
				}
			}
		})

		// Test UpdateMany with noModifyMt
		t.Run("UpdateMany with noModifyMt", func(t *testing.T) {
			coll := getCleanCollection(t)
			ctx := context.Background()

			// Insert test documents
			docs := []interface{}{
				bson.M{"_id": "update_many_1", "category": "test", "field": "initial_value"},
				bson.M{"_id": "update_many_2", "category": "test", "field": "initial_value"},
				bson.M{"_id": "update_many_3", "category": "other", "field": "initial_value"},
			}

			_, err := coll.InsertMany(ctx, docs)
			if err != nil {
				t.Fatalf("Failed to insert test documents: %v", err)
			}

			// Get initial _mt values for comparison
			var initialDoc1 bson.M
			err = coll.FindOne(ctx, bson.M{"_id": "update_many_1"}).Decode(&initialDoc1)
			if err != nil {
				t.Fatalf("Failed to find initial document: %v", err)
			}
			initialMt1 := initialDoc1["_mt"]

			var initialDoc2 bson.M
			err = coll.FindOne(ctx, bson.M{"_id": "update_many_2"}).Decode(&initialDoc2)
			if err != nil {
				t.Fatalf("Failed to find initial document: %v", err)
			}
			initialMt2 := initialDoc2["_mt"]

			// Small delay to ensure _mt would be different
			time.Sleep(10 * time.Millisecond)

			// Test Case 1: noModifyMt = true
			t.Run("noModifyMt set to true", func(t *testing.T) {
				// Update with noModifyMt flag
				update := bson.M{
					"noModifyMt": true,
					"$set": bson.M{
						"field": "new_value_true",
					},
				}

				result, err := coll.UpdateMany(ctx, bson.M{"category": "test"}, update)
				if err != nil {
					t.Fatalf("UpdateMany failed: %v", err)
				}
				if result.ModifiedCount != 2 {
					t.Errorf("UpdateMany modified %d documents, want 2", result.ModifiedCount)
				}

				// Verify the updates
				var updated1 bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "update_many_1"}).Decode(&updated1)
				if err != nil {
					t.Fatalf("Failed to find updated document: %v", err)
				}

				if updated1["field"] != "new_value_true" {
					t.Errorf("Field not updated, got %v, want %v", updated1["field"], "new_value_true")
				}

				// Verify _mt wasn't modified due to noModifyMt flag
				if updated1["_mt"] != initialMt1 {
					t.Error("_mt field was modified despite noModifyMt flag")
				}

				var updated2 bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "update_many_2"}).Decode(&updated2)
				if err != nil {
					t.Fatalf("Failed to find updated document: %v", err)
				}

				if updated2["field"] != "new_value_true" {
					t.Errorf("Field not updated, got %v, want %v", updated2["field"], "new_value_true")
				}

				// Verify _mt wasn't modified due to noModifyMt flag
				if updated2["_mt"] != initialMt2 {
					t.Error("_mt field was modified despite noModifyMt flag")
				}
			})

			// Reset for next tests
			coll = getCleanCollection(t)

			// Re-insert the test documents
			_, err = coll.InsertMany(ctx, docs)
			if err != nil {
				t.Fatalf("Failed to insert test documents: %v", err)
			}

			// Get fresh initial _mt values
			err = coll.FindOne(ctx, bson.M{"_id": "update_many_1"}).Decode(&initialDoc1)
			if err != nil {
				t.Fatalf("Failed to find initial document: %v", err)
			}
			initialMt1 = initialDoc1["_mt"]

			err = coll.FindOne(ctx, bson.M{"_id": "update_many_2"}).Decode(&initialDoc2)
			if err != nil {
				t.Fatalf("Failed to find initial document: %v", err)
			}
			initialMt2 = initialDoc2["_mt"]

			// Small delay to ensure _mt would be different
			time.Sleep(10 * time.Millisecond)

			// Test Case 2: noModifyMt = false
			t.Run("noModifyMt set to false", func(t *testing.T) {
				// Update with noModifyMt explicitly set to false
				update := bson.M{
					"noModifyMt": false,
					"$set": bson.M{
						"field": "new_value_false",
					},
				}

				result, err := coll.UpdateMany(ctx, bson.M{"category": "test"}, update)
				if err != nil {
					t.Fatalf("UpdateMany failed: %v", err)
				}
				if result.ModifiedCount != 2 {
					t.Errorf("UpdateMany modified %d documents, want 2", result.ModifiedCount)
				}

				// Verify the updates
				var updated1 bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "update_many_1"}).Decode(&updated1)
				if err != nil {
					t.Fatalf("Failed to find updated document: %v", err)
				}

				if updated1["field"] != "new_value_false" {
					t.Errorf("Field not updated, got %v, want %v", updated1["field"], "new_value_false")
				}

				// Verify _mt was modified when noModifyMt is false
				if updated1["_mt"] == initialMt1 {
					t.Error("_mt field was not modified when noModifyMt is set to false")
				}

				var updated2 bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "update_many_2"}).Decode(&updated2)
				if err != nil {
					t.Fatalf("Failed to find updated document: %v", err)
				}

				if updated2["field"] != "new_value_false" {
					t.Errorf("Field not updated, got %v, want %v", updated2["field"], "new_value_false")
				}

				// Verify _mt was modified when noModifyMt is false
				if updated2["_mt"] == initialMt2 {
					t.Error("_mt field was not modified when noModifyMt is set to false")
				}
			})

			// Reset for next tests
			coll = getCleanCollection(t)

			// Re-insert the test documents
			_, err = coll.InsertMany(ctx, docs)
			if err != nil {
				t.Fatalf("Failed to insert test documents: %v", err)
			}

			// Get fresh initial _mt values
			err = coll.FindOne(ctx, bson.M{"_id": "update_many_1"}).Decode(&initialDoc1)
			if err != nil {
				t.Fatalf("Failed to find initial document: %v", err)
			}
			initialMt1 = initialDoc1["_mt"]

			err = coll.FindOne(ctx, bson.M{"_id": "update_many_2"}).Decode(&initialDoc2)
			if err != nil {
				t.Fatalf("Failed to find initial document: %v", err)
			}
			initialMt2 = initialDoc2["_mt"]

			// Small delay to ensure _mt would be different
			time.Sleep(10 * time.Millisecond)

			// Test Case 3: noModifyMt not set
			t.Run("noModifyMt not set", func(t *testing.T) {
				// Update without noModifyMt flag
				update := bson.M{
					"$set": bson.M{
						"field": "new_value_default",
					},
				}

				result, err := coll.UpdateMany(ctx, bson.M{"category": "test"}, update)
				if err != nil {
					t.Fatalf("UpdateMany failed: %v", err)
				}
				if result.ModifiedCount != 2 {
					t.Errorf("UpdateMany modified %d documents, want 2", result.ModifiedCount)
				}

				// Verify the updates
				var updated1 bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "update_many_1"}).Decode(&updated1)
				if err != nil {
					t.Fatalf("Failed to find updated document: %v", err)
				}

				if updated1["field"] != "new_value_default" {
					t.Errorf("Field not updated, got %v, want %v", updated1["field"], "new_value_default")
				}

				// Verify _mt was modified when noModifyMt is not set
				if updated1["_mt"] == initialMt1 {
					t.Error("_mt field was not modified when noModifyMt is not set")
				}

				var updated2 bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "update_many_2"}).Decode(&updated2)
				if err != nil {
					t.Fatalf("Failed to find updated document: %v", err)
				}

				if updated2["field"] != "new_value_default" {
					t.Errorf("Field not updated, got %v, want %v", updated2["field"], "new_value_default")
				}

				// Verify _mt was modified when noModifyMt is not set
				if updated2["_mt"] == initialMt2 {
					t.Error("_mt field was not modified when noModifyMt is not set")
				}
			})
		})

		// Test FindOneAndUpdate with noModifyMt
		t.Run("FindOneAndUpdate with noModifyMt", func(t *testing.T) {
			ctx := context.Background()

			// Test Case 1: noModifyMt = true
			t.Run("noModifyMt set to true", func(t *testing.T) {
				coll := getCleanCollection(t)

				// Insert initial document
				doc := bson.M{
					"_id":   "findupdate_true",
					"field": "initial_value",
				}
				_, err := coll.InsertOne(ctx, doc)
				if err != nil {
					t.Fatalf("Failed to insert test document: %v", err)
				}

				// Get the document after insertion to capture initial _mt
				var initial bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "findupdate_true"}).Decode(&initial)
				if err != nil {
					t.Fatalf("Failed to find initial document: %v", err)
				}
				initialMt := initial["_mt"]

				// Small delay to ensure _mt would be different
				time.Sleep(10 * time.Millisecond)

				// Update with noModifyMt flag
				update := bson.M{
					"noModifyMt": true,
					"$set": bson.M{
						"field": "new_value_true",
					},
				}

				// Using ReturnDocument: After to get the updated document
				opts := options.FindOneAndUpdate().SetReturnDocument(options.After)
				result := coll.FindOneAndUpdate(ctx, bson.M{"_id": "findupdate_true"}, update, opts)

				var updated bson.M
				err = result.Decode(&updated)
				if err != nil {
					t.Fatalf("FindOneAndUpdate failed: %v", err)
				}

				if updated["field"] != "new_value_true" {
					t.Errorf("Field not updated, got %v, want %v", updated["field"], "new_value_true")
				}

				// Verify _mt wasn't modified due to noModifyMt flag
				if updated["_mt"] != initialMt {
					t.Error("_mt field was modified despite noModifyMt flag")
				}
			})

			// Test Case 2: noModifyMt = false
			t.Run("noModifyMt set to false", func(t *testing.T) {
				coll := getCleanCollection(t)

				// Insert initial document
				doc := bson.M{
					"_id":   "findupdate_false",
					"field": "initial_value",
				}
				_, err := coll.InsertOne(ctx, doc)
				if err != nil {
					t.Fatalf("Failed to insert test document: %v", err)
				}

				// Get the document after insertion to capture initial _mt
				var initial bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "findupdate_false"}).Decode(&initial)
				if err != nil {
					t.Fatalf("Failed to find initial document: %v", err)
				}
				initialMt := initial["_mt"]

				// Small delay to ensure _mt would be different
				time.Sleep(10 * time.Millisecond)

				// Update with noModifyMt explicitly set to false
				update := bson.M{
					"noModifyMt": false,
					"$set": bson.M{
						"field": "new_value_false",
					},
				}

				// Using ReturnDocument: After to get the updated document
				opts := options.FindOneAndUpdate().SetReturnDocument(options.After)
				result := coll.FindOneAndUpdate(ctx, bson.M{"_id": "findupdate_false"}, update, opts)

				var updated bson.M
				err = result.Decode(&updated)
				if err != nil {
					t.Fatalf("FindOneAndUpdate failed: %v", err)
				}

				if updated["field"] != "new_value_false" {
					t.Errorf("Field not updated, got %v, want %v", updated["field"], "new_value_false")
				}

				// Verify _mt was modified when noModifyMt is false
				if updated["_mt"] == initialMt {
					t.Error("_mt field was not modified when noModifyMt is set to false")
				}
			})

			// Test Case 3: noModifyMt not set
			t.Run("noModifyMt not set", func(t *testing.T) {
				coll := getCleanCollection(t)

				// Insert initial document
				doc := bson.M{
					"_id":   "findupdate_default",
					"field": "initial_value",
				}
				_, err := coll.InsertOne(ctx, doc)
				if err != nil {
					t.Fatalf("Failed to insert test document: %v", err)
				}

				// Get the document after insertion to capture initial _mt
				var initial bson.M
				err = coll.FindOne(ctx, bson.M{"_id": "findupdate_default"}).Decode(&initial)
				if err != nil {
					t.Fatalf("Failed to find initial document: %v", err)
				}
				initialMt := initial["_mt"]

				// Small delay to ensure _mt would be different
				time.Sleep(10 * time.Millisecond)

				// Update without noModifyMt flag
				update := bson.M{
					"$set": bson.M{
						"field": "new_value_default",
					},
				}

				// Using ReturnDocument: After to get the updated document
				opts := options.FindOneAndUpdate().SetReturnDocument(options.After)
				result := coll.FindOneAndUpdate(ctx, bson.M{"_id": "findupdate_default"}, update, opts)

				var updated bson.M
				err = result.Decode(&updated)
				if err != nil {
					t.Fatalf("FindOneAndUpdate failed: %v", err)
				}

				if updated["field"] != "new_value_default" {
					t.Errorf("Field not updated, got %v, want %v", updated["field"], "new_value_default")
				}

				// Verify _mt was modified when noModifyMt is not set
				if updated["_mt"] == initialMt {
					t.Error("_mt field was not modified when noModifyMt is not set")
				}
			})
		})

		// Test FindToArray
		t.Run("FindToArray", func(t *testing.T) {
			coll := getCleanCollection(t)

			ctx := context.Background()

			// Insert test documents
			docs := []interface{}{
				bson.M{"_id": "1", "field": "value2", "status": "active", "extra": "should_not_see"},
				bson.M{"_id": "2", "field": "value1", "status": "active", "extra": "should_not_see"},
				bson.M{"_id": "3", "field": "value3", "status": "inactive", "extra": "should_not_see"},
			}

			for _, doc := range docs {
				_, err := coll.InsertOne(ctx, doc)
				if err != nil {
					t.Fatalf("Failed to insert test document: %v", err)
				}
			}

			// Test FindToArray with QueryOptions
			options := QueryOptions{
				Projection: bson.D{
					{Key: "field", Value: 1},
					{Key: "_ts", Value: 1},
					{Key: "_mt", Value: 1},
				},
				Sort: bson.D{{Key: "field", Value: 1}},
			}

			// Query for only active status documents
			query := bson.M{"status": "active"}
			results, err := coll.FindToArray(ctx, query, options)
			if err != nil {
				t.Fatalf("FindToArray failed: %v", err)
			}

			// Verify results
			if len(results) != 2 {
				t.Errorf("Got %d results, want 2", len(results))
			}

			// Verify sorting and projection
			if results[0]["field"] != "value1" || results[1]["field"] != "value2" {
				t.Error("Results not properly sorted")
			}

			// Verify projection worked
			for _, result := range results {
				if result["extra"] != nil {
					t.Error("Projection failed - extra field present")
				}
				if result["status"] != nil {
					t.Error("Projection failed - status field present")
				}
				// Verify _ts and _mt fields exist
				if result["_ts"] == nil {
					t.Error("_ts field missing from document")
				}
				if result["_mt"] == nil {
					t.Error("_mt field missing from document")
				}
			}
		})

		// Test Find with timeout
		t.Run("FindWithTimeout", func(t *testing.T) {
			coll := getCleanCollection(t)

			ctx := context.Background()

			// Insert test documents with larger data to make query slower
			for i := 0; i < 1000; i++ {
				doc := bson.M{
					"_id":  fmt.Sprintf("timeout_test_%d", i),
					"data": strings.Repeat("test_data_", 10000), // Much larger data to slow down query
					"num":  i,
				}
				_, err := coll.InsertOne(ctx, doc)
				if err != nil {
					t.Fatalf("Failed to insert test document: %v", err)
				}
			}

			// Create a context with short timeout
			ctxTimeout, cancel := context.WithTimeout(ctx, 1*time.Millisecond)
			defer cancel()

			// More complex query with sort and projection to make it slower
			opts := QueryOptions{
				Sort: bson.D{
					{Key: "data", Value: 1},
					{Key: "num", Value: -1},
				},
				Projection: bson.D{
					{Key: "data", Value: 1},
					{Key: "num", Value: 1},
				},
			}

			_, err := coll.Find(ctxTimeout, bson.M{}, opts)

			// Verify timeout error
			if err == nil {
				t.Error("Expected timeout error, got nil")
			} else if !strings.Contains(err.Error(), "deadline exceeded") && !strings.Contains(err.Error(), "context canceled") && !strings.Contains(err.Error(), "operation was interrupted") {
				t.Errorf("Expected deadline exceeded or context canceled error, got: %v", err)
			}
		})

		// Test InsertMany
		t.Run("InsertMany", func(t *testing.T) {
			coll := getCleanCollection(t)

			ctx := context.Background()
			docs := []interface{}{
				bson.M{"test": "value1"},
				bson.M{"test": "value2"},
			}

			result, err := coll.InsertMany(ctx, docs)
			if err != nil {
				t.Fatalf("InsertMany failed: %v", err)
			}
			if len(result.InsertedIDs) != len(docs) {
				t.Fatalf("InsertMany inserted %d documents, want %d", len(result.InsertedIDs), len(docs))
			}

			// Verify the documents were inserted with timestamps
			for _, id := range result.InsertedIDs {
				var found bson.M
				err = coll.FindOne(ctx, bson.M{"_id": id}).Decode(&found)
				if err != nil {
					t.Errorf("Failed to find inserted document: %v", err)
				}
				if found["test"] == nil {
					t.Errorf("Wrong value in found document: got %v, want %v", found["test"], "value1 or value2")
				}
				if found["_ts"] == nil {
					t.Error("_ts field missing from inserted document")
				}
				if found["_mt"] == nil {
					t.Error("_mt field missing from inserted document")
				}
			}
		})

		// Test Drop
		t.Run("Drop", func(t *testing.T) {
			coll := getCleanCollection(t)

			ctx := context.Background()

			// Insert a test document to ensure the collection exists
			doc := bson.M{"test": "value"}
			_, err := coll.InsertOne(ctx, doc)
			if err != nil {
				t.Fatalf("InsertOne failed: %v", err)
			}

			// Drop the collection
			err = coll.Drop(ctx)
			if err != nil {
				t.Fatalf("Drop failed: %v", err)
			}

			// Attempt to find a document in the dropped collection, should return an error
			err = coll.FindOne(ctx, bson.M{"test": "value"}).Decode(bson.M{})
			if err == nil || !strings.Contains(err.Error(), "mongo: no documents in result") {
				t.Errorf("Expected 'mongo: no documents in result' error after drop, got: %v", err)
			}
		})

		// Test BulkWrite
		t.Run("BulkWrite", func(t *testing.T) {
			coll := getCleanCollection(t)

			ctx := context.Background()

			// Define write models
			models := []mongo.WriteModel{
				mongo.NewInsertOneModel().SetDocument(bson.M{"_id": "bulk_insert_1", "field": "value1"}),
				mongo.NewUpdateOneModel().SetFilter(bson.M{"_id": "bulk_insert_1"}).SetUpdate(bson.M{"$set": bson.M{"field": "updated_value1"}}),
				mongo.NewDeleteOneModel().SetFilter(bson.M{"_id": "bulk_insert_1"}),
				mongo.NewInsertOneModel().SetDocument(bson.M{"_id": "bulk_insert_2", "field": "value2"}),
			}

			// Perform BulkWrite
			result, err := coll.BulkWrite(ctx, models)
			if err != nil {
				t.Fatalf("BulkWrite failed: %v", err)
			}

			// Verify results
			if result.InsertedCount != 2 {
				t.Errorf("BulkWrite inserted %d documents, want 2", result.InsertedCount)
			}
			if result.ModifiedCount != 1 {
				t.Errorf("BulkWrite modified %d documents, want 1", result.ModifiedCount)
			}
			if result.DeletedCount != 1 {
				t.Errorf("BulkWrite deleted %d documents, want 1", result.DeletedCount)
			}

			// Verify the document was inserted and updated
			var found bson.M
			err = coll.FindOne(ctx, bson.M{"_id": "bulk_insert_2"}).Decode(&found)
			if err != nil {
				t.Fatalf("Failed to find inserted document: %v", err)
			}
			if found["field"] != "value2" {
				t.Errorf("Wrong value in found document: got %v, want %v", found["field"], "value2")
			}
		})

		// Test Rename
		t.Run("Rename", func(t *testing.T) {
			coll := getCleanCollection(t)
			ctx := context.Background()
			cleanupCollection(t, coll)
			_ = Coll("tmp", "goapp_test_renamed").Drop(ctx)
			// Insert a document before renaming
			testDoc := bson.M{"test_field": "test_value"}
			_, err := coll.InsertOne(ctx, testDoc)
			if err != nil {
				t.Fatalf("InsertOne failed: %v", err)
			}

			// Rename the collection
			err = coll.Rename(ctx, "goapp_test_renamed")
			if err != nil {
				t.Fatalf("Rename failed: %v", err)
			}

			// Verify the new collection exists and has the document
			newColl := Coll("tmp", "goapp_test_renamed")
			var newResult bson.M
			err = newColl.FindOne(ctx, bson.M{}).Decode(&newResult)
			if err != nil {
				t.Fatalf("Failed to find document in renamed collection: %v", err)
			}

			if newResult["test_field"] != "test_value" {
				t.Errorf("Wrong value in found document: got %v, want %v", newResult["test_field"], "test_value")
			}

			// Clean up
			_ = newColl.Drop(ctx)
		})

		// Test FindToArray with filtering
		t.Run("FindToArray with filtering", func(t *testing.T) {
			coll := getCleanCollection(t)

			ctx := context.Background()

			// Insert test documents
			docs := []interface{}{
				bson.M{"_id": "1", "field": "value1", "category": "A"},
				bson.M{"_id": "2", "field": "value2", "category": "A"},
				bson.M{"_id": "3", "field": "value3", "category": "B"},
			}

			for _, doc := range docs {
				_, err := coll.InsertOne(ctx, doc)
				if err != nil {
					t.Fatalf("Failed to insert test document: %v", err)
				}
			}

			// Test FindToArray with filter and QueryOptions
			options := QueryOptions{
				Projection: bson.D{
					{Key: "field", Value: 1},
					{Key: "category", Value: 1},
				},
				Sort: bson.D{{Key: "field", Value: 1}},
			}

			// Filter to get only category A documents
			filter := bson.M{"category": "A"}
			results, err := coll.FindToArray(ctx, filter, options)
			if err != nil {
				t.Fatalf("FindToArray failed: %v", err)
			}

			// Verify results count
			if len(results) != 2 {
				t.Errorf("Got %d results, want 2", len(results))
			}

			// Verify filtering and sorting
			if results[0]["field"] != "value1" || results[1]["field"] != "value2" {
				t.Error("Results not properly sorted")
			}

			// Verify all results are from category A
			for _, result := range results {
				if result["category"] != "A" {
					t.Errorf("Got document with category %v, want 'A'", result["category"])
				}
			}
		})

		// Test Name
		t.Run("Name", func(t *testing.T) {
			coll := getCleanCollection(t)

			// Test the collection name
			expectedName := "goapp_test"
			actualName := coll.Name()
			if actualName != expectedName {
				t.Errorf("Collection name mismatch, got %s, want %s", actualName, expectedName)
			}
		})

		// Test DBName
		t.Run("DBName", func(t *testing.T) {
			coll := getCleanCollection(t)

			// Test the database name
			expectedDbName := dbConfigs["tmp"].database
			actualDbName := coll.DBName()
			if actualDbName != expectedDbName {
				t.Errorf("Database name mismatch, got %s, want %s", actualDbName, expectedDbName)
			}
		})
	})
}

func TestCreateCollection(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("Create new collection", func(t *testing.T) {
		db := Database("tmp")
		if db == nil {
			t.Fatal("Failed to get database")
		}

		// Test creating a new collection
		collectionName := "test_create_collection"
		err := db.CreateCollection(context.Background(), collectionName, nil)
		if err != nil {
			t.Fatalf("Failed to create collection: %v", err)
		}

		// Verify collection exists
		collections, err := db.ListCollectionNames(context.Background(), bson.M{})
		if err != nil {
			t.Fatalf("Failed to list collections: %v", err)
		}
		if !contains(collections, collectionName) {
			t.Errorf("Collection %s not found in database", collectionName)
		}

		// Test creating existing collection (should not error)
		err = db.CreateCollection(context.Background(), collectionName, nil)
		if err != nil {
			t.Errorf("Creating existing collection should not error: %v", err)
		}

		// Cleanup
		coll := Coll("tmp", collectionName)
		if err := coll.Drop(context.Background()); err != nil {
			t.Errorf("Failed to drop collection: %v", err)
		}
	})
}

func TestPreDefColl(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("Get predefined collection", func(t *testing.T) {
		// Test with valid collection name
		coll := PreDefColl("mailLog")
		if coll == nil {
			t.Fatal("Failed to get predefined collection")
		}

		// Test with invalid collection name
		invalidColl := PreDefColl("non_existent_coll")
		if invalidColl != nil {
			t.Error("Expected nil for non-existent collection")
		}

		// Test collection operations
		doc := bson.M{"test": "value", "timestamp": time.Now()}
		_, err := coll.InsertOne(context.Background(), doc)
		if err != nil {
			t.Fatalf("Failed to insert document: %v", err)
		}

		// Verify document was inserted
		var result bson.M
		err = coll.FindOne(context.Background(), bson.M{"test": "value"}).Decode(&result)
		if err != nil {
			t.Fatalf("Failed to find document: %v", err)
		}
		if result["test"] != "value" {
			t.Errorf("Wrong value in found document: got %v, want %v", result["test"], "value")
		}

		// Cleanup
		if _, err := coll.DeleteMany(context.Background(), bson.M{}); err != nil {
			t.Errorf("Failed to cleanup collection: %v", err)
		}

		if err := coll.Drop(context.Background()); err != nil {
			t.Errorf("Failed to drop collection: %v", err)
		}
	})
}

// Transaction Tests
func TestTransactionSessionManagement(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("StartSession", func(t *testing.T) {
		// Test successful session creation
		session, err := StartSession("tmp")
		if err != nil {
			t.Fatalf("Failed to start session: %v", err)
		}
		if session == nil {
			t.Fatal("Session should not be nil")
		}
		defer session.EndSession()

		// Test session with invalid database
		invalidSession, err := StartSession("non_existent_db")
		if err == nil {
			t.Error("Expected error for non-existent database")
		}
		if invalidSession != nil {
			t.Error("Session should be nil for invalid database")
		}
	})

	t.Run("StartSessionWithContext", func(t *testing.T) {
		ctx := context.Background()

		// Test successful session creation with context
		session, err := StartSessionWithContext(ctx, "tmp")
		if err != nil {
			t.Fatalf("Failed to start session with context: %v", err)
		}
		if session == nil {
			t.Fatal("Session should not be nil")
		}
		defer session.EndSession()

		// Test session with invalid database
		invalidSession, err := StartSessionWithContext(ctx, "non_existent_db")
		if err == nil {
			t.Error("Expected error for non-existent database")
		}
		if invalidSession != nil {
			t.Error("Session should be nil for invalid database")
		}
	})

	t.Run("EndSession", func(t *testing.T) {
		session, err := StartSession("tmp")
		if err != nil {
			t.Fatalf("Failed to start session: %v", err)
		}

		// Test EndSession with valid session
		session.EndSession()

		// Test EndSession with nil session (should not panic)
		var nilSession *MongoSession
		nilSession.EndSession()
	})
}

func TestTransactionBasicOperations(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("StartTransaction", func(t *testing.T) {
		session, err := StartSession("tmp")
		if err != nil {
			t.Fatalf("Failed to start session: %v", err)
		}
		defer session.EndSession()

		// Test successful transaction start
		txn, err := session.StartTransaction()
		if err != nil {
			t.Fatalf("Failed to start transaction: %v", err)
		}
		if txn == nil {
			t.Fatal("Transaction should not be nil")
		}
		if !txn.started {
			t.Error("Transaction should be marked as started")
		}
		if txn.committed {
			t.Error("Transaction should not be committed yet")
		}
		if txn.aborted {
			t.Error("Transaction should not be aborted yet")
		}

		// End the first transaction before starting another
		err = txn.CommitTransaction()
		if err != nil {
			t.Fatalf("Failed to commit first transaction: %v", err)
		}

		// Test transaction start with options
		txnOpts := &TransactionOptions{
			ReadConcern:  readconcern.Snapshot(),
			WriteConcern: writeconcern.Majority(),
		}
		txnWithOpts, err := session.StartTransaction(txnOpts)
		if err != nil {
			t.Fatalf("Failed to start transaction with options: %v", err)
		}
		if txnWithOpts == nil {
			t.Fatal("Transaction with options should not be nil")
		}
		defer txnWithOpts.AbortTransaction()

		// Test transaction start with nil session
		var nilSession *MongoSession
		nilTxn, err := nilSession.StartTransaction()
		if err == nil {
			t.Error("Expected error for nil session")
		}
		if nilTxn != nil {
			t.Error("Transaction should be nil for nil session")
		}
	})

	t.Run("CommitTransaction", func(t *testing.T) {
		session, err := StartSession("tmp")
		if err != nil {
			t.Fatalf("Failed to start session: %v", err)
		}
		defer session.EndSession()

		// Test successful commit
		txn, err := session.StartTransaction()
		if err != nil {
			t.Fatalf("Failed to start transaction: %v", err)
		}

		err = txn.CommitTransaction()
		if err != nil {
			t.Fatalf("Failed to commit transaction: %v", err)
		}
		if !txn.committed {
			t.Error("Transaction should be marked as committed")
		}

		// Test commit on already committed transaction
		err = txn.CommitTransaction()
		if err == nil {
			t.Error("Expected error for already committed transaction")
		}

		// Test commit on not started transaction
		txn2 := &MongoTransaction{
			session:   session.session,
			ctx:       session.ctx,
			started:   false,
			committed: false,
			aborted:   false,
		}
		err = txn2.CommitTransaction()
		if err == nil {
			t.Error("Expected error for not started transaction")
		}

		// Test commit on aborted transaction
		txn3, err := session.StartTransaction()
		if err != nil {
			t.Fatalf("Failed to start transaction: %v", err)
		}
		err = txn3.AbortTransaction()
		if err != nil {
			t.Fatalf("Failed to abort transaction: %v", err)
		}
		err = txn3.CommitTransaction()
		if err == nil {
			t.Error("Expected error for aborted transaction")
		}
	})

	t.Run("AbortTransaction", func(t *testing.T) {
		session, err := StartSession("tmp")
		if err != nil {
			t.Fatalf("Failed to start session: %v", err)
		}
		defer session.EndSession()

		// Test successful abort
		txn, err := session.StartTransaction()
		if err != nil {
			t.Fatalf("Failed to start transaction: %v", err)
		}

		err = txn.AbortTransaction()
		if err != nil {
			t.Fatalf("Failed to abort transaction: %v", err)
		}
		if !txn.aborted {
			t.Error("Transaction should be marked as aborted")
		}

		// Test abort on already aborted transaction
		err = txn.AbortTransaction()
		if err == nil {
			t.Error("Expected error for already aborted transaction")
		}

		// Test abort on not started transaction
		txn2 := &MongoTransaction{
			session:   session.session,
			ctx:       session.ctx,
			started:   false,
			committed: false,
			aborted:   false,
		}
		err = txn2.AbortTransaction()
		if err == nil {
			t.Error("Expected error for not started transaction")
		}

		// Test abort on committed transaction
		txn3, err := session.StartTransaction()
		if err != nil {
			t.Fatalf("Failed to start transaction: %v", err)
		}
		err = txn3.CommitTransaction()
		if err != nil {
			t.Fatalf("Failed to commit transaction: %v", err)
		}
		err = txn3.AbortTransaction()
		if err == nil {
			t.Error("Expected error for committed transaction")
		}
	})
}

func TestWithTransaction(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("WithTransaction Success", func(t *testing.T) {
		session, err := StartSession("tmp")
		if err != nil {
			t.Fatalf("Failed to start session: %v", err)
		}
		defer session.EndSession()

		// Test successful transaction
		err = session.WithTransaction(func(sessCtx mongo.SessionContext) error {
			coll := Coll("tmp", "test_transaction")
			_, err := coll.InsertOne(sessCtx, bson.M{"test": "value"})
			return err
		})
		if err != nil {
			t.Fatalf("WithTransaction failed: %v", err)
		}

		// Verify the document was inserted
		coll := Coll("tmp", "test_transaction")
		var result bson.M
		err = coll.FindOne(context.Background(), bson.M{"test": "value"}).Decode(&result)
		if err != nil {
			t.Fatalf("Failed to find inserted document: %v", err)
		}
		if result["test"] != "value" {
			t.Errorf("Wrong value in found document: got %v, want %v", result["test"], "value")
		}

		// Cleanup
		coll.Drop(context.Background())
	})

	t.Run("WithTransaction Failure", func(t *testing.T) {
		session, err := StartSession("tmp")
		if err != nil {
			t.Fatalf("Failed to start session: %v", err)
		}
		defer session.EndSession()

		// Test transaction that fails
		err = session.WithTransaction(func(sessCtx mongo.SessionContext) error {
			coll := Coll("tmp", "test_transaction_fail")
			_, err := coll.InsertOne(sessCtx, bson.M{"test": "value"})
			if err != nil {
				return err
			}
			// Force an error
			return fmt.Errorf("intentional error")
		})
		if err == nil {
			t.Error("Expected error from WithTransaction")
		}

		// Verify the document was not inserted (transaction rolled back)
		coll := Coll("tmp", "test_transaction_fail")
		var result bson.M
		err = coll.FindOne(context.Background(), bson.M{"test": "value"}).Decode(&result)
		if err == nil {
			t.Error("Document should not exist after transaction failure")
		}

		// Cleanup
		coll.Drop(context.Background())
	})

	t.Run("WithTransaction with Options", func(t *testing.T) {
		session, err := StartSession("tmp")
		if err != nil {
			t.Fatalf("Failed to start session: %v", err)
		}
		defer session.EndSession()

		// Test transaction with options
		txnOpts := &TransactionOptions{
			ReadConcern:  readconcern.Snapshot(),
			WriteConcern: writeconcern.Majority(),
		}

		err = session.WithTransaction(func(sessCtx mongo.SessionContext) error {
			coll := Coll("tmp", "test_transaction_opts")
			_, err := coll.InsertOne(sessCtx, bson.M{"test": "value_with_opts"})
			return err
		}, txnOpts)
		if err != nil {
			t.Fatalf("WithTransaction with options failed: %v", err)
		}

		// Verify the document was inserted
		coll := Coll("tmp", "test_transaction_opts")
		var result bson.M
		err = coll.FindOne(context.Background(), bson.M{"test": "value_with_opts"}).Decode(&result)
		if err != nil {
			t.Fatalf("Failed to find inserted document: %v", err)
		}
		if result["test"] != "value_with_opts" {
			t.Errorf("Wrong value in found document: got %v, want %v", result["test"], "value_with_opts")
		}

		// Cleanup
		coll.Drop(context.Background())
	})

	t.Run("WithTransaction with Nil Session", func(t *testing.T) {
		var session *MongoSession
		err := session.WithTransaction(func(sessCtx mongo.SessionContext) error {
			return nil
		})
		if err == nil {
			t.Error("Expected error for nil session")
		}
	})
}

func TestGetSessionContext(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("GetSessionContext", func(t *testing.T) {
		session, err := StartSession("tmp")
		if err != nil {
			t.Fatalf("Failed to start session: %v", err)
		}
		defer session.EndSession()

		// Test valid session
		sessCtx := session.GetSessionContext()
		if sessCtx == nil {
			t.Error("Session context should not be nil")
		}

		// Test nil session
		var nilSession *MongoSession
		nilCtx := nilSession.GetSessionContext()
		if nilCtx != nil {
			t.Error("Session context should be nil for nil session")
		}
	})

	t.Run("GetTransactionContext", func(t *testing.T) {
		session, err := StartSession("tmp")
		if err != nil {
			t.Fatalf("Failed to start session: %v", err)
		}
		defer session.EndSession()

		txn, err := session.StartTransaction()
		if err != nil {
			t.Fatalf("Failed to start transaction: %v", err)
		}

		// Test valid transaction
		txnCtx := txn.GetSessionContext()
		if txnCtx == nil {
			t.Error("Transaction context should not be nil")
		}

		// Test nil transaction
		var nilTxn *MongoTransaction
		nilCtx := nilTxn.GetSessionContext()
		if nilCtx != nil {
			t.Error("Transaction context should be nil for nil transaction")
		}
	})
}

func TestTransactionComplexOperations(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("Multi-Collection Transaction", func(t *testing.T) {
		session, err := StartSession("tmp")
		if err != nil {
			t.Fatalf("Failed to start session: %v", err)
		}
		defer session.EndSession()

		err = session.WithTransaction(func(sessCtx mongo.SessionContext) error {
			// Insert into first collection
			usersColl := Coll("tmp", "users")
			userResult, err := usersColl.InsertOne(sessCtx, bson.M{
				"_id":   "user123",
				"name":  "John Doe",
				"email": "<EMAIL>",
			})
			if err != nil {
				return err
			}

			// Insert into second collection
			accountsColl := Coll("tmp", "accounts")
			_, err = accountsColl.InsertOne(sessCtx, bson.M{
				"userId":  userResult.InsertedID,
				"balance": 1000,
				"type":    "savings",
			})
			return err
		})

		if err != nil {
			t.Fatalf("Multi-collection transaction failed: %v", err)
		}

		// Verify both documents were inserted
		usersColl := Coll("tmp", "users")
		var user bson.M
		err = usersColl.FindOne(context.Background(), bson.M{"_id": "user123"}).Decode(&user)
		if err != nil {
			t.Fatalf("Failed to find user: %v", err)
		}
		if user["name"] != "John Doe" {
			t.Errorf("Wrong user name: got %v, want %v", user["name"], "John Doe")
		}

		accountsColl := Coll("tmp", "accounts")
		var account bson.M
		err = accountsColl.FindOne(context.Background(), bson.M{"userId": "user123"}).Decode(&account)
		if err != nil {
			t.Fatalf("Failed to find account: %v", err)
		}
		if account["balance"] != int32(1000) {
			t.Errorf("Wrong account balance: got %v, want %v", account["balance"], 1000)
		}

		// Cleanup
		usersColl.Drop(context.Background())
		accountsColl.Drop(context.Background())
	})

	t.Run("Transaction with Read Operations", func(t *testing.T) {
		session, err := StartSession("tmp")
		if err != nil {
			t.Fatalf("Failed to start session: %v", err)
		}
		defer session.EndSession()

		// First, insert some data outside transaction
		coll := Coll("tmp", "test_read")
		_, err = coll.InsertOne(context.Background(), bson.M{"_id": "doc1", "value": "test1"})
		if err != nil {
			t.Fatalf("Failed to insert test document: %v", err)
		}

		// Then read and update in transaction
		err = session.WithTransaction(func(sessCtx mongo.SessionContext) error {
			// Read operation
			var doc bson.M
			err := coll.FindOne(sessCtx, bson.M{"_id": "doc1"}).Decode(&doc)
			if err != nil {
				return err
			}
			if doc["value"] != "test1" {
				return fmt.Errorf("wrong value: got %v, want %v", doc["value"], "test1")
			}

			// Update operation
			_, err = coll.UpdateOne(sessCtx, bson.M{"_id": "doc1"}, bson.M{
				"$set": bson.M{"value": "updated"},
			})
			return err
		})

		if err != nil {
			t.Fatalf("Transaction with read operations failed: %v", err)
		}

		// Verify the update
		var updated bson.M
		err = coll.FindOne(context.Background(), bson.M{"_id": "doc1"}).Decode(&updated)
		if err != nil {
			t.Fatalf("Failed to find updated document: %v", err)
		}
		if updated["value"] != "updated" {
			t.Errorf("Wrong updated value: got %v, want %v", updated["value"], "updated")
		}

		// Cleanup
		coll.Drop(context.Background())
	})

	t.Run("Transaction Rollback on Error", func(t *testing.T) {
		session, err := StartSession("tmp")
		if err != nil {
			t.Fatalf("Failed to start session: %v", err)
		}
		defer session.EndSession()

		coll := Coll("tmp", "test_rollback")

		// Insert initial data
		_, err = coll.InsertOne(context.Background(), bson.M{"_id": "initial", "value": "original"})
		if err != nil {
			t.Fatalf("Failed to insert initial document: %v", err)
		}

		// Start transaction that will fail
		err = session.WithTransaction(func(sessCtx mongo.SessionContext) error {
			// Update the document
			_, err := coll.UpdateOne(sessCtx, bson.M{"_id": "initial"}, bson.M{
				"$set": bson.M{"value": "updated_in_txn"},
			})
			if err != nil {
				return err
			}

			// Insert new document
			_, err = coll.InsertOne(sessCtx, bson.M{"_id": "new", "value": "new_in_txn"})
			if err != nil {
				return err
			}

			// Force an error to trigger rollback
			return fmt.Errorf("intentional error for rollback test")
		})

		if err == nil {
			t.Error("Expected error from transaction")
		}

		// Verify rollback - original document should be unchanged
		var original bson.M
		err = coll.FindOne(context.Background(), bson.M{"_id": "initial"}).Decode(&original)
		if err != nil {
			t.Fatalf("Failed to find original document: %v", err)
		}
		if original["value"] != "original" {
			t.Errorf("Document should be unchanged after rollback: got %v, want %v", original["value"], "original")
		}

		// Verify new document was not inserted
		var newDoc bson.M
		err = coll.FindOne(context.Background(), bson.M{"_id": "new"}).Decode(&newDoc)
		if err == nil {
			t.Error("New document should not exist after rollback")
		}

		// Cleanup
		coll.Drop(context.Background())
	})
}

func TestTransactionErrorHandling(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("Database Configuration Errors", func(t *testing.T) {
		// Test with non-existent database
		session, err := StartSession("non_existent_db")
		if err == nil {
			t.Error("Expected error for non-existent database")
		}
		if session != nil {
			t.Error("Session should be nil for non-existent database")
		}

		// Test with empty database name
		session, err = StartSession("")
		if err == nil {
			t.Error("Expected error for empty database name")
		}
		if session != nil {
			t.Error("Session should be nil for empty database name")
		}
	})

	t.Run("Transaction State Errors", func(t *testing.T) {
		session, err := StartSession("tmp")
		if err != nil {
			t.Fatalf("Failed to start session: %v", err)
		}
		defer session.EndSession()

		// Test transaction with invalid state
		txn := &MongoTransaction{
			session:   session.session,
			ctx:       session.ctx,
			started:   false,
			committed: false,
			aborted:   false,
		}

		// Test commit on not started transaction
		err = txn.CommitTransaction()
		if err == nil {
			t.Error("Expected error for commit on not started transaction")
		}

		// Test abort on not started transaction
		err = txn.AbortTransaction()
		if err == nil {
			t.Error("Expected error for abort on not started transaction")
		}
	})

	t.Run("Context Timeout", func(t *testing.T) {
		// Create context with very short timeout
		ctx, cancel := context.WithTimeout(context.Background(), 1*time.Millisecond)
		defer cancel()

		// Start session with timeout context
		session, err := StartSessionWithContext(ctx, "tmp")
		if err != nil {
			t.Fatalf("Failed to start session with context: %v", err)
		}
		defer session.EndSession()

		// Wait for context to timeout
		time.Sleep(10 * time.Millisecond)

		// Try to start transaction with expired context
		txn, err := session.StartTransaction()
		if err != nil {
			// This might fail due to context timeout, which is expected
			t.Logf("Transaction start failed as expected: %v", err)
		} else {
			// If it succeeds, try to commit
			err = txn.CommitTransaction()
			if err != nil {
				t.Logf("Transaction commit failed as expected: %v", err)
			}
		}
	})
}

func TestTransactionPerformance(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("Bulk Operations in Transaction", func(t *testing.T) {
		session, err := StartSession("tmp")
		if err != nil {
			t.Fatalf("Failed to start session: %v", err)
		}
		defer session.EndSession()

		coll := Coll("tmp", "test_bulk")

		start := time.Now()
		err = session.WithTransaction(func(sessCtx mongo.SessionContext) error {
			// Insert multiple documents
			docs := make([]interface{}, 100)
			for i := 0; i < 100; i++ {
				docs[i] = bson.M{
					"_id":   fmt.Sprintf("bulk_%d", i),
					"value": fmt.Sprintf("value_%d", i),
					"index": i,
				}
			}

			_, err := coll.InsertMany(sessCtx, docs)
			if err != nil {
				return err
			}

			// Update multiple documents
			for i := 0; i < 50; i++ {
				_, err := coll.UpdateOne(sessCtx, bson.M{"_id": fmt.Sprintf("bulk_%d", i)}, bson.M{
					"$set": bson.M{"updated": true},
				})
				if err != nil {
					return err
				}
			}

			return nil
		})

		duration := time.Since(start)
		if err != nil {
			t.Fatalf("Bulk transaction failed: %v", err)
		}

		t.Logf("Bulk transaction completed in %v", duration)

		// Verify results
		count, err := coll.CountDocuments(context.Background(), bson.M{})
		if err != nil {
			t.Fatalf("Failed to count documents: %v", err)
		}
		if count != 100 {
			t.Errorf("Expected 100 documents, got %d", count)
		}

		updatedCount, err := coll.CountDocuments(context.Background(), bson.M{"updated": true})
		if err != nil {
			t.Fatalf("Failed to count updated documents: %v", err)
		}
		if updatedCount != 50 {
			t.Errorf("Expected 50 updated documents, got %d", updatedCount)
		}

		// Cleanup
		coll.Drop(context.Background())
	})
}

func TestTransactionConcurrency(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("Concurrent Transactions", func(t *testing.T) {
		coll := Coll("tmp", "test_concurrent")

		// Insert initial documents for each transaction
		for i := 0; i < 10; i++ {
			_, err := coll.InsertOne(context.Background(), bson.M{
				"_id":   fmt.Sprintf("counter_%d", i),
				"value": 0,
			})
			if err != nil {
				t.Fatalf("Failed to insert initial document %d: %v", i, err)
			}
		}

		// Run multiple concurrent transactions, each updating its own document
		const numTransactions = 10
		errors := make(chan error, numTransactions)
		var wg sync.WaitGroup

		for i := 0; i < numTransactions; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()

				session, err := StartSession("tmp")
				if err != nil {
					errors <- fmt.Errorf("session %d failed to start: %v", id, err)
					return
				}
				defer session.EndSession()

				err = session.WithTransaction(func(sessCtx mongo.SessionContext) error {
					// Each transaction updates its own document to avoid conflicts
					docID := fmt.Sprintf("counter_%d", id)

					// Read current value
					var doc bson.M
					err := coll.FindOne(sessCtx, bson.M{"_id": docID}).Decode(&doc)
					if err != nil {
						return err
					}

					currentValue := doc["value"].(int32)

					// Update value
					_, err = coll.UpdateOne(sessCtx, bson.M{"_id": docID}, bson.M{
						"$set": bson.M{"value": currentValue + 1},
					})
					return err
				})

				if err != nil {
					errors <- fmt.Errorf("transaction %d failed: %v", id, err)
				}
			}(i)
		}

		wg.Wait()
		close(errors)

		// Check for errors
		for err := range errors {
			t.Errorf("Concurrent transaction error: %v", err)
		}

		// Verify all documents were updated
		for i := 0; i < numTransactions; i++ {
			var finalDoc bson.M
			err := coll.FindOne(context.Background(), bson.M{"_id": fmt.Sprintf("counter_%d", i)}).Decode(&finalDoc)
			if err != nil {
				t.Fatalf("Failed to find final document %d: %v", i, err)
			}

			finalValue := finalDoc["value"].(int32)
			if finalValue != 1 {
				t.Errorf("Expected final value 1 for document %d, got %d", i, finalValue)
			}
		}

		// Cleanup
		coll.Drop(context.Background())
	})
}

// Helper function for concurrent tests
func TestTransactionHelperFunctions(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("ToBSONDoc", func(t *testing.T) {
		// Test with struct
		type TestStruct struct {
			Name  string `bson:"name"`
			Value int    `bson:"value"`
		}

		testStruct := TestStruct{
			Name:  "test",
			Value: 123,
		}

		doc, err := ToBSONDoc(testStruct)
		if err != nil {
			t.Fatalf("ToBSONDoc failed: %v", err)
		}

		if doc["name"] != "test" {
			t.Errorf("Wrong name value: got %v, want %v", doc["name"], "test")
		}
		if doc["value"] != int32(123) {
			t.Errorf("Wrong value: got %v, want %v", doc["value"], 123)
		}

		// Test with bson.M
		bsonDoc := bson.M{"test": "value"}
		doc, err = ToBSONDoc(bsonDoc)
		if err != nil {
			t.Fatalf("ToBSONDoc failed with bson.M: %v", err)
		}
		if doc["test"] != "value" {
			t.Errorf("Wrong value in bson.M: got %v, want %v", doc["test"], "value")
		}

		// Test with invalid struct (should still work)
		type InvalidStruct struct {
			Chan chan int `bson:"chan"` // Channel cannot be marshaled
		}
		invalidStruct := InvalidStruct{Chan: make(chan int)}
		_, err = ToBSONDoc(invalidStruct)
		if err == nil {
			t.Error("Expected error for invalid struct with channel")
		}
	})

	t.Run("Contains", func(t *testing.T) {
		slice := []string{"a", "b", "c"}

		if !contains(slice, "a") {
			t.Error("contains should return true for 'a'")
		}
		if !contains(slice, "b") {
			t.Error("contains should return true for 'b'")
		}
		if contains(slice, "d") {
			t.Error("contains should return false for 'd'")
		}
		if contains(slice, "") {
			t.Error("contains should return false for empty string")
		}

		// Test with empty slice
		emptySlice := []string{}
		if contains(emptySlice, "a") {
			t.Error("contains should return false for empty slice")
		}

		// Test with nil slice
		var nilSlice []string
		if contains(nilSlice, "a") {
			t.Error("contains should return false for nil slice")
		}
	})

	t.Run("ApplyOptions", func(t *testing.T) {
		// Test with QueryOptions
		opts := QueryOptions{
			Projection: bson.M{"field": 1},
			Sort:       bson.M{"field": 1},
			Limit:      10,
			Skip:       5,
		}

		result := applyOptions([]interface{}{opts})
		if result == nil {
			t.Error("applyOptions should return QueryOptions")
		}
		if result.Projection == nil {
			t.Error("Projection should not be nil")
		}
		if result.Limit != 10 {
			t.Errorf("Limit should be 10, got %d", result.Limit)
		}

		// Test with no QueryOptions
		result = applyOptions([]interface{}{"not_query_options"})
		if result != nil {
			t.Error("applyOptions should return nil for non-QueryOptions")
		}

		// Test with empty options
		result = applyOptions([]interface{}{})
		if result != nil {
			t.Error("applyOptions should return nil for empty options")
		}

		// Test with nil options
		result = applyOptions(nil)
		if result != nil {
			t.Error("applyOptions should return nil for nil options")
		}
	})
}

// Additional tests for better coverage
func TestTransactionEdgeCases(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("Nil Session Operations", func(t *testing.T) {
		var session *MongoSession

		// Test StartTransaction with nil session
		txn, err := session.StartTransaction()
		if err == nil {
			t.Error("Expected error for nil session StartTransaction")
		}
		if txn != nil {
			t.Error("Transaction should be nil for nil session")
		}

		// Test WithTransaction with nil session
		err = session.WithTransaction(func(sessCtx mongo.SessionContext) error {
			return nil
		})
		if err == nil {
			t.Error("Expected error for nil session WithTransaction")
		}

		// Test GetSessionContext with nil session
		ctx := session.GetSessionContext()
		if ctx != nil {
			t.Error("Session context should be nil for nil session")
		}
	})

	t.Run("Nil Transaction Operations", func(t *testing.T) {
		var txn *MongoTransaction

		// Test CommitTransaction with nil transaction
		err := txn.CommitTransaction()
		if err == nil {
			t.Error("Expected error for nil transaction CommitTransaction")
		}

		// Test AbortTransaction with nil transaction
		err = txn.AbortTransaction()
		if err == nil {
			t.Error("Expected error for nil transaction AbortTransaction")
		}

		// Test GetSessionContext with nil transaction
		ctx := txn.GetSessionContext()
		if ctx != nil {
			t.Error("Transaction context should be nil for nil transaction")
		}
	})

	t.Run("Transaction State Transitions", func(t *testing.T) {
		session, err := StartSession("tmp")
		if err != nil {
			t.Fatalf("Failed to start session: %v", err)
		}
		defer session.EndSession()

		// Test transaction state after commit
		txn, err := session.StartTransaction()
		if err != nil {
			t.Fatalf("Failed to start transaction: %v", err)
		}

		err = txn.CommitTransaction()
		if err != nil {
			t.Fatalf("Failed to commit transaction: %v", err)
		}

		// Try to commit again
		err = txn.CommitTransaction()
		if err == nil {
			t.Error("Expected error for already committed transaction")
		}

		// Try to abort committed transaction
		err = txn.AbortTransaction()
		if err == nil {
			t.Error("Expected error for committed transaction abort")
		}

		// Test transaction state after abort
		txn2, err := session.StartTransaction()
		if err != nil {
			t.Fatalf("Failed to start second transaction: %v", err)
		}

		err = txn2.AbortTransaction()
		if err != nil {
			t.Fatalf("Failed to abort transaction: %v", err)
		}

		// Try to commit aborted transaction
		err = txn2.CommitTransaction()
		if err == nil {
			t.Error("Expected error for aborted transaction commit")
		}

		// Try to abort again
		err = txn2.AbortTransaction()
		if err == nil {
			t.Error("Expected error for already aborted transaction")
		}
	})

	t.Run("Session Context Operations", func(t *testing.T) {
		session, err := StartSession("tmp")
		if err != nil {
			t.Fatalf("Failed to start session: %v", err)
		}
		defer session.EndSession()

		// Test session context operations
		sessCtx := session.GetSessionContext()
		if sessCtx == nil {
			t.Fatal("Session context should not be nil")
		}

		// Test collection operations with session context
		coll := Coll("tmp", "test_session_ctx")
		_, err = coll.InsertOne(sessCtx, bson.M{"test": "session_context"})
		if err != nil {
			t.Fatalf("Failed to insert with session context: %v", err)
		}

		// Verify the document was inserted
		var result bson.M
		err = coll.FindOne(context.Background(), bson.M{"test": "session_context"}).Decode(&result)
		if err != nil {
			t.Fatalf("Failed to find inserted document: %v", err)
		}
		if result["test"] != "session_context" {
			t.Errorf("Wrong value: got %v, want %v", result["test"], "session_context")
		}

		// Cleanup
		coll.Drop(context.Background())
	})

	t.Run("Transaction Context Operations", func(t *testing.T) {
		session, err := StartSession("tmp")
		if err != nil {
			t.Fatalf("Failed to start session: %v", err)
		}
		defer session.EndSession()

		txn, err := session.StartTransaction()
		if err != nil {
			t.Fatalf("Failed to start transaction: %v", err)
		}

		// Test transaction context operations
		txnCtx := txn.GetSessionContext()
		if txnCtx == nil {
			t.Fatal("Transaction context should not be nil")
		}

		// Test collection operations with transaction context
		coll := Coll("tmp", "test_txn_ctx")
		_, err = coll.InsertOne(txnCtx, bson.M{"test": "transaction_context"})
		if err != nil {
			t.Fatalf("Failed to insert with transaction context: %v", err)
		}

		// Commit the transaction
		err = txn.CommitTransaction()
		if err != nil {
			t.Fatalf("Failed to commit transaction: %v", err)
		}

		// Verify the document was inserted
		var result bson.M
		err = coll.FindOne(context.Background(), bson.M{"test": "transaction_context"}).Decode(&result)
		if err != nil {
			t.Fatalf("Failed to find inserted document: %v", err)
		}
		if result["test"] != "transaction_context" {
			t.Errorf("Wrong value: got %v, want %v", result["test"], "transaction_context")
		}

		// Cleanup
		coll.Drop(context.Background())
	})
}

func TestDatabaseOperations(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("Database Operations", func(t *testing.T) {
		// Test Database function with valid database
		db := Database("tmp")
		if db == nil {
			t.Fatal("Database should not be nil for valid database")
		}

		// Test Database function with invalid database
		invalidDb := Database("non_existent_db")
		if invalidDb != nil {
			t.Error("Database should be nil for invalid database")
		}

		// Test Coll function with valid parameters
		coll := Coll("tmp", "test_coll")
		if coll == nil {
			t.Fatal("Collection should not be nil for valid parameters")
		}

		// Test Coll function with invalid database
		invalidColl := Coll("non_existent_db", "test_coll")
		if invalidColl != nil {
			t.Error("Collection should be nil for invalid database")
		}

		// Test collection operations
		ctx := context.Background()
		_, err := coll.InsertOne(ctx, bson.M{"test": "value"})
		if err != nil {
			t.Fatalf("Failed to insert document: %v", err)
		}

		// Test collection name
		if coll.Name() != "test_coll" {
			t.Errorf("Wrong collection name: got %s, want %s", coll.Name(), "test_coll")
		}

		// Test database name
		if coll.DBName() != dbConfigs["tmp"].database {
			t.Errorf("Wrong database name: got %s, want %s", coll.DBName(), dbConfigs["tmp"].database)
		}

		// Test ping
		err = coll.Ping(ctx)
		if err != nil {
			t.Fatalf("Failed to ping collection: %v", err)
		}

		// Test get client
		client := coll.GetClient()
		if client == nil {
			t.Error("Client should not be nil")
		}

		// Cleanup
		coll.Drop(ctx)
	})

	t.Run("ListCollections", func(t *testing.T) {
		// Test ListCollections with valid database
		collections, err := ListCollections("tmp")
		if err != nil {
			t.Fatalf("Failed to list collections: %v", err)
		}
		if collections == nil {
			t.Error("Collections should not be nil")
		}

		// Test ListCollections with invalid database
		_, err = ListCollections("non_existent_db")
		if err == nil {
			t.Error("Expected error for invalid database")
		}
	})
}

func TestMongoLogSink(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("MongoLogSink", func(t *testing.T) {
		sink := &mongoLogSink{}

		// Test Info method
		sink.Info(1, "test info message", "key1", "value1", "key2", "value2")

		// Test Error method
		testErr := fmt.Errorf("test error")
		sink.Error(testErr, "test error message", "key1", "value1", "key2", "value2")
	})
}

func TestGomongoEdgeCoverage(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("PreDefColl edge cases", func(t *testing.T) {
		// Should return nil for non-existent preDefColl
		coll := PreDefColl("not_exist")
		if coll != nil {
			t.Error("PreDefColl should return nil for non-existent collection")
		}
	})

	t.Run("Database edge cases", func(t *testing.T) {
		// Should return nil for non-existent db
		db := Database("not_exist_db")
		if db != nil {
			t.Error("Database should return nil for non-existent db")
		}
	})

	t.Run("Coll edge cases", func(t *testing.T) {
		// Should return nil for non-existent db
		coll := Coll("not_exist_db", "coll")
		if coll != nil {
			t.Error("Coll should return nil for non-existent db")
		}
	})

	t.Run("ListCollections edge cases", func(t *testing.T) {
		// Should return error for non-existent db
		_, err := ListCollections("not_exist_db")
		if err == nil {
			t.Error("ListCollections should return error for non-existent db")
		}
	})

	t.Run("addTimestamps edge cases", func(t *testing.T) {
		// Not a bson.M, but a struct
		type S struct {
			X int `bson:"x"`
		}
		m, err := addTimestamps(S{X: 1}, false, false)
		if err != nil {
			t.Errorf("addTimestamps struct failed: %v", err)
		}
		bm, ok := m.(bson.M)
		if !ok || bm["x"] != int32(1) {
			t.Error("addTimestamps did not convert struct correctly")
		}

		// Update mode, with $set
		update := bson.M{"$set": bson.M{"a": 1}}
		m, err = addTimestamps(update, true, false)
		if err != nil {
			t.Errorf("addTimestamps update failed: %v", err)
		}
		um, ok := m.(bson.M)
		if !ok || um["$setOnInsert"] == nil || um["$set"] == nil {
			t.Error("addTimestamps update did not add $setOnInsert/$set")
		}

		// Update mode, with noModifyMt true
		update = bson.M{"$set": bson.M{"a": 1}}
		m, err = addTimestamps(update, true, true)
		if err != nil {
			t.Errorf("addTimestamps update failed: %v", err)
		}
		um, ok = m.(bson.M)
		if !ok || um["$setOnInsert"] == nil {
			t.Error("addTimestamps update did not add $setOnInsert")
		}

		// Test with non-operator fields in update
		update = bson.M{"field1": "value1", "field2": "value2"}
		m, err = addTimestamps(update, true, false)
		if err != nil {
			t.Errorf("addTimestamps update with non-operator fields failed: %v", err)
		}
		um, ok = m.(bson.M)
		if !ok || um["$setOnInsert"] == nil || um["$set"] == nil {
			t.Error("addTimestamps update did not wrap non-operator fields in $set")
		}

		// Test with mixed operators and fields
		update = bson.M{"$set": bson.M{"a": 1}, "field1": "value1", "$inc": bson.M{"counter": 1}}
		m, err = addTimestamps(update, true, false)
		if err != nil {
			t.Errorf("addTimestamps update with mixed operators failed: %v", err)
		}
		um, ok = m.(bson.M)
		if !ok || um["$setOnInsert"] == nil || um["$set"] == nil || um["$inc"] == nil {
			t.Error("addTimestamps update did not handle mixed operators correctly")
		}
	})

	t.Run("extractMetadata edge cases", func(t *testing.T) {
		// With metadata
		m := bson.M{"noModifyMt": true, "foo": 1}
		clean, meta := extractMetadata(m)
		if meta["noModifyMt"] != true {
			t.Error("extractMetadata did not extract metadata")
		}
		cm, ok := clean.(bson.M)
		if !ok || cm["foo"] != 1 {
			t.Error("extractMetadata did not clean metadata fields")
		}
		// Without metadata
		m = bson.M{"foo": 2}
		clean, meta = extractMetadata(m)
		if len(meta) != 0 {
			t.Error("extractMetadata should return empty metadata if none present")
		}
		cm, ok = clean.(bson.M)
		if !ok || cm["foo"] != 2 {
			t.Error("extractMetadata did not return original map if no metadata")
		}

		// Test with non-bson.M input
		nonMap := "not a map"
		clean, meta = extractMetadata(nonMap)
		if clean != nonMap || len(meta) != 0 {
			t.Error("extractMetadata should return original value for non-map input")
		}
	})

	t.Run("logSlowOperation edge cases", func(t *testing.T) {
		// Should log warning if duration > 2s
		logSlowOperation(3*time.Second, "TestOp", "TestColl", "arg1", 123)
		// Should log debug if verboseLevel > 2
		oldVerbose := verboseLevel
		verboseLevel = 3
		logSlowOperation(1*time.Second, "TestOp", "TestColl", "arg2", 456)
		verboseLevel = oldVerbose
	})

	t.Run("mongoLogSink edge cases", func(t *testing.T) {
		sink := &mongoLogSink{}
		sink.Info(2, "info message", "k", "v")
		sink.Error(fmt.Errorf("err"), "error message", "k", "v")
	})
}

// Additional comprehensive tests for better coverage
func TestDatabaseOperationsComprehensive(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("Database ListCollectionNames edge cases", func(t *testing.T) {
		db := Database("tmp")
		if db == nil {
			t.Fatal("Database should not be nil")
		}

		// Test with nil database
		var nilDb *mongoDatabase
		_, err := nilDb.ListCollectionNames(context.Background(), bson.M{})
		if err == nil {
			t.Error("Expected error for nil database")
		}

		// Test with invalid database name
		invalidDb := &mongoDatabase{db: nil}
		_, err = invalidDb.ListCollectionNames(context.Background(), bson.M{})
		if err == nil {
			t.Error("Expected error for invalid database")
		}
	})

	t.Run("Database CreateCollection edge cases", func(t *testing.T) {
		db := Database("tmp")
		if db == nil {
			t.Fatal("Database should not be nil")
		}

		// Test with nil database
		var nilDb *mongoDatabase
		err := nilDb.CreateCollection(context.Background(), "test", nil)
		if err == nil {
			t.Error("Expected error for nil database")
		}

		// Test with invalid database
		invalidDb := &mongoDatabase{db: nil}
		err = invalidDb.CreateCollection(context.Background(), "test", nil)
		if err == nil {
			t.Error("Expected error for invalid database")
		}

		// Test with empty collection name
		err = db.CreateCollection(context.Background(), "", nil)
		if err == nil {
			t.Error("Expected error for empty collection name")
		}
	})
}

func TestCollectionOperationsComprehensive(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("Collection operation edge cases", func(t *testing.T) {
		coll := getCleanCollection(t)

		// Test InsertOne with invalid document
		_, err := coll.InsertOne(context.Background(), nil)
		if err == nil {
			t.Error("Expected error for nil document")
		}

		// Test UpdateOne with invalid filter
		_, err = coll.UpdateOne(context.Background(), nil, bson.M{"$set": bson.M{"field": "value"}})
		if err == nil {
			t.Error("Expected error for nil filter")
		}

		// Test UpdateOne with invalid update
		_, err = coll.UpdateOne(context.Background(), bson.M{"_id": "test"}, nil)
		if err == nil {
			t.Error("Expected error for nil update")
		}

		// Test UpdateMany with invalid filter
		_, err = coll.UpdateMany(context.Background(), nil, bson.M{"$set": bson.M{"field": "value"}})
		if err == nil {
			t.Error("Expected error for nil filter")
		}

		// Test UpdateMany with invalid update
		_, err = coll.UpdateMany(context.Background(), bson.M{"field": "value"}, nil)
		if err == nil {
			t.Error("Expected error for nil update")
		}

		// Test Find with invalid filter
		_, err = coll.Find(context.Background(), nil)
		if err == nil {
			t.Error("Expected error for nil filter")
		}

		// Test FindOne with invalid filter
		result := coll.FindOne(context.Background(), nil)
		if result != nil && result.Err() == nil {
			t.Error("Expected error for nil filter")
		}

		// Test DeleteOne with invalid filter
		_, err = coll.DeleteOne(context.Background(), nil)
		if err == nil {
			t.Error("Expected error for nil filter")
		}

		// Test DeleteMany with invalid filter
		_, err = coll.DeleteMany(context.Background(), nil)
		if err == nil {
			t.Error("Expected error for nil filter")
		}

		// Test FindOneAndUpdate with invalid filter
		result = coll.FindOneAndUpdate(context.Background(), nil, bson.M{"$set": bson.M{"field": "value"}})
		if result != nil && result.Err() == nil {
			t.Error("Expected error for nil filter")
		}

		// Test FindOneAndUpdate with invalid update
		result = coll.FindOneAndUpdate(context.Background(), bson.M{"_id": "test"}, nil)
		if result != nil && result.Err() == nil {
			t.Error("Expected error for nil update")
		}

		// Test ReplaceOne with invalid filter
		_, err = coll.ReplaceOne(context.Background(), nil, bson.M{"field": "value"})
		if err == nil {
			t.Error("Expected error for nil filter")
		}

		// Test ReplaceOne with invalid replacement
		_, err = coll.ReplaceOne(context.Background(), bson.M{"_id": "test"}, nil)
		if err == nil {
			t.Error("Expected error for nil replacement")
		}

		// Test FindOneAndReplace with invalid filter
		result = coll.FindOneAndReplace(context.Background(), nil, bson.M{"field": "value"})
		if result != nil && result.Err() == nil {
			t.Error("Expected error for nil filter")
		}

		// Test FindOneAndReplace with invalid replacement
		result = coll.FindOneAndReplace(context.Background(), bson.M{"_id": "test"}, nil)
		if result != nil && result.Err() == nil {
			t.Error("Expected error for nil replacement")
		}

		// Test Distinct with invalid filter
		_, err = coll.Distinct(context.Background(), "field", nil)
		if err == nil {
			t.Error("Expected error for nil filter")
		}

		// Test CountDocuments with invalid filter
		_, err = coll.CountDocuments(context.Background(), nil)
		if err == nil {
			t.Error("Expected error for nil filter")
		}

		// Test Aggregate with invalid pipeline
		_, err = coll.Aggregate(context.Background(), nil)
		if err == nil {
			t.Error("Expected error for nil pipeline")
		}

		// Test Watch with invalid pipeline
		_, err = coll.Watch(context.Background(), nil)
		if err == nil {
			t.Error("Expected error for nil pipeline")
		}

		// Test Rename with empty new name
		err = coll.Rename(context.Background(), "")
		if err == nil {
			t.Error("Expected error for empty new name")
		}

		// Test FindToArray with invalid filter
		_, err = coll.FindToArray(context.Background(), nil)
		if err == nil {
			t.Error("Expected error for nil filter")
		}

		// Test InsertMany with empty documents
		_, err = coll.InsertMany(context.Background(), []interface{}{})
		if err == nil {
			t.Error("Expected error for empty documents")
		}

		// Test InsertMany with nil documents
		_, err = coll.InsertMany(context.Background(), nil)
		if err == nil {
			t.Error("Expected error for nil documents")
		}

		// Test BulkWrite with empty models
		_, err = coll.BulkWrite(context.Background(), []mongo.WriteModel{})
		if err == nil {
			t.Error("Expected error for empty models")
		}

		// Test BulkWrite with nil models
		_, err = coll.BulkWrite(context.Background(), nil)
		if err == nil {
			t.Error("Expected error for nil models")
		}
	})

	t.Run("Collection operation with context timeout", func(t *testing.T) {
		coll := getCleanCollection(t)

		// Create context with very short timeout
		ctx, cancel := context.WithTimeout(context.Background(), 1*time.Millisecond)
		defer cancel()

		// Wait for context to timeout
		time.Sleep(10 * time.Millisecond)

		// Test operations with expired context
		_, err := coll.InsertOne(ctx, bson.M{"test": "value"})
		if err == nil {
			t.Error("Expected error for expired context")
		}

		_, err = coll.Find(ctx, bson.M{})
		if err == nil {
			t.Error("Expected error for expired context")
		}

		_, err = coll.FindToArray(ctx, bson.M{})
		if err == nil {
			t.Error("Expected error for expired context")
		}
	})
}

func TestUtilityFunctionsComprehensive(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("ToBSONDoc comprehensive tests", func(t *testing.T) {
		// Test with nil
		_, err := ToBSONDoc(nil)
		if err == nil {
			t.Error("Expected error for nil input")
		}

		// Test with unsupported type
		_, err = ToBSONDoc(make(chan int))
		if err == nil {
			t.Error("Expected error for unsupported type")
		}

		// Test with complex struct
		type ComplexStruct struct {
			Name     string                 `bson:"name"`
			Age      int                    `bson:"age"`
			Tags     []string               `bson:"tags"`
			Metadata map[string]interface{} `bson:"metadata"`
		}

		complex := ComplexStruct{
			Name: "test",
			Age:  25,
			Tags: []string{"tag1", "tag2"},
			Metadata: map[string]interface{}{
				"key1": "value1",
				"key2": 123,
			},
		}

		doc, err := ToBSONDoc(complex)
		if err != nil {
			t.Fatalf("ToBSONDoc failed: %v", err)
		}

		if doc["name"] != "test" {
			t.Errorf("Wrong name: got %v, want %v", doc["name"], "test")
		}
		if doc["age"] != int32(25) {
			t.Errorf("Wrong age: got %v, want %v", doc["age"], 25)
		}
		if doc["tags"] == nil {
			t.Error("Tags should not be nil")
		}
		if doc["metadata"] == nil {
			t.Error("Metadata should not be nil")
		}
	})

	t.Run("Contains comprehensive tests", func(t *testing.T) {
		// Test with single element slice
		slice := []string{"single"}
		if !contains(slice, "single") {
			t.Error("contains should return true for single element")
		}
		if contains(slice, "not_found") {
			t.Error("contains should return false for not found element")
		}

		// Test with duplicate elements
		duplicateSlice := []string{"a", "b", "a", "c"}
		if !contains(duplicateSlice, "a") {
			t.Error("contains should return true for duplicate element")
		}

		// Test with case sensitivity
		caseSlice := []string{"Hello", "World"}
		if contains(caseSlice, "hello") {
			t.Error("contains should be case sensitive")
		}
		if contains(caseSlice, "world") {
			t.Error("contains should be case sensitive")
		}
	})

	t.Run("ApplyOptions comprehensive tests", func(t *testing.T) {
		// Test with multiple QueryOptions (should return first one)
		opts1 := QueryOptions{Projection: bson.M{"field1": 1}}
		opts2 := QueryOptions{Projection: bson.M{"field2": 1}}
		result := applyOptions([]interface{}{opts1, opts2})
		if result == nil {
			t.Error("applyOptions should return first QueryOptions")
		}
		if result.Projection == nil {
			t.Error("Projection should not be nil")
		}

		// Test with mixed types
		mixedOpts := []interface{}{
			"string",
			QueryOptions{Projection: bson.M{"field": 1}},
			123,
			bson.M{"key": "value"},
		}
		result = applyOptions(mixedOpts)
		if result == nil {
			t.Error("applyOptions should return QueryOptions from mixed types")
		}

		// Test with QueryOptions at end
		endOpts := []interface{}{
			"string",
			123,
			QueryOptions{Projection: bson.M{"field": 1}},
		}
		result = applyOptions(endOpts)
		if result == nil {
			t.Error("applyOptions should return QueryOptions at end")
		}
	})
}

func TestErrorHandlingComprehensive(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("InitMongoDB error cases", func(t *testing.T) {
		// Test with invalid config
		originalCfg := os.Getenv("RMBASE_FILE_CFG")
		defer os.Setenv("RMBASE_FILE_CFG", originalCfg)

		// Set invalid config path
		os.Setenv("RMBASE_FILE_CFG", "/non/existent/path.ini")

		// This should fail but not panic
		err := InitMongoDB()
		if err == nil {
			t.Log("InitMongoDB with invalid config should fail gracefully")
		}
	})

	t.Run("Database configuration error cases", func(t *testing.T) {
		// Test Database with empty name
		db := Database("")
		if db != nil {
			t.Error("Database should return nil for empty name")
		}

		// Test Coll with empty database name
		coll := Coll("", "test")
		if coll != nil {
			t.Error("Coll should return nil for empty database name")
		}

		// Test Coll with empty collection name
		coll = Coll("tmp", "")
		if coll != nil {
			t.Error("Coll should return nil for empty collection name")
		}

		// Test PreDefColl with empty name
		coll = PreDefColl("")
		if coll != nil {
			t.Error("PreDefColl should return nil for empty name")
		}
	})

	t.Run("Collection operation error cases", func(t *testing.T) {
		// Test with nil collection
		var nilColl *MongoCollection
		_, err := nilColl.InsertOne(context.Background(), bson.M{"test": "value"})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.UpdateOne(context.Background(), bson.M{"_id": "test"}, bson.M{"$set": bson.M{"field": "value"}})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.Find(context.Background(), bson.M{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		result := nilColl.FindOne(context.Background(), bson.M{})
		if result != nil && result.Err() == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.DeleteOne(context.Background(), bson.M{"_id": "test"})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.DeleteMany(context.Background(), bson.M{"field": "value"})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		result = nilColl.FindOneAndUpdate(context.Background(), bson.M{"_id": "test"}, bson.M{"$set": bson.M{"field": "value"}})
		if result != nil && result.Err() == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.ReplaceOne(context.Background(), bson.M{"_id": "test"}, bson.M{"field": "value"})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		result = nilColl.FindOneAndReplace(context.Background(), bson.M{"_id": "test"}, bson.M{"field": "value"})
		if result != nil && result.Err() == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.Distinct(context.Background(), "field", bson.M{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.CountDocuments(context.Background(), bson.M{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.EstimatedDocumentCount(context.Background())
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.CreateIndex(context.Background(), mongo.IndexModel{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.CreateIndexes(context.Background(), []mongo.IndexModel{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		err = nilColl.DropIndex(context.Background(), "index_name")
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.Aggregate(context.Background(), []bson.M{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.Watch(context.Background(), []bson.M{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		err = nilColl.Rename(context.Background(), "new_name")
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.FindToArray(context.Background(), bson.M{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.InsertMany(context.Background(), []interface{}{bson.M{"test": "value"}})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		err = nilColl.Drop(context.Background())
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.BulkWrite(context.Background(), []mongo.WriteModel{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		// Test collection name and database name with nil collection
		if nilColl.Name() != "" {
			t.Error("Name should return empty string for nil collection")
		}

		if nilColl.DBName() != "" {
			t.Error("DBName should return empty string for nil collection")
		}

		if nilColl.Database() != nil {
			t.Error("Database should return nil for nil collection")
		}

		if nilColl.Ping(context.Background()) == nil {
			t.Error("Ping should return error for nil collection")
		}

		if nilColl.GetClient() != nil {
			t.Error("GetClient should return nil for nil collection")
		}
	})
}

func TestTransactionErrorHandlingComprehensive(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("Transaction error cases", func(t *testing.T) {
		// Test StartSession with empty database name
		session, err := StartSession("")
		if err == nil {
			t.Error("Expected error for empty database name")
		}
		if session != nil {
			t.Error("Session should be nil for empty database name")
		}

		// Test StartSessionWithContext with empty database name
		session, err = StartSessionWithContext(context.Background(), "")
		if err == nil {
			t.Error("Expected error for empty database name")
		}
		if session != nil {
			t.Error("Session should be nil for empty database name")
		}

		// Test StartSessionWithContext with nil context
		session, err = StartSessionWithContext(nil, "tmp")
		if err == nil {
			t.Error("Expected error for nil context")
		}
		if session != nil {
			t.Error("Session should be nil for nil context")
		}

		// Test transaction with nil session field
		txn := &MongoTransaction{
			session:   nil,
			ctx:       context.Background(),
			started:   true,
			committed: false,
			aborted:   false,
		}

		err = txn.CommitTransaction()
		if err == nil {
			t.Error("Expected error for transaction with nil session")
		}

		err = txn.AbortTransaction()
		if err == nil {
			t.Error("Expected error for transaction with nil session")
		}

		ctx := txn.GetSessionContext()
		if ctx != nil {
			t.Error("Session context should be nil for transaction with nil session")
		}
	})

	t.Run("Transaction state error cases", func(t *testing.T) {
		session, err := StartSession("tmp")
		if err != nil {
			t.Fatalf("Failed to start session: %v", err)
		}
		defer session.EndSession()

		// Test transaction with nil session
		var nilTxn *MongoTransaction
		err = nilTxn.CommitTransaction()
		if err == nil {
			t.Error("Expected error for nil transaction")
		}

		err = nilTxn.AbortTransaction()
		if err == nil {
			t.Error("Expected error for nil transaction")
		}

		ctx := nilTxn.GetSessionContext()
		if ctx != nil {
			t.Error("Session context should be nil for nil transaction")
		}

		// Test transaction with nil session field
		txn := &MongoTransaction{
			session:   nil,
			ctx:       context.Background(),
			started:   true,
			committed: false,
			aborted:   false,
		}

		err = txn.CommitTransaction()
		if err == nil {
			t.Error("Expected error for transaction with nil session")
		}

		err = txn.AbortTransaction()
		if err == nil {
			t.Error("Expected error for transaction with nil session")
		}
	})
}

func TestPerformanceAndStressTests(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("Large document operations", func(t *testing.T) {
		coll := getCleanCollection(t)

		// Create large document
		largeDoc := bson.M{}
		for i := 0; i < 1000; i++ {
			largeDoc[fmt.Sprintf("field_%d", i)] = fmt.Sprintf("value_%d", i)
		}

		// Test InsertOne with large document
		start := time.Now()
		_, err := coll.InsertOne(context.Background(), largeDoc)
		duration := time.Since(start)
		if err != nil {
			t.Fatalf("Failed to insert large document: %v", err)
		}
		t.Logf("Large document insert took: %v", duration)

		// Test Find with large document
		start = time.Now()
		_, err = coll.Find(context.Background(), largeDoc)
		duration = time.Since(start)
		if err != nil {
			t.Fatalf("Failed to find large document: %v", err)
		}
		t.Logf("Large document find took: %v", duration)
	})

	t.Run("Concurrent operations", func(t *testing.T) {
		coll := getCleanCollection(t)
		const numGoroutines = 10
		const operationsPerGoroutine = 10

		var wg sync.WaitGroup
		errors := make(chan error, numGoroutines*operationsPerGoroutine)

		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				for j := 0; j < operationsPerGoroutine; j++ {
					doc := bson.M{
						"_id":       fmt.Sprintf("concurrent_%d_%d", id, j),
						"goroutine": id,
						"operation": j,
					}
					_, err := coll.InsertOne(context.Background(), doc)
					if err != nil {
						errors <- fmt.Errorf("goroutine %d operation %d failed: %v", id, j, err)
					}
				}
			}(i)
		}

		wg.Wait()
		close(errors)

		// Check for errors
		for err := range errors {
			t.Errorf("Concurrent operation error: %v", err)
		}

		// Verify all documents were inserted
		count, err := coll.CountDocuments(context.Background(), bson.M{})
		if err != nil {
			t.Fatalf("Failed to count documents: %v", err)
		}
		expectedCount := int64(numGoroutines * operationsPerGoroutine)
		if count != expectedCount {
			t.Errorf("Expected %d documents, got %d", expectedCount, count)
		}
	})
}

// Additional tests for better coverage
func TestAdditionalCoverage(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("Database operations with nil database", func(t *testing.T) {
		var nilDb *mongoDatabase

		// Test ListCollectionNames with nil database
		_, err := nilDb.ListCollectionNames(context.Background(), bson.M{})
		if err == nil {
			t.Error("Expected error for nil database")
		}

		// Test CreateCollection with nil database
		err = nilDb.CreateCollection(context.Background(), "test", nil)
		if err == nil {
			t.Error("Expected error for nil database")
		}
	})

	t.Run("Collection operations with invalid collection", func(t *testing.T) {
		// Create a collection with nil underlying collection
		invalidColl := &MongoCollection{coll: nil}

		// Test all operations with invalid collection
		_, err := invalidColl.InsertOne(context.Background(), bson.M{"test": "value"})
		if err == nil {
			t.Error("Expected error for invalid collection")
		}

		_, err = invalidColl.UpdateOne(context.Background(), bson.M{"_id": "test"}, bson.M{"$set": bson.M{"field": "value"}})
		if err == nil {
			t.Error("Expected error for invalid collection")
		}

		_, err = invalidColl.UpdateMany(context.Background(), bson.M{"field": "value"}, bson.M{"$set": bson.M{"field": "value"}})
		if err == nil {
			t.Error("Expected error for invalid collection")
		}

		_, err = invalidColl.Find(context.Background(), bson.M{})
		if err == nil {
			t.Error("Expected error for invalid collection")
		}

		result := invalidColl.FindOne(context.Background(), bson.M{})
		if result != nil && result.Err() == nil {
			t.Error("Expected error for invalid collection")
		}

		_, err = invalidColl.DeleteOne(context.Background(), bson.M{"_id": "test"})
		if err == nil {
			t.Error("Expected error for invalid collection")
		}

		_, err = invalidColl.DeleteMany(context.Background(), bson.M{"field": "value"})
		if err == nil {
			t.Error("Expected error for invalid collection")
		}

		result = invalidColl.FindOneAndUpdate(context.Background(), bson.M{"_id": "test"}, bson.M{"$set": bson.M{"field": "value"}})
		if result != nil && result.Err() == nil {
			t.Error("Expected error for invalid collection")
		}

		_, err = invalidColl.ReplaceOne(context.Background(), bson.M{"_id": "test"}, bson.M{"field": "value"})
		if err == nil {
			t.Error("Expected error for invalid collection")
		}

		result = invalidColl.FindOneAndReplace(context.Background(), bson.M{"_id": "test"}, bson.M{"field": "value"})
		if result != nil && result.Err() == nil {
			t.Error("Expected error for invalid collection")
		}

		_, err = invalidColl.Distinct(context.Background(), "field", bson.M{})
		if err == nil {
			t.Error("Expected error for invalid collection")
		}

		_, err = invalidColl.CountDocuments(context.Background(), bson.M{})
		if err == nil {
			t.Error("Expected error for invalid collection")
		}

		_, err = invalidColl.EstimatedDocumentCount(context.Background())
		if err == nil {
			t.Error("Expected error for invalid collection")
		}

		_, err = invalidColl.CreateIndex(context.Background(), mongo.IndexModel{})
		if err == nil {
			t.Error("Expected error for invalid collection")
		}

		_, err = invalidColl.CreateIndexes(context.Background(), []mongo.IndexModel{})
		if err == nil {
			t.Error("Expected error for invalid collection")
		}

		err = invalidColl.DropIndex(context.Background(), "index_name")
		if err == nil {
			t.Error("Expected error for invalid collection")
		}

		_, err = invalidColl.Aggregate(context.Background(), []bson.M{})
		if err == nil {
			t.Error("Expected error for invalid collection")
		}

		_, err = invalidColl.Watch(context.Background(), []bson.M{})
		if err == nil {
			t.Error("Expected error for invalid collection")
		}

		err = invalidColl.Rename(context.Background(), "new_name")
		if err == nil {
			t.Error("Expected error for invalid collection")
		}

		_, err = invalidColl.FindToArray(context.Background(), bson.M{})
		if err == nil {
			t.Error("Expected error for invalid collection")
		}

		_, err = invalidColl.InsertMany(context.Background(), []interface{}{bson.M{"test": "value"}})
		if err == nil {
			t.Error("Expected error for invalid collection")
		}

		err = invalidColl.Drop(context.Background())
		if err == nil {
			t.Error("Expected error for invalid collection")
		}

		_, err = invalidColl.BulkWrite(context.Background(), []mongo.WriteModel{})
		if err == nil {
			t.Error("Expected error for invalid collection")
		}

		// Test collection properties with invalid collection
		if invalidColl.Name() != "" {
			t.Error("Name should return empty string for invalid collection")
		}

		if invalidColl.DBName() != "" {
			t.Error("DBName should return empty string for invalid collection")
		}

		if invalidColl.Database() != nil {
			t.Error("Database should return nil for invalid collection")
		}

		if invalidColl.Ping(context.Background()) == nil {
			t.Error("Ping should return error for invalid collection")
		}

		if invalidColl.GetClient() != nil {
			t.Error("GetClient should return nil for invalid collection")
		}
	})

	t.Run("Utility functions edge cases", func(t *testing.T) {
		// Test addTimestamps with various edge cases

		// Test with nil document
		_, err := addTimestamps(nil, false, false)
		if err == nil {
			t.Error("Expected error for nil document")
		}

		// Test with unsupported type
		_, err = addTimestamps(make(chan int), false, false)
		if err == nil {
			t.Error("Expected error for unsupported type")
		}

		// Test with complex update structure
		complexUpdate := bson.M{
			"$set":   bson.M{"field1": "value1"},
			"$inc":   bson.M{"counter": 1},
			"$push":  bson.M{"array": "item"},
			"field2": "value2", // Non-operator field
		}
		result, err := addTimestamps(complexUpdate, true, false)
		if err != nil {
			t.Errorf("addTimestamps failed with complex update: %v", err)
		}
		um, ok := result.(bson.M)
		if !ok {
			t.Error("addTimestamps should return bson.M")
		}
		if um["$setOnInsert"] == nil {
			t.Error("addTimestamps should add $setOnInsert")
		}
		if um["$set"] == nil {
			t.Error("addTimestamps should add $set")
		}
		if um["$inc"] == nil {
			t.Error("addTimestamps should preserve $inc")
		}
		if um["$push"] == nil {
			t.Error("addTimestamps should preserve $push")
		}

		// Test extractMetadata with various inputs

		// Test with nil input
		clean, meta := extractMetadata(nil)
		if clean != nil || len(meta) != 0 {
			t.Error("extractMetadata should handle nil input")
		}

		// Test with non-map input
		clean, meta = extractMetadata("string")
		if clean != "string" || len(meta) != 0 {
			t.Error("extractMetadata should return original value for non-map input")
		}

		// Test with empty map
		clean, meta = extractMetadata(bson.M{})
		if len(meta) != 0 {
			t.Error("extractMetadata should return empty metadata for empty map")
		}

		// Test with multiple metadata fields
		multiMeta := bson.M{
			"noModifyMt": true,
			"field1":     "value1",
			"field2":     "value2",
		}
		clean, meta = extractMetadata(multiMeta)
		if meta["noModifyMt"] != true {
			t.Error("extractMetadata should extract noModifyMt")
		}
		cm, ok := clean.(bson.M)
		if !ok || cm["field1"] != "value1" || cm["field2"] != "value2" {
			t.Error("extractMetadata should preserve non-metadata fields")
		}
	})

	t.Run("QueryOptions edge cases", func(t *testing.T) {
		// Test with nil options
		result := applyOptions(nil)
		if result != nil {
			t.Error("applyOptions should return nil for nil options")
		}

		// Test with empty options slice
		result = applyOptions([]interface{}{})
		if result != nil {
			t.Error("applyOptions should return nil for empty options")
		}

		// Test with non-QueryOptions types
		result = applyOptions([]interface{}{
			"string",
			123,
			bson.M{"key": "value"},
			[]string{"a", "b", "c"},
		})
		if result != nil {
			t.Error("applyOptions should return nil for non-QueryOptions types")
		}

		// Test with QueryOptions at different positions
		opts1 := QueryOptions{Projection: bson.M{"field1": 1}}
		opts2 := QueryOptions{Projection: bson.M{"field2": 1}}

		// Test with QueryOptions at beginning
		result = applyOptions([]interface{}{opts1, "string", 123})
		if result == nil || result.Projection == nil {
			t.Error("applyOptions should return first QueryOptions")
		}

		// Test with QueryOptions at end
		result = applyOptions([]interface{}{"string", 123, opts2})
		if result == nil || result.Projection == nil {
			t.Error("applyOptions should return QueryOptions at end")
		}

		// Test with multiple QueryOptions (should return first)
		result = applyOptions([]interface{}{opts1, opts2})
		if result == nil || result.Projection == nil {
			t.Error("applyOptions should return first QueryOptions when multiple present")
		}
	})

	t.Run("Transaction edge cases", func(t *testing.T) {
		// Test StartSession with invalid database
		session, err := StartSession("invalid_db")
		if err == nil {
			t.Error("Expected error for invalid database")
		}
		if session != nil {
			t.Error("Session should be nil for invalid database")
		}

		// Test StartSessionWithContext with invalid database
		session, err = StartSessionWithContext(context.Background(), "invalid_db")
		if err == nil {
			t.Error("Expected error for invalid database")
		}
		if session != nil {
			t.Error("Session should be nil for invalid database")
		}

		// Test transaction with nil session field
		txn := &MongoTransaction{
			session:   nil,
			ctx:       context.Background(),
			started:   true,
			committed: false,
			aborted:   false,
		}

		err = txn.CommitTransaction()
		if err == nil {
			t.Error("Expected error for transaction with nil session")
		}

		err = txn.AbortTransaction()
		if err == nil {
			t.Error("Expected error for transaction with nil session")
		}

		ctx := txn.GetSessionContext()
		if ctx != nil {
			t.Error("Session context should be nil for transaction with nil session")
		}
	})

	t.Run("Collection operations with context cancellation", func(t *testing.T) {
		coll := getCleanCollection(t)

		// Create context with cancellation
		ctx, cancel := context.WithCancel(context.Background())
		cancel() // Cancel immediately

		// Test operations with cancelled context
		_, err := coll.InsertOne(ctx, bson.M{"test": "value"})
		if err == nil {
			t.Error("Expected error for cancelled context")
		}

		_, err = coll.Find(ctx, bson.M{})
		if err == nil {
			t.Error("Expected error for cancelled context")
		}

		_, err = coll.FindToArray(ctx, bson.M{})
		if err == nil {
			t.Error("Expected error for cancelled context")
		}

		_, err = coll.UpdateOne(ctx, bson.M{"_id": "test"}, bson.M{"$set": bson.M{"field": "value"}})
		if err == nil {
			t.Error("Expected error for cancelled context")
		}

		_, err = coll.DeleteOne(ctx, bson.M{"_id": "test"})
		if err == nil {
			t.Error("Expected error for cancelled context")
		}
	})

	t.Run("Database operations with context cancellation", func(t *testing.T) {
		db := Database("tmp")
		if db == nil {
			t.Fatal("Database should not be nil")
		}

		// Create context with cancellation
		ctx, cancel := context.WithCancel(context.Background())
		cancel() // Cancel immediately

		// Test operations with cancelled context
		_, err := db.ListCollectionNames(ctx, bson.M{})
		if err == nil {
			t.Error("Expected error for cancelled context")
		}

		err = db.CreateCollection(ctx, "test_cancelled", nil)
		if err == nil {
			t.Error("Expected error for cancelled context")
		}
	})

	t.Run("Transaction operations with context cancellation", func(t *testing.T) {
		// Create context with cancellation
		ctx, cancel := context.WithCancel(context.Background())
		cancel() // Cancel immediately

		// Create session with cancelled context
		session, err := StartSessionWithContext(ctx, "tmp")
		if err != nil {
			t.Fatalf("Failed to start session: %v", err)
		}
		defer session.EndSession()

		// Test transaction operations with cancelled context
		// Note: MongoDB might not immediately fail on context cancellation
		// so we'll just test that the session was created successfully
		if session == nil {
			t.Error("Session should not be nil even with cancelled context")
		}
	})

	t.Run("Logging edge cases", func(t *testing.T) {
		// Test logSlowOperation with various durations
		logSlowOperation(1*time.Second, "TestOp", "TestColl", "arg1", 123)
		logSlowOperation(3*time.Second, "TestOp", "TestColl", "arg2", 456)
		logSlowOperation(0*time.Second, "TestOp", "TestColl", "arg3", 789)

		// Test mongoLogSink with various inputs
		sink := &mongoLogSink{}
		sink.Info(0, "info message")
		sink.Info(1, "info message", "key1", "value1")
		sink.Info(2, "info message", "key1", "value1", "key2", "value2")
		sink.Error(nil, "error message")
		sink.Error(fmt.Errorf("test error"), "error message")
		sink.Error(fmt.Errorf("test error"), "error message", "key1", "value1")
	})

	t.Run("Configuration edge cases", func(t *testing.T) {
		// Test with empty database name
		db := Database("")
		if db != nil {
			t.Error("Database should return nil for empty name")
		}

		// Test with empty collection name
		coll := Coll("tmp", "")
		if coll != nil {
			t.Error("Collection should return nil for empty name")
		}

		// Test with empty predefined collection name
		coll = PreDefColl("")
		if coll != nil {
			t.Error("PreDefColl should return nil for empty name")
		}

		// Test ListCollections with empty database name
		_, err := ListCollections("")
		if err == nil {
			t.Error("Expected error for empty database name")
		}
	})

	t.Run("Transaction_operations_with_context_cancellation", func(t *testing.T) {
		coll := getCleanCollection(t)
		ctx, cancel := context.WithCancel(context.Background())
		cancel()
		_, _ = coll.InsertOne(ctx, bson.M{"test": "value"}) // ignore err, see comment above
		_, _ = coll.Find(ctx, bson.M{})
		_, _ = coll.FindToArray(ctx, bson.M{})
		_, _ = coll.UpdateOne(ctx, bson.M{"_id": "test"}, bson.M{"$set": bson.M{"field": "value"}})
		_, _ = coll.DeleteOne(ctx, bson.M{"_id": "test"})
	})
}

func TestAdditionalCoverageComprehensive(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("InitMongoDB error handling", func(t *testing.T) {
		// Test with invalid config structure
		originalCfg := os.Getenv("RMBASE_FILE_CFG")
		defer os.Setenv("RMBASE_FILE_CFG", originalCfg)

		// Set invalid config path
		os.Setenv("RMBASE_FILE_CFG", "/non/existent/path.ini")

		// This should fail but not panic
		err := InitMongoDB()
		if err == nil {
			t.Log("InitMongoDB with invalid config should fail gracefully")
		}
	})

	t.Run("Database configuration edge cases", func(t *testing.T) {
		// Test Database with empty name
		db := Database("")
		if db != nil {
			t.Error("Database should return nil for empty name")
		}

		// Test Coll with empty database name
		coll := Coll("", "test")
		if coll != nil {
			t.Error("Coll should return nil for empty database name")
		}

		// Test Coll with empty collection name
		coll = Coll("tmp", "")
		if coll != nil {
			t.Error("Coll should return nil for empty collection name")
		}

		// Test PreDefColl with empty name
		coll = PreDefColl("")
		if coll != nil {
			t.Error("PreDefColl should return nil for empty name")
		}

		// Test ListCollections with empty database name
		_, err := ListCollections("")
		if err == nil {
			t.Error("Expected error for empty database name")
		}
	})

	t.Run("Collection operation edge cases", func(t *testing.T) {
		// Test with nil collection
		var nilColl *MongoCollection
		_, err := nilColl.InsertOne(context.Background(), bson.M{"test": "value"})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.UpdateOne(context.Background(), bson.M{"_id": "test"}, bson.M{"$set": bson.M{"field": "value"}})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.UpdateMany(context.Background(), bson.M{"field": "value"}, bson.M{"$set": bson.M{"field": "value"}})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.Find(context.Background(), bson.M{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		result := nilColl.FindOne(context.Background(), bson.M{})
		if result != nil && result.Err() == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.DeleteOne(context.Background(), bson.M{"_id": "test"})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.DeleteMany(context.Background(), bson.M{"field": "value"})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		result = nilColl.FindOneAndUpdate(context.Background(), bson.M{"_id": "test"}, bson.M{"$set": bson.M{"field": "value"}})
		if result != nil && result.Err() == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.ReplaceOne(context.Background(), bson.M{"_id": "test"}, bson.M{"field": "value"})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		result = nilColl.FindOneAndReplace(context.Background(), bson.M{"_id": "test"}, bson.M{"field": "value"})
		if result != nil && result.Err() == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.Distinct(context.Background(), "field", bson.M{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.CountDocuments(context.Background(), bson.M{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.EstimatedDocumentCount(context.Background())
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.CreateIndex(context.Background(), mongo.IndexModel{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.CreateIndexes(context.Background(), []mongo.IndexModel{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		err = nilColl.DropIndex(context.Background(), "index_name")
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.Aggregate(context.Background(), []bson.M{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.Watch(context.Background(), []bson.M{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		err = nilColl.Rename(context.Background(), "new_name")
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.FindToArray(context.Background(), bson.M{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.InsertMany(context.Background(), []interface{}{bson.M{"test": "value"}})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		err = nilColl.Drop(context.Background())
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.BulkWrite(context.Background(), []mongo.WriteModel{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		// Test collection properties with nil collection
		if nilColl.Name() != "" {
			t.Error("Name should return empty string for nil collection")
		}

		if nilColl.DBName() != "" {
			t.Error("DBName should return empty string for nil collection")
		}

		if nilColl.Database() != nil {
			t.Error("Database should return nil for nil collection")
		}

		if nilColl.Ping(context.Background()) == nil {
			t.Error("Ping should return error for nil collection")
		}

		if nilColl.GetClient() != nil {
			t.Error("GetClient should return nil for nil collection")
		}
	})

	t.Run("Database operations edge cases", func(t *testing.T) {
		var nilDb *mongoDatabase

		// Test ListCollectionNames with nil database
		_, err := nilDb.ListCollectionNames(context.Background(), bson.M{})
		if err == nil {
			t.Error("Expected error for nil database")
		}

		// Test CreateCollection with nil database
		err = nilDb.CreateCollection(context.Background(), "test", nil)
		if err == nil {
			t.Error("Expected error for nil database")
		}

		// Test with invalid database
		invalidDb := &mongoDatabase{db: nil}
		_, err = invalidDb.ListCollectionNames(context.Background(), bson.M{})
		if err == nil {
			t.Error("Expected error for invalid database")
		}

		err = invalidDb.CreateCollection(context.Background(), "test", nil)
		if err == nil {
			t.Error("Expected error for invalid database")
		}
	})

	t.Run("Transaction error cases", func(t *testing.T) {
		// Test StartSession with empty database name
		session, err := StartSession("")
		if err == nil {
			t.Error("Expected error for empty database name")
		}
		if session != nil {
			t.Error("Session should be nil for empty database name")
		}

		// Test StartSessionWithContext with empty database name
		session, err = StartSessionWithContext(context.Background(), "")
		if err == nil {
			t.Error("Expected error for empty database name")
		}
		if session != nil {
			t.Error("Session should be nil for empty database name")
		}

		// Test StartSessionWithContext with nil context
		session, err = StartSessionWithContext(nil, "tmp")
		if err == nil {
			t.Error("Expected error for nil context")
		}
		if session != nil {
			t.Error("Session should be nil for nil context")
		}
	})
}

// Additional comprehensive tests for better coverage
func TestComprehensiveCoverage(t *testing.T) {
	cleanup := setupTestMongoDB(t)
	defer cleanup()

	t.Run("AddTimestamps edge cases", func(t *testing.T) {
		// Test with nil document
		_, err := addTimestamps(nil, false, false)
		if err == nil {
			t.Error("Expected error for nil document")
		}

		// Test with unsupported type
		_, err = addTimestamps(make(chan int), false, false)
		if err == nil {
			t.Error("Expected error for unsupported type")
		}

		// Test with complex update structure
		complexUpdate := bson.M{
			"$set":   bson.M{"field1": "value1"},
			"$inc":   bson.M{"counter": 1},
			"$push":  bson.M{"array": "item"},
			"field2": "value2", // Non-operator field
		}
		result, err := addTimestamps(complexUpdate, true, false)
		if err != nil {
			t.Errorf("addTimestamps failed with complex update: %v", err)
		}
		um, ok := result.(bson.M)
		if !ok {
			t.Error("addTimestamps should return bson.M")
		}
		if um["$setOnInsert"] == nil {
			t.Error("addTimestamps should add $setOnInsert")
		}
		if um["$set"] == nil {
			t.Error("addTimestamps should add $set")
		}
		if um["$inc"] == nil {
			t.Error("addTimestamps should preserve $inc")
		}
		if um["$push"] == nil {
			t.Error("addTimestamps should preserve $push")
		}

		// Test with noModifyMt = true
		result, err = addTimestamps(complexUpdate, true, true)
		if err != nil {
			t.Errorf("addTimestamps failed with noModifyMt: %v", err)
		}
		um, ok = result.(bson.M)
		if !ok {
			t.Error("addTimestamps should return bson.M")
		}
		t.Logf("Result with noModifyMt=true: %+v", um)
		if um["$setOnInsert"] == nil {
			t.Error("addTimestamps should add $setOnInsert")
		}
		// When noModifyMt is true, $set should not be added by addTimestamps
		if um["$set"] != nil {
			setFields, ok := um["$set"].(bson.M)
			if !ok {
				t.Error("$set should be bson.M if present")
			}
			t.Logf("$set fields: %+v", setFields)
			if setFields["_mt"] != nil {
				t.Error("addTimestamps should not add _mt to $set when noModifyMt is true")
			}
			// If $set exists in input, field2 should be inside $set (because input $set is copied)
			if setFields["field2"] != "value2" {
				t.Errorf("Expected field2 to be in $set, got: %v", setFields["field2"])
			}
			// field2 should not be at top level when $set exists
			if um["field2"] != nil {
				t.Error("addTimestamps should not have field2 at top level when $set exists")
			}
		} else {
			// If $set does not exist, field2 should be at the top level
			if um["field2"] != "value2" {
				t.Error("addTimestamps should preserve non-operator fields at top level if $set does not exist")
			}
		}
	})

	t.Run("ExtractMetadata edge cases", func(t *testing.T) {
		// Test with nil input
		clean, meta := extractMetadata(nil)
		if clean != nil || len(meta) != 0 {
			t.Error("extractMetadata should handle nil input")
		}

		// Test with non-map input
		clean, meta = extractMetadata("string")
		if clean != "string" || len(meta) != 0 {
			t.Error("extractMetadata should return original value for non-map input")
		}

		// Test with empty map
		clean, meta = extractMetadata(bson.M{})
		if len(meta) != 0 {
			t.Error("extractMetadata should return empty metadata for empty map")
		}

		// Test with multiple metadata fields
		multiMeta := bson.M{
			"noModifyMt": true,
			"field1":     "value1",
			"field2":     "value2",
		}
		clean, meta = extractMetadata(multiMeta)
		if meta["noModifyMt"] != true {
			t.Error("extractMetadata should extract noModifyMt")
		}
		cm, ok := clean.(bson.M)
		if !ok || cm["field1"] != "value1" || cm["field2"] != "value2" {
			t.Error("extractMetadata should preserve non-metadata fields")
		}
	})

	t.Run("ToBSONDoc edge cases", func(t *testing.T) {
		// Test with nil
		_, err := ToBSONDoc(nil)
		if err == nil {
			t.Error("Expected error for nil input")
		}

		// Test with unsupported type
		_, err = ToBSONDoc(make(chan int))
		if err == nil {
			t.Error("Expected error for unsupported type")
		}

		// Test with complex struct
		type ComplexStruct struct {
			Name     string                 `bson:"name"`
			Age      int                    `bson:"age"`
			Tags     []string               `bson:"tags"`
			Metadata map[string]interface{} `bson:"metadata"`
		}

		complex := ComplexStruct{
			Name: "test",
			Age:  25,
			Tags: []string{"tag1", "tag2"},
			Metadata: map[string]interface{}{
				"key1": "value1",
				"key2": 123,
			},
		}

		doc, err := ToBSONDoc(complex)
		if err != nil {
			t.Fatalf("ToBSONDoc failed: %v", err)
		}

		if doc["name"] != "test" {
			t.Errorf("Wrong name: got %v, want %v", doc["name"], "test")
		}
		if doc["age"] != int32(25) {
			t.Errorf("Wrong age: got %v, want %v", doc["age"], 25)
		}
		if doc["tags"] == nil {
			t.Error("Tags should not be nil")
		}
		if doc["metadata"] == nil {
			t.Error("Metadata should not be nil")
		}
	})

	t.Run("Collection operations with invalid parameters", func(t *testing.T) {
		coll := getCleanCollection(t)

		// Test InsertOne with invalid document
		_, err := coll.InsertOne(context.Background(), nil)
		if err == nil {
			t.Error("Expected error for nil document")
		}

		// Test UpdateOne with invalid filter
		_, err = coll.UpdateOne(context.Background(), nil, bson.M{"$set": bson.M{"field": "value"}})
		if err == nil {
			t.Error("Expected error for nil filter")
		}

		// Test UpdateOne with invalid update
		_, err = coll.UpdateOne(context.Background(), bson.M{"_id": "test"}, nil)
		if err == nil {
			t.Error("Expected error for nil update")
		}

		// Test UpdateMany with invalid filter
		_, err = coll.UpdateMany(context.Background(), nil, bson.M{"$set": bson.M{"field": "value"}})
		if err == nil {
			t.Error("Expected error for nil filter")
		}

		// Test UpdateMany with invalid update
		_, err = coll.UpdateMany(context.Background(), bson.M{"field": "value"}, nil)
		if err == nil {
			t.Error("Expected error for nil update")
		}

		// Test Find with invalid filter
		_, err = coll.Find(context.Background(), nil)
		if err == nil {
			t.Error("Expected error for nil filter")
		}

		// Test FindOne with invalid filter
		result := coll.FindOne(context.Background(), nil)
		if result != nil && result.Err() == nil {
			t.Error("Expected error for nil filter")
		}

		// Test DeleteOne with invalid filter
		_, err = coll.DeleteOne(context.Background(), nil)
		if err == nil {
			t.Error("Expected error for nil filter")
		}

		// Test DeleteMany with invalid filter
		_, err = coll.DeleteMany(context.Background(), nil)
		if err == nil {
			t.Error("Expected error for nil filter")
		}

		// Test FindOneAndUpdate with invalid filter
		result = coll.FindOneAndUpdate(context.Background(), nil, bson.M{"$set": bson.M{"field": "value"}})
		if result != nil && result.Err() == nil {
			t.Error("Expected error for nil filter")
		}

		// Test FindOneAndUpdate with invalid update
		result = coll.FindOneAndUpdate(context.Background(), bson.M{"_id": "test"}, nil)
		if result != nil && result.Err() == nil {
			t.Error("Expected error for nil update")
		}

		// Test ReplaceOne with invalid filter
		_, err = coll.ReplaceOne(context.Background(), nil, bson.M{"field": "value"})
		if err == nil {
			t.Error("Expected error for nil filter")
		}

		// Test ReplaceOne with invalid replacement
		_, err = coll.ReplaceOne(context.Background(), bson.M{"_id": "test"}, nil)
		if err == nil {
			t.Error("Expected error for nil replacement")
		}

		// Test FindOneAndReplace with invalid filter
		result = coll.FindOneAndReplace(context.Background(), nil, bson.M{"field": "value"})
		if result != nil && result.Err() == nil {
			t.Error("Expected error for nil filter")
		}

		// Test FindOneAndReplace with invalid replacement
		result = coll.FindOneAndReplace(context.Background(), bson.M{"_id": "test"}, nil)
		if result != nil && result.Err() == nil {
			t.Error("Expected error for nil replacement")
		}

		// Test Distinct with invalid filter
		_, err = coll.Distinct(context.Background(), "field", nil)
		if err == nil {
			t.Error("Expected error for nil filter")
		}

		// Test CountDocuments with invalid filter
		_, err = coll.CountDocuments(context.Background(), nil)
		if err == nil {
			t.Error("Expected error for nil filter")
		}

		// Test Aggregate with invalid pipeline
		_, err = coll.Aggregate(context.Background(), nil)
		if err == nil {
			t.Error("Expected error for nil pipeline")
		}

		// Test Watch with invalid pipeline
		_, err = coll.Watch(context.Background(), nil)
		if err == nil {
			t.Error("Expected error for nil pipeline")
		}

		// Test Rename with empty new name
		err = coll.Rename(context.Background(), "")
		if err == nil {
			t.Error("Expected error for empty new name")
		}

		// Test FindToArray with invalid filter
		_, err = coll.FindToArray(context.Background(), nil)
		if err == nil {
			t.Error("Expected error for nil filter")
		}

		// Test InsertMany with empty documents
		_, err = coll.InsertMany(context.Background(), []interface{}{})
		if err == nil {
			t.Error("Expected error for empty documents")
		}

		// Test InsertMany with nil documents
		_, err = coll.InsertMany(context.Background(), nil)
		if err == nil {
			t.Error("Expected error for nil documents")
		}

		// Test BulkWrite with empty models
		_, err = coll.BulkWrite(context.Background(), []mongo.WriteModel{})
		if err == nil {
			t.Error("Expected error for empty models")
		}

		// Test BulkWrite with nil models
		_, err = coll.BulkWrite(context.Background(), nil)
		if err == nil {
			t.Error("Expected error for nil models")
		}
	})

	t.Run("Database operations with invalid parameters", func(t *testing.T) {
		db := Database("tmp")
		if db == nil {
			t.Fatal("Database should not be nil")
		}

		// Test CreateCollection with empty collection name
		err := db.CreateCollection(context.Background(), "", nil)
		if err == nil {
			t.Error("Expected error for empty collection name")
		}

		// Test ListCollectionNames with invalid filter
		_, err = db.ListCollectionNames(context.Background(), "invalid_filter")
		if err == nil {
			t.Error("Expected error for invalid filter")
		}
	})

	t.Run("Transaction operations with invalid parameters", func(t *testing.T) {
		session, err := StartSession("tmp")
		if err != nil {
			t.Fatalf("Failed to start session: %v", err)
		}
		defer session.EndSession()

		// Test StartTransaction with nil session
		var nilSession *MongoSession
		txn, err := nilSession.StartTransaction()
		if err == nil {
			t.Error("Expected error for nil session")
		}
		if txn != nil {
			t.Error("Transaction should be nil for nil session")
		}

		// Test WithTransaction with nil session
		err = nilSession.WithTransaction(func(sessCtx mongo.SessionContext) error {
			return nil
		})
		if err == nil {
			t.Error("Expected error for nil session")
		}

		// Test GetSessionContext with nil session
		ctx := nilSession.GetSessionContext()
		if ctx != nil {
			t.Error("Session context should be nil for nil session")
		}

		// Test transaction with nil session field
		txn = &MongoTransaction{
			session:   nil,
			ctx:       context.Background(),
			started:   true,
			committed: false,
			aborted:   false,
		}

		err = txn.CommitTransaction()
		if err == nil {
			t.Error("Expected error for transaction with nil session")
		}

		err = txn.AbortTransaction()
		if err == nil {
			t.Error("Expected error for transaction with nil session")
		}

		ctx = txn.GetSessionContext()
		if ctx != nil {
			t.Error("Session context should be nil for transaction with nil session")
		}
	})

	t.Run("Utility functions edge cases", func(t *testing.T) {
		// Test contains with various inputs
		slice := []string{"a", "b", "c"}
		if !contains(slice, "a") {
			t.Error("contains should return true for 'a'")
		}
		if contains(slice, "d") {
			t.Error("contains should return false for 'd'")
		}
		if contains(slice, "") {
			t.Error("contains should return false for empty string")
		}

		// Test with empty slice
		emptySlice := []string{}
		if contains(emptySlice, "a") {
			t.Error("contains should return false for empty slice")
		}

		// Test with nil slice
		var nilSlice []string
		if contains(nilSlice, "a") {
			t.Error("contains should return false for nil slice")
		}

		// Test applyOptions with various inputs
		opts := QueryOptions{Projection: bson.M{"field": 1}}
		result := applyOptions([]interface{}{opts})
		if result == nil {
			t.Error("applyOptions should return QueryOptions")
		}

		// Test with no QueryOptions
		result = applyOptions([]interface{}{"not_query_options"})
		if result != nil {
			t.Error("applyOptions should return nil for non-QueryOptions")
		}

		// Test with empty options
		result = applyOptions([]interface{}{})
		if result != nil {
			t.Error("applyOptions should return nil for empty options")
		}

		// Test with nil options
		result = applyOptions(nil)
		if result != nil {
			t.Error("applyOptions should return nil for nil options")
		}
	})

	t.Run("Collection metadata operations", func(t *testing.T) {
		coll := getCleanCollection(t)

		// Test collection name
		if coll.Name() != "goapp_test" {
			t.Errorf("Wrong collection name: got %s, want %s", coll.Name(), "goapp_test")
		}

		// Test database name
		if coll.DBName() != dbConfigs["tmp"].database {
			t.Errorf("Wrong database name: got %s, want %s", coll.DBName(), dbConfigs["tmp"].database)
		}

		// Test database reference
		db := coll.Database()
		if db == nil {
			t.Error("Database should not be nil")
		}

		// Test ping
		err := coll.Ping(context.Background())
		if err != nil {
			t.Errorf("Ping failed: %v", err)
		}

		// Test get client
		client := coll.GetClient()
		if client == nil {
			t.Error("Client should not be nil")
		}
	})

	t.Run("Database metadata operations", func(t *testing.T) {
		db := Database("tmp")
		if db == nil {
			t.Fatal("Database should not be nil")
		}

		// Test collection creation
		err := db.CreateCollection(context.Background(), "test_metadata", nil)
		if err != nil {
			t.Errorf("Failed to create collection: %v", err)
		}

		// Test list collections
		collections, err := db.ListCollectionNames(context.Background(), bson.M{})
		if err != nil {
			t.Errorf("Failed to list collections: %v", err)
		}
		if len(collections) == 0 {
			t.Error("Should have at least one collection")
		}

		// Cleanup
		testColl := Coll("tmp", "test_metadata")
		if testColl != nil {
			testColl.Drop(context.Background())
		}
	})

	t.Run("Complex transaction scenarios", func(t *testing.T) {
		session, err := StartSession("tmp")
		if err != nil {
			t.Fatalf("Failed to start session: %v", err)
		}
		defer session.EndSession()

		// Test transaction with options
		txnOpts := &TransactionOptions{
			ReadConcern:  readconcern.Snapshot(),
			WriteConcern: writeconcern.Majority(),
		}

		err = session.WithTransaction(func(sessCtx mongo.SessionContext) error {
			coll := Coll("tmp", "test_complex_txn")
			_, err := coll.InsertOne(sessCtx, bson.M{"test": "complex_transaction"})
			return err
		}, txnOpts)

		if err != nil {
			t.Fatalf("Complex transaction failed: %v", err)
		}

		// Verify the document was inserted
		coll := Coll("tmp", "test_complex_txn")
		var result bson.M
		err = coll.FindOne(context.Background(), bson.M{"test": "complex_transaction"}).Decode(&result)
		if err != nil {
			t.Fatalf("Failed to find inserted document: %v", err)
		}
		if result["test"] != "complex_transaction" {
			t.Errorf("Wrong value: got %v, want %v", result["test"], "complex_transaction")
		}

		// Cleanup
		coll.Drop(context.Background())
	})

	t.Run("Error propagation and handling", func(t *testing.T) {
		// Test with non-existent database
		nonExistentDb := Database("non_existent")
		if nonExistentDb != nil {
			t.Error("Database should be nil for non-existent database")
		}

		// Test with non-existent collection
		nonExistentColl := Coll("tmp", "non_existent")
		if nonExistentColl == nil {
			t.Error("Collection should not be nil even if it doesn't exist")
		}

		// Test operations on non-existent collection
		err := nonExistentColl.FindOne(context.Background(), bson.M{"_id": "test"}).Decode(bson.M{})
		if err == nil {
			t.Error("Expected error for non-existent document")
		}

		// Test with invalid database configuration
		invalidColl := Coll("invalid_db", "test")
		if invalidColl != nil {
			t.Error("Collection should be nil for invalid database")
		}
	})

	t.Run("ListCollections edge cases", func(t *testing.T) {
		// Test with non-existent database
		_, err := ListCollections("non_existent")
		if err == nil {
			t.Error("Expected error for non-existent database")
		}

		// Test with empty database name
		_, err = ListCollections("")
		if err == nil {
			t.Error("Expected error for empty database name")
		}
	})

	t.Run("PreDefColl edge cases", func(t *testing.T) {
		// Test with non-existent predefined collection
		coll := PreDefColl("non_existent_predef")
		if coll != nil {
			t.Error("PreDefColl should return nil for non-existent collection")
		}

		// Test with empty name
		coll = PreDefColl("")
		if coll != nil {
			t.Error("PreDefColl should return nil for empty name")
		}
	})

	t.Run("Database edge cases", func(t *testing.T) {
		// Test with non-existent database
		db := Database("non_existent_db")
		if db != nil {
			t.Error("Database should return nil for non-existent database")
		}

		// Test with empty database name
		db = Database("")
		if db != nil {
			t.Error("Database should return nil for empty database name")
		}
	})

	t.Run("Coll edge cases", func(t *testing.T) {
		// Test with non-existent database
		coll := Coll("non_existent_db", "test")
		if coll != nil {
			t.Error("Coll should return nil for non-existent database")
		}

		// Test with empty database name
		coll = Coll("", "test")
		if coll != nil {
			t.Error("Coll should return nil for empty database name")
		}

		// Test with empty collection name
		coll = Coll("tmp", "")
		if coll != nil {
			t.Error("Coll should return nil for empty collection name")
		}
	})

	t.Run("Database Coll method", func(t *testing.T) {
		db := Database("tmp")
		if db == nil {
			t.Fatal("Database should not be nil")
		}

		// Test mongoDatabase.Coll method
		coll := db.Coll("test_coll")
		if coll == nil {
			t.Error("Collection should not be nil")
		}

		// Test with nil database
		var nilDb *mongoDatabase
		nilColl := nilDb.Coll("test")
		if nilColl != nil {
			t.Error("Collection should be nil for nil database")
		}
	})

	t.Run("FindOne with QueryOptions", func(t *testing.T) {
		coll := getCleanCollection(t)

		// Insert test document
		_, err := coll.InsertOne(context.Background(), bson.M{"_id": "test_findone", "field": "value"})
		if err != nil {
			t.Fatalf("Failed to insert test document: %v", err)
		}

		// Test FindOne with QueryOptions
		opts := QueryOptions{
			Projection: bson.M{"field": 1},
			Sort:       bson.M{"_id": 1},
			Skip:       0,
		}

		result := coll.FindOne(context.Background(), bson.M{"_id": "test_findone"}, opts)
		if result.Err() != nil {
			t.Fatalf("FindOne failed: %v", result.Err())
		}

		var doc bson.M
		err = result.Decode(&doc)
		if err != nil {
			t.Fatalf("Failed to decode result: %v", err)
		}

		if doc["field"] != "value" {
			t.Errorf("Expected field to be 'value', got %v", doc["field"])
		}

		// Test FindOne with Skip > 0
		optsWithSkip := QueryOptions{
			Projection: bson.M{"field": 1},
			Sort:       bson.M{"_id": 1},
			Skip:       1,
		}

		resultWithSkip := coll.FindOne(context.Background(), bson.M{"_id": "test_findone"}, optsWithSkip)
		if resultWithSkip.Err() == nil {
			t.Error("Expected error when skipping all documents")
		}
	})

	t.Run("Database operations with error handling", func(t *testing.T) {
		db := Database("tmp")
		if db == nil {
			t.Fatal("Database should not be nil")
		}

		// Test ListCollectionNames with invalid filter
		_, err := db.ListCollectionNames(context.Background(), "invalid_filter")
		if err == nil {
			t.Error("Expected error for invalid filter")
		}

		// Test CreateCollection with empty name
		err = db.CreateCollection(context.Background(), "", nil)
		if err == nil {
			t.Error("Expected error for empty collection name")
		}

		// Test CreateCollection with nil database
		var nilDb *mongoDatabase
		err = nilDb.CreateCollection(context.Background(), "test", nil)
		if err == nil {
			t.Error("Expected error for nil database")
		}

		// Test ListCollectionNames with nil database
		_, err = nilDb.ListCollectionNames(context.Background(), bson.M{})
		if err == nil {
			t.Error("Expected error for nil database")
		}
	})

	t.Run("Collection operations with nil collection", func(t *testing.T) {
		var nilColl *MongoCollection

		// Test all operations with nil collection
		_, err := nilColl.InsertOne(context.Background(), bson.M{"test": "value"})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.UpdateOne(context.Background(), bson.M{"_id": "test"}, bson.M{"$set": bson.M{"field": "value"}})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.UpdateMany(context.Background(), bson.M{"field": "value"}, bson.M{"$set": bson.M{"field": "value"}})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.Find(context.Background(), bson.M{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		result := nilColl.FindOne(context.Background(), bson.M{})
		if result != nil {
			t.Error("Expected nil result for nil collection")
		}

		_, err = nilColl.DeleteOne(context.Background(), bson.M{"_id": "test"})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.DeleteMany(context.Background(), bson.M{"field": "value"})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		result = nilColl.FindOneAndUpdate(context.Background(), bson.M{"_id": "test"}, bson.M{"$set": bson.M{"field": "value"}})
		if result != nil {
			t.Error("Expected nil result for nil collection")
		}

		_, err = nilColl.ReplaceOne(context.Background(), bson.M{"_id": "test"}, bson.M{"field": "value"})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		result = nilColl.FindOneAndReplace(context.Background(), bson.M{"_id": "test"}, bson.M{"field": "value"})
		if result != nil {
			t.Error("Expected nil result for nil collection")
		}

		_, err = nilColl.Distinct(context.Background(), "field", bson.M{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.CountDocuments(context.Background(), bson.M{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.EstimatedDocumentCount(context.Background())
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.CreateIndex(context.Background(), mongo.IndexModel{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.CreateIndexes(context.Background(), []mongo.IndexModel{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		err = nilColl.DropIndex(context.Background(), "index_name")
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.Aggregate(context.Background(), []bson.M{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.Watch(context.Background(), []bson.M{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		err = nilColl.Rename(context.Background(), "new_name")
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.FindToArray(context.Background(), bson.M{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.InsertMany(context.Background(), []interface{}{bson.M{"test": "value"}})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		err = nilColl.Drop(context.Background())
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		_, err = nilColl.BulkWrite(context.Background(), []mongo.WriteModel{})
		if err == nil {
			t.Error("Expected error for nil collection")
		}

		// Test collection properties with nil collection
		if nilColl.Name() != "" {
			t.Error("Name should return empty string for nil collection")
		}

		if nilColl.DBName() != "" {
			t.Error("DBName should return empty string for nil collection")
		}

		if nilColl.Database() != nil {
			t.Error("Database should return nil for nil collection")
		}

		if nilColl.Ping(context.Background()) == nil {
			t.Error("Ping should return error for nil collection")
		}

		if nilColl.GetClient() != nil {
			t.Error("GetClient should return nil for nil collection")
		}
	})

	t.Run("FindOneAndUpdate with noModifyMt", func(t *testing.T) {
		coll := getCleanCollection(t)

		// Insert test document
		_, err := coll.InsertOne(context.Background(), bson.M{"_id": "test_findoneandupdate", "field": "initial"})
		if err != nil {
			t.Fatalf("Failed to insert test document: %v", err)
		}

		// Test FindOneAndUpdate with noModifyMt = true
		update := bson.M{
			"noModifyMt": true,
			"$set": bson.M{
				"field": "updated",
			},
		}

		result := coll.FindOneAndUpdate(context.Background(), bson.M{"_id": "test_findoneandupdate"}, update)
		if result.Err() != nil {
			t.Fatalf("FindOneAndUpdate failed: %v", result.Err())
		}

		var doc bson.M
		err = result.Decode(&doc)
		if err != nil {
			t.Fatalf("Failed to decode result: %v", err)
		}

		if doc["field"] != "initial" {
			t.Errorf("Expected field to be 'initial' (before update), got %v", doc["field"])
		}

		// Verify the document was actually updated
		var updatedDoc bson.M
		err = coll.FindOne(context.Background(), bson.M{"_id": "test_findoneandupdate"}).Decode(&updatedDoc)
		if err != nil {
			t.Fatalf("Failed to find updated document: %v", err)
		}

		if updatedDoc["field"] != "updated" {
			t.Errorf("Expected field to be 'updated', got %v", updatedDoc["field"])
		}
	})

	t.Run("FindOneAndUpdate with invalid noModifyMt type", func(t *testing.T) {
		coll := getCleanCollection(t)

		// Insert test document
		_, err := coll.InsertOne(context.Background(), bson.M{"_id": "test_invalid_noModifyMt", "field": "initial"})
		if err != nil {
			t.Fatalf("Failed to insert test document: %v", err)
		}

		// Test FindOneAndUpdate with invalid noModifyMt type
		update := bson.M{
			"noModifyMt": "not_a_boolean", // Invalid type
			"$set": bson.M{
				"field": "updated",
			},
		}

		result := coll.FindOneAndUpdate(context.Background(), bson.M{"_id": "test_invalid_noModifyMt"}, update)
		if result.Err() != nil {
			t.Fatalf("FindOneAndUpdate failed: %v", result.Err())
		}

		// Should still work, just log an error
		var doc bson.M
		err = result.Decode(&doc)
		if err != nil {
			t.Fatalf("Failed to decode result: %v", err)
		}
	})

	t.Run("Collection operations with context timeout", func(t *testing.T) {
		coll := getCleanCollection(t)

		// Create context with very short timeout
		ctx, cancel := context.WithTimeout(context.Background(), 1*time.Millisecond)
		defer cancel()

		// Wait for context to timeout
		time.Sleep(10 * time.Millisecond)

		// Test operations with expired context
		_, err := coll.InsertOne(ctx, bson.M{"test": "value"})
		if err == nil {
			t.Error("Expected error for expired context")
		}

		_, err = coll.UpdateOne(ctx, bson.M{"_id": "test"}, bson.M{"$set": bson.M{"field": "value"}})
		if err == nil {
			t.Error("Expected error for expired context")
		}

		_, err = coll.UpdateMany(ctx, bson.M{"field": "value"}, bson.M{"$set": bson.M{"field": "value"}})
		if err == nil {
			t.Error("Expected error for expired context")
		}

		_, err = coll.Find(ctx, bson.M{})
		if err == nil {
			t.Error("Expected error for expired context")
		}

		result := coll.FindOne(ctx, bson.M{})
		if result.Err() == nil {
			t.Error("Expected error for expired context")
		}

		_, err = coll.DeleteOne(ctx, bson.M{"_id": "test"})
		if err == nil {
			t.Error("Expected error for expired context")
		}

		_, err = coll.DeleteMany(ctx, bson.M{"field": "value"})
		if err == nil {
			t.Error("Expected error for expired context")
		}

		result = coll.FindOneAndUpdate(ctx, bson.M{"_id": "test"}, bson.M{"$set": bson.M{"field": "value"}})
		if result.Err() == nil {
			t.Error("Expected error for expired context")
		}

		_, err = coll.ReplaceOne(ctx, bson.M{"_id": "test"}, bson.M{"field": "value"})
		if err == nil {
			t.Error("Expected error for expired context")
		}

		result = coll.FindOneAndReplace(ctx, bson.M{"_id": "test"}, bson.M{"field": "value"})
		if result.Err() == nil {
			t.Error("Expected error for expired context")
		}

		_, err = coll.Distinct(ctx, "field", bson.M{})
		if err == nil {
			t.Error("Expected error for expired context")
		}

		_, err = coll.CountDocuments(ctx, bson.M{})
		if err == nil {
			t.Error("Expected error for expired context")
		}

		_, err = coll.EstimatedDocumentCount(ctx)
		if err == nil {
			t.Error("Expected error for expired context")
		}

		_, err = coll.CreateIndex(ctx, mongo.IndexModel{})
		if err == nil {
			t.Error("Expected error for expired context")
		}

		_, err = coll.CreateIndexes(ctx, []mongo.IndexModel{})
		if err == nil {
			t.Error("Expected error for expired context")
		}

		err = coll.DropIndex(ctx, "index_name")
		if err == nil {
			t.Error("Expected error for expired context")
		}

		_, err = coll.Aggregate(ctx, []bson.M{})
		if err == nil {
			t.Error("Expected error for expired context")
		}

		_, err = coll.Watch(ctx, []bson.M{})
		if err == nil {
			t.Error("Expected error for expired context")
		}

		err = coll.Rename(ctx, "new_name")
		if err == nil {
			t.Error("Expected error for expired context")
		}

		_, err = coll.FindToArray(ctx, bson.M{})
		if err == nil {
			t.Error("Expected error for expired context")
		}

		_, err = coll.InsertMany(ctx, []interface{}{bson.M{"test": "value"}})
		if err == nil {
			t.Error("Expected error for expired context")
		}

		err = coll.Drop(ctx)
		if err == nil {
			t.Error("Expected error for expired context")
		}

		_, err = coll.BulkWrite(ctx, []mongo.WriteModel{})
		if err == nil {
			t.Error("Expected error for expired context")
		}
	})

	t.Run("AddTimestamps edge cases", func(t *testing.T) {
		// Not a bson.M, but a struct
		type S struct {
			X int `bson:"x"`
		}
		m, err := addTimestamps(S{X: 1}, false, false)
		if err != nil {
			t.Errorf("addTimestamps struct failed: %v", err)
		}
		bm, ok := m.(bson.M)
		if !ok || bm["x"] != int32(1) {
			t.Error("addTimestamps did not convert struct correctly")
		}

		// Update mode, with $set
		update := bson.M{"$set": bson.M{"a": 1}}
		m, err = addTimestamps(update, true, false)
		if err != nil {
			t.Errorf("addTimestamps update failed: %v", err)
		}
		um, ok := m.(bson.M)
		if !ok || um["$setOnInsert"] == nil || um["$set"] == nil {
			t.Error("addTimestamps update did not add $setOnInsert/$set")
		}

		// Update mode, with noModifyMt true
		update = bson.M{"$set": bson.M{"a": 1}}
		m, err = addTimestamps(update, true, true)
		if err != nil {
			t.Errorf("addTimestamps update failed: %v", err)
		}
		um, ok = m.(bson.M)
		if !ok || um["$setOnInsert"] == nil {
			t.Error("addTimestamps update did not add $setOnInsert")
		}

		// Test with non-operator fields in update
		update = bson.M{"field1": "value1", "field2": "value2"}
		m, err = addTimestamps(update, true, false)
		if err != nil {
			t.Errorf("addTimestamps update with non-operator fields failed: %v", err)
		}
		um, ok = m.(bson.M)
		if !ok || um["$setOnInsert"] == nil || um["$set"] == nil {
			t.Error("addTimestamps update did not wrap non-operator fields in $set")
		}

		// Test with mixed operators and fields
		update = bson.M{"$set": bson.M{"a": 1}, "field1": "value1", "$inc": bson.M{"counter": 1}}
		m, err = addTimestamps(update, true, false)
		if err != nil {
			t.Errorf("addTimestamps update with mixed operators failed: %v", err)
		}
		um, ok = m.(bson.M)
		if !ok || um["$setOnInsert"] == nil || um["$set"] == nil || um["$inc"] == nil {
			t.Error("addTimestamps update did not handle mixed operators correctly")
		}
	})

	t.Run("extractMetadata edge cases", func(t *testing.T) {
		// With metadata
		m := bson.M{"noModifyMt": true, "foo": 1}
		clean, meta := extractMetadata(m)
		if meta["noModifyMt"] != true {
			t.Error("extractMetadata did not extract metadata")
		}
		cm, ok := clean.(bson.M)
		if !ok || cm["foo"] != 1 {
			t.Error("extractMetadata did not clean metadata fields")
		}
		// Without metadata
		m = bson.M{"foo": 2}
		clean, meta = extractMetadata(m)
		if len(meta) != 0 {
			t.Error("extractMetadata should return empty metadata if none present")
		}
		cm, ok = clean.(bson.M)
		if !ok || cm["foo"] != 2 {
			t.Error("extractMetadata did not return original map if no metadata")
		}

		// Test with non-bson.M input
		nonMap := "not a map"
		clean, meta = extractMetadata(nonMap)
		if clean != nonMap || len(meta) != 0 {
			t.Error("extractMetadata should return original value for non-map input")
		}
	})

	t.Run("logSlowOperation edge cases", func(t *testing.T) {
		// Should log warning if duration > 2s
		logSlowOperation(3*time.Second, "TestOp", "TestColl", "arg1", 123)
		// Should log debug if verboseLevel > 2
		oldVerbose := verboseLevel
		verboseLevel = 3
		logSlowOperation(1*time.Second, "TestOp", "TestColl", "arg2", 456)
		verboseLevel = oldVerbose
	})

	t.Run("mongoLogSink edge cases", func(t *testing.T) {
		sink := &mongoLogSink{}
		sink.Info(2, "info message", "k", "v")
		sink.Error(fmt.Errorf("err"), "error message", "k", "v")
	})
}
